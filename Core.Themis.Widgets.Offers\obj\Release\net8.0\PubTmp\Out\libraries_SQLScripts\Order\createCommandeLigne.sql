INSERT INTO commande_ligne
(commande_ligne_id
,commande_id
,manifestation_id
,seance_id
,dossier_id
,groupe_id
,type_ligne
,formule_id
,abo_id
,identite_id
,contrainte
,num_acompte)
VALUES
(@pcommandeLigneId,
@porderId,
@peventId,
@psessionId,
@pdossierId,
@pgroupeId,
@ptypeLigne,
@pformulaId,
@paboId,
@pidentiteId,
@pcontrainte,
@pnumacompte);

UPDATE Commande_Ligne_comp SET
Nombre=@pnombre
,Montant1=@pmontant1/100
,Montant2=@pmontant2/100
,Montant3=@pmontant3/100
,Montant4=@pmontant4/100
,Montant5=@pmontant5/100
,Montant6=@pmontant6/100
,Montant7=@pmontant7/100
,Montant8=@pmontant8/100
,Montant9=@pmontant9/100
,Montant10=@pmontant10/100
,filiere_id = @pfiliereId
,fancard =@pfancard
,dossier_client_nom=@pdossierclientnom
,dossier_numero = @pdossiernum
,dossier_c = @pdossierC
,dossier_icone = @pdossierIcone
,operateur_id = @poperateurId
WHERE commande_ligne_id = @pcommandeLigneId