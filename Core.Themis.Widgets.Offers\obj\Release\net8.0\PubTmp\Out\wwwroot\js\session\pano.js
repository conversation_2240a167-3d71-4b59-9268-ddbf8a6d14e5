﻿var panofov;
//intialisation pano
function krpano() {
    return document.getElementById("krpanoSWFObject");
}
// met a jour le style et les dispos des zones
// appelée une fois le chargement du pano fini (cf pano.xml)
function UpdateStyleZoneInPano() {
    xmlUrl = krpano().get("xml.url");
    $.ajax({
        url: xmlUrl,
        dataType: 'xml',
        async: false,
        success: function (response, status, xh) {
            $xml = $(response);
        }
    });

    panofov = $xml.find('view').attr('fov')
    if ($xml.find('view').attr('fov') == undefined) {
        panofov = krpano().get("view.fov")
    }

    listHotspots = $xml.find('hotspot');
    listHotspots.each(function (e, index) {
        thisid = $(index).attr('thisid');
        if (thisid == undefined) {
            thisidn = $(index).attr('name');
            thisid = thisidn.substr(1)

        }
        //on récupère l'objet complet jusqu'a la section
        var objUntilZone = getSessionObjectFromAreaId(thisSessionLoadedPlan, "zone", thisid)
        var thisZone = objUntilZone.session.listZones

        var dispo = 0;
        if (thisZone != null) {
            dispo = thisZone.dispo;
        }

        if (dispo <= 0) {
            krpano().call("hotspot[z" + thisid + "].loadstyle('nodispo');")
            krpano().set("hotspot[z" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible') );
            krpano().set("texts" + thisid, "unavailable");
        }
        else {
            krpano().call("hotspot[z" + thisid + "].loadstyle('enabled');")
            krpano().set("hotspot[z" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replaceAll("{SeatsNumber}", dispo));
        }
    });
}

// appellée sur la click du pano (voir xml)
function ZoneClick(zoneId) {
    //supprime le plein écran 
    if (krpano().get('fullscreen')) {
        krpano().set('fullscreen', false);
    }
    //récupère l'objet jusqu'a la categ désirée
    var objUntilCateg = getSessionObjectFromAreaId(thisSessionLoadedPlan, "zone", zoneId)
    var thisZone = objUntilCateg.session.listZones
    var thisCateg = objUntilCateg.session.listZones.listFloors.listSections.listCategories

    if (thisZone.dispo > 0) {
        var lstCategs = []
        $.each(thisSessionLoadedPlan.categories, function (i, k) {
            lstCategs.push(k.categId)
        })
        loadingBootstrapOn('#panoInner')
        refreshChoiceByPlan(objUntilCateg.session, [thisZone.zoneId], [0], [0], lstCategs, $('#panoInner').attr('data-isuniquesession'), true)
    }
}
// met a jour le style et les dispos des étages
// appelée une fois le chargement du pano fini (cf pano.xml)
function UpdateStyleEtageInPano() {
    xmlUrl = krpano().get("xml.url");
    $.ajax({
        url: xmlUrl,
        dataType: 'xml',
        async: false,
        success: function (response, status, xh) {
            $xml = $(response);
        }
    });

    panofov = $xml.find('view').attr('fov')
    if ($xml.find('view').attr('fov') == undefined) {
        panofov = krpano().get("view.fov")
    }

    listHotspots = $xml.find('hotspot');
    listHotspots.each(function (e, index) {
        thisid = $(index).attr('thisid');
        if (thisid == undefined) {
            thisidn = $(index).attr('name');
            thisid = thisidn.substr(1)

        }
        //on récupère l'objet complet jusqu'a la section
        var objUntilFloors = getSessionObjectFromAreaId(thisSessionLoadedPlan, "floor", thisid)
        var thisFloor = objUntilFloors.session.listZones.listFloors

        var dispo = 0;
        var isSurPlan = false
        if (thisFloor != null) {
            dispo = thisFloor.dispo;
            $.each(thisFloor.listSections, function (lsi, lsk) {
                $.each(lsk.listCategories, function (lci, lck) {
                    $.each(lck.listGestionPlace, function (lgpi, lgpk) {
                        if (lgpk.isSurPlan) {
                            isSurPlan = true
                        }
                    })
                })
            })

           
        }

        if (dispo <= 0 || (!isSurPlan && !SettingsMerge.pano.redirectIfOnlyAuto)) {
            krpano().call("hotspot[e" + thisid + "].loadstyle('nodispo');")
            krpano().set("hotspot[e" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible'));
            krpano().set("texte" + thisid, "unavailable");
        }
        else {
            krpano().call("hotspot[e" + thisid + "].loadstyle('enabled');")
            krpano().set("hotspot[e" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replaceAll("{SeatsNumber}", dispo));
        }
    });
}
// met a jour le style et les dispos des étages
// appelée une fois le chargement du pano fini (cf pano.xml)
function EtageClick(floorId) {
    //supprime le plein écran
    /*if (krpano().get('fullscreen')) {
        krpano().set('fullscreen', false);
    }
    //récupère l'objet jusqu'a l'étage désiré
    var objUntilFloors = getSessionObjectFromAreaId(thisSessionLoadedPlan, "floor", floorId)
    var thisZone = objUntilFloors.session.listZones
    var thisFloor = objUntilFloors.session.listZones.listFloors

    if (thisFloor.dispo > 0) {
        var lstCategs = []
        $.each(thisSessionLoadedPlan.categories, function (i, k) {
            lstCategs.push(k.categId)
        })
        loadingBootstrapOn('#panoInner')
        refreshChoiceByPlan(objUntilFloors.session, [thisZone.zoneId], [thisFloor.floorId], [0], lstCategs, $('#panoInner').attr('data-isuniquesession'), true)
    }*/
    //supprime le plein écran 
    if (krpano().get('fullscreen')) {
        krpano().set('fullscreen', false);
    }
    //récupère l'objet jusqu'a la section désirée
    var objUntilFloors = getSessionObjectFromAreaId(thisSessionLoadedPlan, "floor", floorId)
    var thisZone = objUntilFloors.session.listZones
    var thisFloor = objUntilFloors.session.listZones.listFloors
    var isSurPlan = false
    var isAuto = false
    if (thisFloor != null) {
        dispo = thisFloor.dispo;
        $.each(thisFloor.listSections, function (lsi, lsk) {
            $.each(lsk.listCategories, function (lci, lck) {
                $.each(lck.listGestionPlace, function (lgpi, lgpk) {
                    if (lgpk.isSurPlan) {
                        isSurPlan = true
                    }
                    if (!lgpk.isSurPlan) {
                        isAuto = true
                    }
                })
            })
        })
    }
    if (thisFloor.dispo > 0 && isSurPlan) {
        //s'il y a des dispos sur plan
        var lstCategs = []
        $.each(thisSessionLoadedPlan.categories, function (i, k) {
            lstCategs.push(k.categId)
        })
        loadingBootstrapOn('#panoInner')
        refreshChoiceByPlan(objUntilSection.session, [thisZone.zoneId], [thisFloor.floorId], [0], lstCategs, $('#panoInner').attr('data-isuniquesession'), true)
    } else if (thisFloor.dispo > 0 && isAuto && SettingsMerge.pano.redirectIfOnlyAuto) {
        //s'il y a des dispos, seulement en auto, et que l'option de redirection vers la grille (redirectIfOnlyAuto) est activé
        console.log("cette section a des dispos qu'en auto")
        var uniqueSession = false;
        if (sessionsData.length == 1 && JSON.parse(sessionsData[0].value).length == 1) {
            uniqueSession = true;
        }
        if (SettingsMerge.pano.showMsgWhenRedirectIfOnlyAuto) {
            $('#modalNoDsipoOnPlanButDispoOnAuto').modal('show')
            $('#BtnModalNoDsipoOnPlanButDispoOnAutoContinue').off('click').on('click', function () {
                $('#modalNoDsipoOnPlanButDispoOnAuto').modal('hide')
                $('#wdgInsertGrilleTarif').html("")
                $("#auto-tab").tab('show')
                if ($('#panoInner').length > 0) {
                    $('#wdgInsertSeatPlan').html("")
                    loadingBootstrapOn('#wdgInsertSeatPlan')
                }
                refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, thisZone.zoneId, thisFloor.floorId, 0);
            })
        } else {
            $('#wdgInsertGrilleTarif').html("")
            $("#auto-tab").tab('show')

            if ($('#panoInner').length > 0) {
                $('#wdgInsertSeatPlan').html("")
                loadingBootstrapOn('#wdgInsertSeatPlan')
            }
            refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, thisZone.zoneId, thisFloor.floorId, 0);
        }
    }
}
// met a jour le style et les dispos des sections
// appelée une fois le chargement du pano fini (cf pano.xml)
function UpdateStyleSectionInPano() {
    xmlUrl = krpano().get("xml.url");    
    $.ajax({
        url: xmlUrl,
        dataType: 'xml',
        async: false,
        success: function (response, status, xh) {
            $xml = $(response);
        }
    });

    panofov = $xml.find('view').attr('fov')
    if ($xml.find('view').attr('fov') == undefined) {
        panofov = krpano().get("view.fov")
    }

    listHotspots = $xml.find('hotspot');
    listHotspots.each(function (e, index) {
        thisid = $(index).attr('thisid');
        if (thisid == undefined) {
            thisidn = $(index).attr('name');
            thisid = thisidn.substr(1)

        }
        //on récupère l'objet complet jusqu'a la section
        var objUntilSection = getSessionObjectFromAreaId(thisSessionLoadedPlan, "section", thisid)
        var thisSection = objUntilSection.session.listZones.listFloors.listSections

        var dispo = 0;
        var isSurPlan = false
        if (thisSection != null) {
            dispo = thisSection.dispo;
            $.each(thisSection.listCategories, function (lci, lck) {
                $.each(lck.listGestionPlace, function (lgpi, lgpk) {
                    if (lgpk.isSurPlan) {
                        isSurPlan = true
                    }
                })
            })
        }
         //s'il y a 0 dispo ou que l'option de redirection vers la grille (redirectIfOnlyAuto) est desactivé + aucune place sur plan
        if (dispo <= 0 || (!isSurPlan && !SettingsMerge.pano.redirectIfOnlyAuto) ) {
            krpano().call("hotspot[s" + thisid + "].loadstyle('nodispo');")
            krpano().set("hotspot[s" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible'));
            krpano().set("texts" + thisid, "unavailable");
        }
        else {
            krpano().call("hotspot[s" + thisid + "].loadstyle('enabled');")
            krpano().set("hotspot[s" + thisid + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replaceAll("{SeatsNumber}", dispo));
        }
    });
}

// appelé depuis le pano (click sur un hotspot cf panoXX.xml)
function SectionClick(sectionId)
{
    //supprime le plein écran 
    if (krpano().get('fullscreen')) {
        krpano().set('fullscreen', false);
    }
    //récupère l'objet jusqu'a la section désirée
    var objUntilSection = getSessionObjectFromAreaId(thisSessionLoadedPlan, "section", sectionId)
    var thisZone = objUntilSection.session.listZones
    var thisFloor = objUntilSection.session.listZones.listFloors
    var thisSection = objUntilSection.session.listZones.listFloors.listSections
    var isSurPlan = false
    var isAuto = false
    if (thisSection != null) {
        dispo = thisSection.dispo;
        $.each(thisSection.listCategories, function (lci, lck) {
            $.each(lck.listGestionPlace, function (lgpi, lgpk) {
                if (lgpk.isSurPlan) {
                    isSurPlan = true
                }
                if (!lgpk.isSurPlan) {
                    isAuto = true
                }
            })
        })
    }
    if (thisSection.dispo > 0 && isSurPlan) {
        //s'il y a des dispos sur plan
        var lstCategs = []
        $.each(thisSessionLoadedPlan.categories, function (i, k) {
            lstCategs.push(k.categId)
        })
        loadingBootstrapOn('#panoInner')
        refreshChoiceByPlan(objUntilSection.session, [thisZone.zoneId], [thisFloor.floorId], [thisSection.sectionId], lstCategs, $('#panoInner').attr('data-isuniquesession'), true)
    } else if (thisSection.dispo > 0 && isAuto && SettingsMerge.pano.redirectIfOnlyAuto) {
        //s'il y a des dispos, seulement en auto, et que l'option de redirection vers la grille (redirectIfOnlyAuto) est activé
        console.log("cette section a des dispos qu'en auto")
        var uniqueSession = false;
        if (sessionsData.length == 1 && JSON.parse(sessionsData[0].value).length == 1) {
            uniqueSession = true;
        }
        if (SettingsMerge.pano.showMsgWhenRedirectIfOnlyAuto) {
            $('#modalNoDsipoOnPlanButDispoOnAuto').modal('show')
            $('#BtnModalNoDsipoOnPlanButDispoOnAutoContinue').off('click').on('click', function () {
                $('#modalNoDsipoOnPlanButDispoOnAuto').modal('hide')
                $('#wdgInsertGrilleTarif').html("")
                $("#auto-tab").tab('show')
                if ($('#panoInner').length > 0) {
                    $('#wdgInsertSeatPlan').html("")
                    loadingBootstrapOn('#wdgInsertSeatPlan')
                }
                refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, thisZone.zoneId, thisFloor.floorId, thisSection.sectionId);
            })
        } else {
            $('#wdgInsertGrilleTarif').html("")
            $("#auto-tab").tab('show')

            if ($('#panoInner').length > 0) {
                $('#wdgInsertSeatPlan').html("")
                loadingBootstrapOn('#wdgInsertSeatPlan')
            }
            refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, thisZone.zoneId, thisFloor.floorId, thisSection.sectionId);
        }
    }
}

// met a jour le style et les dispos des catégories
// appelée une fois le chargement du pano fini (cf pano.xml)
function UpdateStyleCategInPano() {
    xmlUrl = krpano().get("xml.url");
    $.ajax({
        url: xmlUrl,
        dataType: 'xml',
        async: false,
        success: function (response, status, xh) {
            $xml = $(response);
        }
    });

    panofov = $xml.find('view').attr('fov')
    if ($xml.find('view').attr('fov') == undefined) {
        panofov = krpano().get("view.fov")
    }

    listHotspots = $xml.find('hotspot');
    listHotspots.each(function (e, index) {
        thisid = $(index).attr('thisid');
        thisidn = $(index).attr('name');
        if (thisid == undefined) {
            thisid = parseInt($(index).attr('data-categid'))

        }
        var dispo = 0;
        var restulFound = findNestedObj(thisSessionLoadedPlan.session, "categId", parseInt(thisid));
        //console.log(findNestedObj(thisSessionLoadedPlan.session, "categId", parseInt(thisid)))
        var isSurPlan = false
        $.each(restulFound, function (i, k) {
            
            $.each(k.listGestionPlace, function (gpi, gpk) {
                if (gpk.isSurPlan) {
                    isSurPlan = true
                }
            })
            //if (isSurPlan) {
                dispo = dispo + parseInt(k.dispo)
            //}
        })
        if (dispo <= 0 || (!isSurPlan && !SettingsMerge.pano.redirectIfOnlyAuto)) {
            krpano().call("hotspot[" + thisidn + "].loadstyle('nodispo');")
            krpano().set("hotspot[" + thisidn + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible'));
            krpano().set("text" + thisidn, "unavailable");
        }
        else {
            krpano().call("hotspot[" + thisidn + "].loadstyle('enabled');")
            krpano().set("hotspot[" + thisidn + "].thistext", "<br/>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replaceAll("{SeatsNumber}", dispo));
        }
    });
}

// appellée sur la click du pano (voir xml)
function CategClick(categId) {
    //supprime le plein écran 
    if (krpano().get('fullscreen')) {
        krpano().set('fullscreen', false);
    }
    //on parcours l'objet jusqu'a la categorie désirée, et pour chaque zone/étage/section ou elle se trouve, on stock les zoneids floorids et sectionids dans des tableaux
    var obj = getZESCIdsFromAreaId(thisSessionLoadedPlan.session, "categ", parseInt(categId))
    var zoneIds = obj.zones
    var floorIds = obj.floors
    var sectionsIds = obj.sections
 
    var dispo = 0
    var restulFound = findNestedObj(thisSessionLoadedPlan.session, "categId", parseInt(categId));
    //console.log(findNestedObj(thisSessionLoadedPlan.session, "categId", parseInt(categId)))
    /*$.each(restulFound, function (i, k) {
        dispo = dispo + parseInt(k.dispo)
    })*/
    var isAuto = false
    var isSurPlan = false
    $.each(restulFound, function (i, k) {
       
        $.each(k.listGestionPlace, function (gpi, gpk) {
            if (gpk.isSurPlan) {
                isSurPlan = true
            }
            if (!gpk.isSurPlan) {
                isAuto = true
            }
        })
        //if (isSurPlan) {
            dispo = dispo + parseInt(k.dispo)
        //}
    })
    if (dispo > 0 && isSurPlan) {
        //s'il y a des dispos sur plan
        var lstCategs = []
        lstCategs.push(parseInt(categId))
        loadingBootstrapOn('#panoInner')
        refreshChoiceByPlan(thisSessionLoadedPlan.session, zoneIds, floorIds, sectionsIds, lstCategs, $('#panoInner').attr('data-isuniquesession'), true)
    } else if (dispo > 0 && isAuto && SettingsMerge.pano.redirectIfOnlyAuto) {
        //s'il y a des dispos, seulement en auto, et que l'option de redirection vers la grille (redirectIfOnlyAuto) est activé
        console.log("cette catégorie a des dispos qu'en auto")
        var uniqueSession = false;
        if (sessionsData.length == 1 && JSON.parse(sessionsData[0].value).length == 1) {
            uniqueSession = true;
        }

        var forceZoneId;
        var forceFloorId;
        var forceSectionId;
        if (zoneIds.length == 1) {
            forceZoneId = zoneIds[0]
        }
        if (floorIds.length == 1) {
            forceFloorId = floorIds[0]
        }
        if (sectionsIds.length == 1) {
            forceSectionId = sectionsIds[0]
        }
        if (SettingsMerge.pano.showMsgWhenRedirectIfOnlyAuto) {
            $('#modalNoDsipoOnPlanButDispoOnAuto').modal('show')
            $('#BtnModalNoDsipoOnPlanButDispoOnAutoContinue').off('click').on('click', function () {
                
                $('#modalNoDsipoOnPlanButDispoOnAuto').modal('hide')
                $('#wdgInsertGrilleTarif').html("")
                $("#auto-tab").tab('show')
                if ($('#panoInner').length > 0) {
                    $('#wdgInsertSeatPlan').html("")
                    loadingBootstrapOn('#wdgInsertSeatPlan')
                }
                refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, forceZoneId, forceFloorId, forceSectionId, categId);
            })
        } else {
            $('#wdgInsertGrilleTarif').html("")
            $("#auto-tab").tab('show')
            if ($('#panoInner').length > 0) {
                $('#wdgInsertSeatPlan').html("")
                loadingBootstrapOn('#wdgInsertSeatPlan')
            }
            refreshZoneFloorSection(thisSessionLoadedPlan.session.sessionId, uniqueSession, undefined, forceZoneId, forceFloorId, forceSectionId, categId);
        }
        
    }
}