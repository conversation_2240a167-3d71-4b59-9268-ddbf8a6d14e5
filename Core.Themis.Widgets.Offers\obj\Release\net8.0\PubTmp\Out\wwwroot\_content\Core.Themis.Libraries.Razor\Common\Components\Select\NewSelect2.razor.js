﻿export function init(id, dotnetHelper, nameFunc, value, hideSearch, createTagType, useTags) {
    let select = $(`#${id}`);

    let select2Params = setSelect2Options(value, hideSearch, createTagType, useTags);

    select.off();
    select.select2(select2Params);

    setValue(id, value);

    select.on('change', () => dotnetHelper.invokeMethodAsync(nameFunc, select.val()));
    select.on('select2:unselecting', () => dotnetHelper.invokeMethodAsync(nameFunc, select.val()));
};

export function setValue(id, value, hideSearch, createTagType, useTags) {
    let select = $(`#${id}`);

    if (useTags == true) {
        let select2Params = setSelect2Options(value, hideSearch, createTagType, useTags);
        select.select2(select2Params);
    }

    select.val(value).trigger('change.select2');
};

function addCreateTagToSelect2Params(createTagType, select2Params) {
    switch (createTagType) {
        case "Email":
            select2Params.createTag = (params) => {
                const regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

                if (!params.term.match(regex))
                    return null;

                return {
                    id: params.term,
                    text: params.term
                }
            }
            break;
    }
}

function setSelect2Options(value, hideSearch, createTagType, useTags) {
    let select2Params = {
        theme: "bootstrap4",
    };

    if (useTags == true)
        select2Params.data = value;

    if (hideSearch == true)
        select2Params.minimumResultsForSearch = -1;

    if (createTagType != null)
        addCreateTagToSelect2Params(createTagType, select2Params);

    return select2Params;
}