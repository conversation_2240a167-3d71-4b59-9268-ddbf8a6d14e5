﻿declare @langcolumn varchar(20)

declare @sqlselect varchar(max)
declare @sqlwhere varchar(max)


declare @isExactTerm int
declare @areaid int
declare @searchterm varchar(500)
declare @CountColonne int
declare @n int


set @isExactTerm = [isExactTerm]
set @areaid = [areaId]
set @searchterm = '%[termToSearch]%'

set @sqlselect = ''
set @sqlwhere =  ''
--set @sqlwhere = ' where ( '


--Langues qui sont dans la base de données Rodrigue chez le client
declare @langueClientBddRodrigue varchar(max) = '[langsColumnOf]'


if @areaid <> 0 and  @searchterm <> ''
	begin

		if @isExactTerm > 0 --on cherche le term exact sur les clés
			begin
				set @sqlwhere =  ' where area_id = '+convert(varchar(50), @areaid) + ' and (fc.fieldSpecificCode = ' + ''''+  @searchterm +'''  '
			end
		else
			begin
				set @sqlwhere =  ' where area_id = '+convert(varchar(50), @areaid) + ' and ([termFieldsSpecificCode] fc.fieldSpecificCode Like ' + ''''+  @searchterm +''' OR fgt.fieldCode Like ' + ''''+  @searchterm +''' OR fc.description Like ' + ''''+  @searchterm +''' or '
			end
	end
else if @searchterm <> ''
	begin
		if @isExactTerm > 0
			begin
				set @sqlwhere =  ' where  (fc.fieldSpecificCode = ' + ''''+  @searchterm +'''  '
			end
		else
			begin
				set @sqlwhere =  ' where  ([termFieldsSpecificCode]  fc.fieldSpecificCode Like ' + ''''+  @searchterm +''' OR fgt.fieldCode Like ' + ''''+  @searchterm +''' OR fc.description  Like ' + ''''+  @searchterm +''' or   '
			end
	end
else if @areaid <> 0 
	begin
		set @sqlwhere =  ' where area_id = '+convert(varchar(50), @areaid)
	end



--compte le nombre de colonne
--set @CountColonne = (select count(*) name from information_schema.COLUMNS as c where c.TABLE_NAME = 'translate_fieldsGlobalTranslation' and c.COLUMN_NAME like 'txt_%' )

set @CountColonne = (select count(*) name from information_schema.COLUMNS as c where c.TABLE_NAME = 'translate_fieldsGlobalTranslation' and c.COLUMN_NAME in (select 'txt_'+Name from splitstring(@langueClientBddRodrigue, ',')) )
set @n = 1



DECLARE cursor_translation CURSOR
FOR
	--nom des colonnes commençant par  (txt_)
	--select column_name from information_schema.COLUMNS as c where c.TABLE_NAME = 'translate_fieldsGlobalTranslation' and c.COLUMN_NAME like 'txt_%'
	select column_name from information_schema.COLUMNS as c where c.TABLE_NAME = 'translate_fieldsGlobalTranslation' and c.COLUMN_NAME in (select 'txt_'+Name from splitstring(@langueClientBddRodrigue, ','))

OPEN cursor_translation

FETCH NEXT FROM cursor_translation INTO @langcolumn

WHILE @@FETCH_STATUS = 0
BEGIN

set @sqlselect = @sqlselect + @langcolumn + ','


if @searchterm <> '' and @isExactTerm = 0
	begin
		--si c'est la dernière colonne, on ne met pas le OR
		if @n = @CountColonne
			begin
				set @sqlwhere =  @sqlwhere + @langcolumn + ' Like ' + ''''+  @searchterm +''' '
			end 
		else
			begin
				set @sqlwhere =  @sqlwhere + @langcolumn + ' Like ' + ''''+  @searchterm +'''  OR '
			end 

	end



PRINT (@sqlselect) 

PRINT (@sqlwhere) 

set @n = @n+1
FETCH NEXT FROM cursor_translation INTO @langcolumn
END

CLOSE cursor_translation


if @searchterm <> ''
	begin
		set @sqlwhere =  @sqlwhere + ')'
	end

declare @SQL varchar(max)
set @SQL =  'SELECT fgt.id as global_field_id, fc.id as field_code_id, ' + @sqlselect + ' fieldSpecificCode, fgt.fieldCode, fc.description,
tv.Id as variable_id, tv.Name as variable_name, tv.Description as variable_description, tv.IsAutoCloseTag as variable_isautoclosetag,
fc.area_id, a.id as area_id, a.description as area_description, a.name as area_name,
case when fgt.fieldCode =  fieldSpecificCode 
	then '''' else  fgt.fieldCode
end as parent_name
FROM translate_fieldsGlobalTranslation fgt 
left outer join translate_fieldsCodesList fc on fc.global_field_id = fgt.id 
left outer  join translate_fieldsVariables tfv on fgt.id = tfv.fieldId
left outer join translate_variables tv on tv.Id = tfv.variableId
inner join translate_areas a on a.id = fc.area_id ' + @sqlwhere + '
 order by global_field_id,parent_name'

PRINT (@SQL) 

DEALLOCATE cursor_translation

exec (@SQL)



--pour faire la liste des enfants
select * from translate_fieldsGlobalTranslation fgt
join translate_fieldsCodesList fc on fgt.id = fc.global_field_id
