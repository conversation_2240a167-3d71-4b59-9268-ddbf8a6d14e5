﻿/* comon */
#widget-rodrigue-wrapper {
    font-family: <PERSON><PERSON>, sans-serif, <PERSON><PERSON>;
}

#widget-rodrigue-wrapper .fade {
    transition: opacity .15s linear;
}

/* text bg colors */
#widget-rodrigue-wrapper .text-bg-primary {
    color: #fff !important;
    background-color: RGBA(13, 110, 253, var(--wdgt-bg-opacity, 1)) !important;
}

#widget-rodrigue-wrapper .text-bg-success {
    color: #fff !important;
    background-color: RGB<PERSON>(25, 135, 84, var(--wdgt-bg-opacity, 1)) !important;
}

#widget-rodrigue-wrapper .text-bg-danger {
    color: #fff !important;
    background-color: RGB<PERSON>(220, 53, 69, var(--wdgt-bg-opacity, 1)) !important;
}

#widget-rodrigue-wrapper .text-bg-warning {
    color: #000 !important;
    background-color: RGBA(255, 193, 7, var(--wdgt-bg-opacity, 1)) !important;
}

/*padding*/
#widget-rodrigue-wrapper .p-auto {
    padding: auto !important;
}
#widget-rodrigue-wrapper .py-auto {
    padding-top: auto !important;
    padding-bottom: auto !important;
}

#widget-rodrigue-wrapper .px-auto {
    padding-left: auto !important;
    padding-right: auto !important;
}
#widget-rodrigue-wrapper .py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

#widget-rodrigue-wrapper .py-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
}

#widget-rodrigue-wrapper .py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

#widget-rodrigue-wrapper .py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

#widget-rodrigue-wrapper .py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
}

#widget-rodrigue-wrapper .py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

#widget-rodrigue-wrapper .px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

#widget-rodrigue-wrapper .px-1 {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
}

#widget-rodrigue-wrapper .px-2 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
}

#widget-rodrigue-wrapper .px-3 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

#widget-rodrigue-wrapper .px-4 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

#widget-rodrigue-wrapper .px-5 {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
}

#widget-rodrigue-wrapper .p-0 {
    padding: 0 !important;
}

#widget-rodrigue-wrapper .p-1 {
    padding: 0.25rem !important;
}

#widget-rodrigue-wrapper .p-2 {
    padding: 0.5rem !important;
}

#widget-rodrigue-wrapper .p-3 {
    padding: 1rem !important;
}

#widget-rodrigue-wrapper .p-4 {
    padding: 1.5rem !important;
}

#widget-rodrigue-wrapper .p-5 {
    padding: 3rem !important;
}

#widget-rodrigue-wrapper .pt-0 {
    padding-top: 0 !important;
}

#widget-rodrigue-wrapper .pt-1 {
    padding-top: 0.25rem !important;
}

#widget-rodrigue-wrapper .pt-2 {
    padding-top: 0.5rem !important;
}

#widget-rodrigue-wrapper .pt-3 {
    padding-top: 1rem !important;
}

#widget-rodrigue-wrapper .pt-4 {
    padding-top: 1.5rem !important;
}

#widget-rodrigue-wrapper .pt-5 {
    padding-top: 3rem !important;
}

#widget-rodrigue-wrapper .pe-0 {
    padding-right: 0 !important;
}

#widget-rodrigue-wrapper .pe-1 {
    padding-right: 0.25rem !important;
}

#widget-rodrigue-wrapper .pe-2 {
    padding-right: 0.5rem !important;
}

#widget-rodrigue-wrapper .pe-3 {
    padding-right: 1rem !important;
}

#widget-rodrigue-wrapper .pe-4 {
    padding-right: 1.5rem !important;
}

#widget-rodrigue-wrapper .pe-5 {
    padding-right: 3rem !important;
}

#widget-rodrigue-wrapper .pb-0 {
    padding-bottom: 0 !important;
}

#widget-rodrigue-wrapper .pb-1 {
    padding-bottom: 0.25rem !important;
}

#widget-rodrigue-wrapper .pb-2 {
    padding-bottom: 0.5rem !important;
}

#widget-rodrigue-wrapper .pb-3 {
    padding-bottom: 1rem !important;
}

#widget-rodrigue-wrapper .pb-4 {
    padding-bottom: 1.5rem !important;
}

#widget-rodrigue-wrapper .pb-5 {
    padding-bottom: 3rem !important;
}

#widget-rodrigue-wrapper .ps-0 {
    padding-left: 0 !important;
}

#widget-rodrigue-wrapper .ps-1 {
    padding-left: 0.25rem !important;
}

#widget-rodrigue-wrapper .ps-2 {
    padding-left: 0.5rem !important;
}

#widget-rodrigue-wrapper .ps-3 {
    padding-left: 1rem !important;
}

#widget-rodrigue-wrapper .ps-4 {
    padding-left: 1.5rem !important;
}

#widget-rodrigue-wrapper .ps-5 {
    padding-left: 3rem !important;
}

/*margin*/
#widget-rodrigue-wrapper .m-auto {
    margin: auto !important;
}
#widget-rodrigue-wrapper .my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
}
#widget-rodrigue-wrapper .mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
}

#widget-rodrigue-wrapper .my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

#widget-rodrigue-wrapper .my-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
}

#widget-rodrigue-wrapper .my-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

#widget-rodrigue-wrapper .my-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
}

#widget-rodrigue-wrapper .my-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
}

#widget-rodrigue-wrapper .my-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
}

#widget-rodrigue-wrapper .mx-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

#widget-rodrigue-wrapper .mx-1 {
    margin-left: 0.25rem !important;
    margin-right: 0.25rem !important;
}

#widget-rodrigue-wrapper .mx-2 {
    margin-left: 0.5rem !important;
    margin-right: 0.5rem !important;
}

#widget-rodrigue-wrapper .mx-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
}

#widget-rodrigue-wrapper .mx-4 {
    margin-left: 1.5rem !important;
    margin-right: 1.5rem !important;
}

#widget-rodrigue-wrapper .mx-5 {
    margin-left: 3rem !important;
    margin-right: 3rem !important;
}
#widget-rodrigue-wrapper .m-0 {
    margin: 0 !important;
}

#widget-rodrigue-wrapper .m-1 {
    margin: 0.25rem !important;
}

#widget-rodrigue-wrapper .m-2 {
    margin: 0.5rem !important;
}

#widget-rodrigue-wrapper .m-3 {
    margin: 1rem !important;
}

#widget-rodrigue-wrapper .m-4 {
    margin: 1.5rem !important;
}

#widget-rodrigue-wrapper .m-5 {
    margin: 3rem !important;
}

#widget-rodrigue-wrapper .mt-0 {
    margin-top: 0 !important;
}

#widget-rodrigue-wrapper .mt-1 {
    margin-top: 0.25rem !important;
}

#widget-rodrigue-wrapper .mt-2 {
    margin-top: 0.5rem !important;
}

#widget-rodrigue-wrapper .mt-3 {
    margin-top: 1rem !important;
}

#widget-rodrigue-wrapper .mt-4 {
    margin-top: 1.5rem !important;
}

#widget-rodrigue-wrapper .mt-5 {
    margin-top: 3rem !important;
}

#widget-rodrigue-wrapper .me-0 {
    margin-right: 0 !important;
}

#widget-rodrigue-wrapper .me-1 {
    margin-right: 0.25rem !important;
}

#widget-rodrigue-wrapper .me-2 {
    margin-right: 0.5rem !important;
}

#widget-rodrigue-wrapper .me-3 {
    margin-right: 1rem !important;
}

#widget-rodrigue-wrapper .me-4 {
    margin-right: 1.5rem !important;
}

#widget-rodrigue-wrapper .me-5 {
    margin-right: 3rem !important;
}

#widget-rodrigue-wrapper .mb-0 {
    margin-bottom: 0 !important;
}

#widget-rodrigue-wrapper .mb-1 {
    margin-bottom: 0.25rem !important;
}

#widget-rodrigue-wrapper .mb-2 {
    margin-bottom: 0.5rem !important;
}

#widget-rodrigue-wrapper .mb-3 {
    margin-bottom: 1rem !important;
}

#widget-rodrigue-wrapper .mb-4 {
    margin-bottom: 1.5rem !important;
}

#widget-rodrigue-wrapper .mb-5 {
    margin-bottom: 3rem !important;
}

#widget-rodrigue-wrapper .ms-0 {
    margin-left: 0 !important;
}

#widget-rodrigue-wrapper .ms-1 {
    margin-left: 0.25rem !important;
}

#widget-rodrigue-wrapper .ms-2 {
    margin-left: 0.5rem !important;
}

#widget-rodrigue-wrapper .ms-3 {
    margin-left: 1rem !important;
}

#widget-rodrigue-wrapper .ms-4 {
    margin-left: 1.5rem !important;
}

#widget-rodrigue-wrapper .ms-5 {
    margin-left: 3rem !important;
}

/* display */
#widget-rodrigue-wrapper .d-flex {
    display: flex !important
}

/*position */
#widget-rodrigue-wrapper.position-static {
    position: static !important;
}

#widget-rodrigue-wrapper .position-sticky {
    position: sticky !important;
}

#widget-rodrigue-wrapper .position-relative {
    position: relative !important;
}

#widget-rodrigue-wrapper .position-absolute {
    position: absolute !important;
}

#widget-rodrigue-wrapper .position-fixed {
    position: fixed !important;
}

#widget-rodrigue-wrapper .start-0 {
    left: 0 !important;
}

#widget-rodrigue-wrapper .start-50 {
    left: 50% !important;
}

#widget-rodrigue-wrapper .end-0 {
    right: 0 !important;
}

#widget-rodrigue-wrapper .top-0 {
    top: 0 !important;
}

#widget-rodrigue-wrapper .top-50 {
    top: 50% !important;
}

#widget-rodrigue-wrapper .bottom-0 {
    bottom: 0 !important;
}

#widget-rodrigue-wrapper .translate-middle-x {
    transform: translateX(-50%) !important;
}

#widget-rodrigue-wrapper .translate-middle-y {
    transform: translateY(-50%) !important;
}

/****************** btn ****************/
#widget-rodrigue-wrapper .btn {
    --wdgt-btn-padding-x: 0.75rem;
    --wdgt-btn-padding-y: 0.375rem;
    --wdgt-btn-font-family:;
    --wdgt-btn-font-size: 1rem;
    --wdgt-btn-font-weight: 400;
    --wdgt-btn-line-height: 1.5;
    --wdgt-btn-color: #212529;
    --wdgt-btn-bg: transparent;
    --wdgt-btn-border-width: 1px;
    --wdgt-btn-border-color: transparent;
    --wdgt-btn-border-radius: 100px;
    --wdgt-btn-hover-border-color: transparent;
    --wdgt-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
    --wdgt-btn-disabled-opacity: 0.65;
    --wdgt-btn-focus-box-shadow: 0 0 0 0.25rem rgba(130, 138, 145, .5);
    display: inline-block;
    padding: var(--wdgt-btn-padding-y) var(--wdgt-btn-padding-x);
    font-family: var(--wdgt-btn-font-family);
    font-size: var(--wdgt-btn-font-size);
    font-weight: var(--wdgt-btn-font-weight);
    line-height: var(--wdgt-btn-line-height);
    color: var(--wdgt-btn-color);
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border: var(--wdgt-btn-border-width) solid var(--wdgt-btn-border-color);
    border-radius: var(--wdgt-btn-border-radius);
    background-color: var(--wdgt-btn-bg);
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

#widget-rodrigue-wrapper .btn-secondary {
    --wdgt-btn-color: #fff;
    --wdgt-btn-bg: #6c757d;
    --wdgt-btn-border-color: #6c757d;
    --wdgt-btn-hover-color: #fff;
    --wdgt-btn-hover-bg: #5c636a;
    --wdgt-btn-hover-border-color: #565e64;
    --wdgt-btn-focus-shadow-rgb: 130, 138, 145;
    --wdgt-btn-active-color: #fff;
    --wdgt-btn-active-bg: #565e64;
    --wdgt-btn-active-border-color: #51585e;
    --wdgt-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --wdgt-btn-disabled-color: #fff;
    --wdgt-btn-disabled-bg: #6c757d;
    --wdgt-btn-disabled-border-color: #6c757d;
}

#widget-rodrigue-wrapper .btn-primary {
    --wdgt-btn-color: #fff;
    --wdgt-btn-bg: #0d6efd;
    --wdgt-btn-border-color: #0d6efd;
    --wdgt-btn-hover-color: #fff;
    --wdgt-btn-hover-bg: #0b5ed7;
    --wdgt-btn-hover-border-color: #0a58ca;
    --wdgt-btn-focus-shadow-rgb: 49, 132, 253;
    --wdgt-btn-active-color: #fff;
    --wdgt-btn-active-bg: #0a58ca;
    --wdgt-btn-active-border-color: #0a53be;
    --wdgt-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --wdgt-btn-disabled-color: #fff;
    --wdgt-btn-disabled-bg: #0d6efd;
    --wdgt-btn-disabled-border-color: #0d6efd;
}

#widget-rodrigue-wrapper .btn-success {
    --wdgt-btn-color: #fff;
    --wdgt-btn-bg: #198754;
    --wdgt-btn-border-color: #198754;
    --wdgt-btn-hover-color: #fff;
    --wdgt-btn-hover-bg: #157347;
    --wdgt-btn-hover-border-color: #146c43;
    --wdgt-btn-focus-shadow-rgb: 60, 153, 110;
    --wdgt-btn-active-color: #fff;
    --wdgt-btn-active-bg: #146c43;
    --wdgt-btn-active-border-color: #13653f;
    --wdgt-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --wdgt-btn-disabled-color: #fff;
    --wdgt-btn-disabled-bg: #198754;
    --wdgt-btn-disabled-border-color: #198754;
}

#widget-rodrigue-wrapper .btn-danger {
    --wdgt-btn-color: #fff;
    --wdgt-btn-bg: #dc3545;
    --wdgt-btn-border-color: #dc3545;
    --wdgt-btn-hover-color: #fff;
    --wdgt-btn-hover-bg: #bb2d3b;
    --wdgt-btn-hover-border-color: #b02a37;
    --wdgt-btn-focus-shadow-rgb: 225, 83, 97;
    --wdgt-btn-active-color: #fff;
    --wdgt-btn-active-bg: #b02a37;
    --wdgt-btn-active-border-color: #a52834;
    --wdgt-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --wdgt-btn-disabled-color: #fff;
    --wdgt-btn-disabled-bg: #dc3545;
    --wdgt-btn-disabled-border-color: #dc3545;
}

    #widget-rodrigue-wrapper .btn-close {
        --wdgt-btn-close-color: #000;
        --wdgt-btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e");
        --wdgt-btn-close-opacity: 0.5;
        --wdgt-btn-close-hover-opacity: 0.75;
        --wdgt-btn-close-focus-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        --wdgt-btn-close-focus-opacity: 1;
        --wdgt-btn-close-disabled-opacity: 0.25;
        --wdgt-btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);
        box-sizing: content-box;
        width: 1em;
        height: 1em;
        padding: .25em .25em;
        color: var(--wdgt-btn-close-color);
        background: transparent var(--wdgt-btn-close-bg) center / 1em auto no-repeat;
        border: 0;
        border-radius: .375rem;
        opacity: var(--wdgt-btn-close-opacity);
    }

#widget-rodrigue-wrapper .btn-close:hover {
    cursor: pointer;
}

#widget-rodrigue-wrapper .btn-close-white {
    filter: var(--wdgt-btn-close-white-filter);
}

/****************** form ***********************/
#widget-rodrigue-wrapper .form-control {
    display: block;
    width: 100%;
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--wdgt-body-color);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

#widget-rodrigue-wrapper .form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: .125rem;
}

#widget-rodrigue-wrapper .form-switch {
    padding-left: 2.5em;
}

#widget-rodrigue-wrapper .form-check-input {
    --wdgt-form-check-bg: #fff;
    flex-shrink: 0;
    width: 1em;
    height: 1em;
    margin-top: .25em;
    vertical-align: top;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--wdgt-form-check-bg);
    background-image: var(--wdgt-form-check-bg-image);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid #dee2e6;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
    print-color-adjust: exact;
}

#widget-rodrigue-wrapper .form-check .form-check-input {
    float: left;
    margin-left: -1.5em;
}

#widget-rodrigue-wrapper .form-check-input[type=checkbox] {
    border-radius: .25em;
}

#widget-rodrigue-wrapper .form-check-input[type=radio] {
    border-radius: 50%;
}

#widget-rodrigue-wrapper .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#widget-rodrigue-wrapper .form-check-input:checked[type=radio] {
    --wdgt-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns=!string!viewBox=!string!%3e%3ccircle r=!string!fill=!string!/%3e%3c/svg%3e");
}

#widget-rodrigue-wrapper .form-check-input:checked[type=checkbox] {
    --wdgt-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns=!string!viewBox=!string!%3e%3cpath fill=!string!stroke=!string!stroke-linecap=!string!stroke-linejoin=!string!stroke-width=!string!d=!string!/%3e%3c/svg%3e");
}

#widget-rodrigue-wrapper .form-switch .form-check-input {
    --wdgt-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns=!string!viewBox=!string!%3e%3ccircle r=!string!fill=!string!/%3e%3c/svg%3e");
    width: 2em;
    margin-left: -2.5em;
    background-image: var(--wdgt-form-switch-bg);
    background-position: left center;
    border-radius: 2em;
    transition: background-position .15s ease-in-out;
}

#widget-rodrigue-wrapper .form-check-input:checked[type=checkbox] {
    --wdgt-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns=!string!viewBox=!string!%3e%3cpath fill=!string!stroke=!string!stroke-linecap=!string!stroke-linejoin=!string!stroke-width=!string!d=!string!/%3e%3c/svg%3e");
}

#widget-rodrigue-wrapper .form-switch .form-check-input:checked {
    background-position: right center;
    --wdgt-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns=!string!viewBox=!string!%3e%3ccircle r=!string!fill=!string!/%3e%3c/svg%3e");
}

/****************** modal ****************/
.widget-modal-open {
    overflow: hidden;
    padding-right: 17px;
}

.widget-modal {
    --wdgt-modal-zindex: 1055;
    --wdgt-modal-width: 500px;
    --wdgt-modal-padding: 1rem;
    --wdgt-modal-margin: 0.5rem;
    --wdgt-modal-color:;
    --wdgt-modal-bg: #fff;
    --wdgt-modal-border-color: rgba(0, 0, 0, 0.175);
    --wdgt-modal-border-width: 1px;
    --wdgt-modal-border-radius: 0.5rem;
    --wdgt-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --wdgt-modal-inner-border-radius: calc(0.5rem - (1px));
    --wdgt-modal-header-padding-x: 1rem;
    --wdgt-modal-header-padding-y: 1rem;
    --wdgt-modal-header-padding: 1rem 1rem;
    --wdgt-modal-header-border-color: #dee2e6;
    --wdgt-modal-header-border-width: 1px;
    --wdgt-modal-title-line-height: 1.5;
    --wdgt-modal-footer-gap: 0.5rem;
    --wdgt-modal-footer-bg:;
    --wdgt-modal-footer-border-color: #dee2e6;
    --wdgt-modal-footer-border-width: 1px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--wdgt-modal-zindex);
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

@media (min-width:576px) {
    .widget-modal {
        --wdgt-modal-margin: 1.75rem;
        --wdgt-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
}
.widget-modal.fade.show{
    opacity:1;
}
.widget-modal.fade .modal-dialog {
    transition: transform .3s ease-out;
    transform: translate(0, -50px);
}

.widget-modal.show .modal-dialog {
    transform: none;
}

.widget-modal .modal-dialog {
    position: relative;
    width: auto;
    margin: var(--wdgt-modal-margin);
    pointer-events: none;
}

.widget-modal .modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - var(--wdgt-modal-margin)* 2);
}

@media (min-width: 1200px) {
    .widget-modal .modal-xl {
        --wdgt-modal-width: 1140px;
    }
}

@media (min-width: 992px) {
    .widget-modal .modal-lg, .modal-xl {
        --wdgt-modal-width: 800px;
    }
}
@media (min-width: 576px) {
    .widget-modal .modal-sm {
        --wdgt-modal-width: 300px;
    }
}

@media (min-width:576px) {
    .widget-modal .modal-dialog {
        max-width: var(--wdgt-modal-width);
        margin-right: auto;
        margin-left: auto;
    }
}

.widget-modal .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: var(--wdgt-modal-color);
    pointer-events: auto;
    background-color: var(--wdgt-modal-bg);
    background-clip: padding-box;
    border: var(--wdgt-modal-border-width) solid var(--wdgt-modal-border-color);
    border-radius: var(--wdgt-modal-border-radius);
    outline: 0;
}

.widget-modal .modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    padding: var(--wdgt-modal-header-padding);
    border-bottom: var(--wdgt-modal-header-border-width) solid var(--wdgt-modal-header-border-color);
    border-top-left-radius: var(--wdgt-modal-inner-border-radius);
    border-top-right-radius: var(--wdgt-modal-inner-border-radius);
}

.widget-modal .modal-header .btn-close {
    padding: calc(var(--wdgt--modal-header-padding-y)* .5) calc(var(--wdgt--modal-header-padding-x)* .5);
    margin: calc(-.5* var(--wdgt--modal-header-padding-y)) calc(-.5* var(--wdgt--modal-header-padding-x)) calc(-.5* var(--wdgt--modal-header-padding-y)) auto;
}

.widget-modal .modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: var(--wdgt-modal-padding);
}

.widget-modal .modal-footer {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: calc(var(--wdgt-modal-padding) - var(--wdgt-modal-footer-gap)* .5);
    background-color: var(--wdgt-modal-footer-bg);
    border-top: var(--wdgt-modal-footer-border-width) solid var(--wdgt-modal-footer-border-color);
    border-bottom-right-radius: var(--wdgt-modal-inner-border-radius);
    border-bottom-left-radius: var(--wdgt-modal-inner-border-radius);
}

.widget-modal .modal-footer > * {
    margin: calc(var(--wdgt-modal-footer-gap)* .5);
}

.widget-modal .modal-title {
    margin-top: 0;
    margin-bottom: 0;
    line-height: var(--wdgt-modal-title-line-height);
    font-size: 1.25rem;
}

.widget-modal .modal-header .btn-close {
    padding: calc(var(--wdgt-modal-header-padding-y)* .5) calc(var(--wdgt-modal-header-padding-x)* .5);
    margin: calc(-.5* var(--wdgt-modal-header-padding-y)) calc(-.5* var(--wdgt-modal-header-padding-x)) calc(-.5* var(--wdgt-modal-header-padding-y)) auto;
}

.widget-modal-backdrop {
    --wdgt-backdrop-zindex: 1050;
    --wdgt-backdrop-bg: #000;
    --wdgt-backdrop-opacity: 0.5;
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--wdgt-backdrop-zindex);
    width: 100vw;
    height: 100vh;
    background-color: var(--wdgt-backdrop-bg);
}

.widget-modal-backdrop.show {
    opacity: var(--wdgt-backdrop-opacity);
}


/*** toast ***/
#widget-rodrigue-wrapper .toast-container {
    --wdgt-toast-zindex: 1090;
    position: absolute;
    z-index: var(--wdgt-toast-zindex);
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    max-width: 100%;
    pointer-events: none;
}

#widget-rodrigue-wrapper .toast {
    --wdgt-toast-zindex: 1090;
    --wdgt-toast-padding-x: 0.75rem;
    --wdgt-toast-padding-y: 0.5rem;
    --wdgt-toast-spacing: 1.5rem;
    --wdgt-toast-max-width: 350px;
    --wdgt-toast-font-size: 0.875rem;
    --wdgt-toast-color:;
    --wdgt-toast-bg: rgba(255, 255, 255, 0.85);
    --wdgt-toast-border-width: 1px;
    --wdgt-toast-border-color: rgba(0, 0, 0, 0.175);
    --wdgt-toast-border-radius: 0.375rem;
    --wdgt-toast-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --wdgt-toast-header-color: rgba(33, 37, 41, 0.75);
    --wdgt-toast-header-bg: rgba(255, 255, 255, 0.85);
    --wdgt-toast-header-border-color: rgba(0, 0, 0, 0.175);
    width: var(--wdgt-toast-max-width);
    max-width: 100%;
    font-size: var(--wdgt-toast-font-size);
    color: var(--wdgt-toast-color);
    pointer-events: auto;
    background-color: var(--wdgt-toast-bg);
    background-clip: padding-box;
    border: var(--wdgt-toast-border-width) solid var(--wdgt-toast-border-color);
    box-shadow: var(--wdgt-toast-box-shadow);
    border-radius: var(--wdgt-toast-border-radius);
}
#widget-rodrigue-wrapper .toast.fade.show {
    opacity: 1;
}
#widget-rodrigue-wrapper .toast-header {
    display: flex;
    align-items: center;
    padding: var(--wdgt-toast-padding-y) var(--wdgt-toast-padding-x);
    color: var(--wdgt-toast-header-color);
    background-color: var(--wdgt-toast-header-bg);
    background-clip: padding-box;
    border-bottom: var(--wdgt-toast-border-width) solid var(--wdgt-toast-header-border-color);
    border-top-left-radius: calc(var(--wdgt-toast-border-radius) - var(--wdgt-toast-border-width));
    border-top-right-radius: calc(var(--wdgt-toast-border-radius) - var(--wdgt-toast-border-width));
}

#widget-rodrigue-wrapper .toast-header .btn-close {
    margin-right: calc(-.5* var(--wdgt-toast-padding-x));
    margin-left: var(--wdgt-toast-padding-x);
}

#widget-rodrigue-wrapper .toast-body {
    padding: var(--wdgt-toast-padding-x);
    word-wrap: break-word;
}

#widget-rodrigue-wrapper .fade:not(.show) {
    opacity: 0;
}

#widget-rodrigue-wrapper .toast.showing {
    opacity: 0;
}

#widget-rodrigue-wrapper .toast:not(.show) {
    display: none;
}
