﻿/*
DECLARE @field_code varchar(300)
DECLARE @value varchar(300)
DECLARE @langue_code varchar(300)

set @field_code = '4'
set @value = ''
set @langue_code = 3
*/



IF (not EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE  TABLE_NAME = 'translate_fieldsSpecificTranslation'))
BEGIN

CREATE TABLE [dbo].[translate_fieldsSpecificTranslation](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[fieldCode] [varchar](max) NULL,
	[val] [varchar](200) NULL,
	[lang_id] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]


ALTER TABLE [dbo].[translate_fieldsSpecificTranslation]  WITH CHECK ADD  CONSTRAINT [FK_translate_fieldsSpecificLocalTranslation_langue] FOREIGN KEY([lang_id])
REFERENCES [dbo].[langue] ([langue_id])



END





DECLARE @nb_row_language int
select @nb_row_language = COUNT(*) from langue where langue_code = @langue_code

 IF(@nb_row_language > 0)
 BEGIN
	
	DECLARE @langue_id int
	select @langue_id = langue_id from langue where langue_code = @langue_code

	DECLARE @nb_row_traslation int
	select @nb_row_traslation =COUNT(*) from translate_fieldsSpecificTranslation where fieldCode= @field_code and lang_id = @langue_id


	IF @value = ''
		BEGIN
		print 'on supprime'
			DELETE FROM translate_fieldsSpecificTranslation where fieldCode=@field_code and lang_id = @langue_id
		END
	ELSE IF(@nb_row_traslation > 0) -- clé + langue remonte une ligne, on update
		BEGIN
			print 'on update'
			update translate_fieldsSpecificTranslation set val=@value where fieldCode=@field_code and lang_id = @langue_id
		END

	ELSE
		BEGIN
		print 'on insert'
			INSERT translate_fieldsSpecificTranslation(fieldCode, val, lang_id) VALUES(@field_code, @value, @langue_id)
		END

	END
	
