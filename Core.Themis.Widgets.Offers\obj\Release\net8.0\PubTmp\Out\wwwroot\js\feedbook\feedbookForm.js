﻿feedbookTokensUsedToCheck = []

$(document).ready(function () {
    $('.inputFeedBook').off('input').on('input', function () {
        $('#SelectionNeedCompletion').hide()
        $(this).removeClass('is-invalid')
        $("#inputFeedBookError").html("").removeClass("d-inline")
    })
    //init bouton check carte avantage
    initBtnCheckFeedBook()
    //init bouton ajouter la selection au panier
    initBtnFeedBookAddSelectionToBasket()

    $('#modalLinkFeedBookToPrices [data-toggle="popover"]').popover()
})


function initBtnCheckFeedBook() {
    $("#btnCheckFeedBook").off('click').on('click', function () {
        loadingButtonBootstrapOn(this)

        $("#inputFeedBook").removeClass('is-invalid')
        $("#inputFeedBookError").html("").removeClass("d-inline")

        feedbookNumber = $("#inputFeedBook").val()

        if (feedbookNumber != null && feedbookNumber.trim() != "") {
            feedbookNumber = feedbookNumber.trim()
            //check si le numéro de carte est correct et utilisable
            checkFeedBookNumber(feedbookNumber, SelectionNeedFeedBook, feedbookTokensUsedToCheck)
        } else {
            //sinon on remet le bouton de validation disponible
            loadingButtonBootstrapOff(this)
        }
    })
}

function initBtnFeedBookAddSelectionToBasket() {
    $('#feedBookAddSelectionToBasket').off('click').on('click', function () {

        var modalContainer = $(this).closest('.modal')
       
        var allCardFilled = true;
        //var allComeWithComplete = true;
        $.each($('#TarifToFeedBookList .selectionNeedFeedBook'), function (i, k) {
            if ($(k).attr("data-complete") != 'true') {
                allCardFilled = false
            }
        })
        if (allCardFilled) {
            //si tous les tarifs sont "complets"
            $(modalContainer).off('hidden.bs.modal',).on('hidden.bs.modal', function (e) {
                if (selectionHasPricesAdhesion(preBasket)) {
                    //si le panier contient des adhesions
                    openModalAdhesionSwitch(feedBookStructureId, feedBookEventId, feedBookSessionId, feedBookIdentityId, feedBookWebUserId, buyerProfilId, feedBookLangCode, partnerToken, preBasket)
                } else {
                    //le panier est ok, on va au basket
                    addToBasket(feedBookStructureId, feedBookEventId, feedBookSessionId, feedBookIdentityId, Basket.basketId, feedBookWebUserId, buyerProfilId, feedBookLangCode, partnerToken, preBasket, "#modalLinkFeedBookToPrices")
                }
            })
            $.each($('.selectionNeedFeedBook'), function (i, k) {
                var thisgpId = $(k).attr('data-gpid');
                var thiscategId = $(k).attr('data-categid');
                var tokenIds = $(k).attr('data-tokensidsused').split(",").filter(elm => elm)
                var tokenJetons = $(k).attr('data-tokensjetonsused').split(",").filter(elm => elm)
                //on parcours le pre-basket pour ajouter les tokens utilisés
                $.each(preBasket, function (pbi, pbk) {
                    if (pbk.categId == thiscategId) {
                        $.each(pbk.propertiesSeatSelectionToFlagList, function (pbgpi, pbgpk) {
                            if (pbgpk.gestionPlaceId == thisgpId) {

                                var preBasketFeedBookTokensUsed = []
                                if (pbgpk.feedBookTokensUsed != null) {
                                    preBasketFeedBookTokensUsed = pbgpk.feedBookTokensUsed
                                }
                                $.each(tokenJetons, function (ijeton, kjeton) {

                                    var feedbookTokensUsedTmp = {
                                        TokenId: parseInt(tokenIds[ijeton]),
                                        Identifiant: kjeton
                                    }
                                    preBasketFeedBookTokensUsed.push(feedbookTokensUsedTmp)
                                })

                                pbgpk.feedBookTokensUsed = preBasketFeedBookTokensUsed
                            }
                        })
                    }
                })
            })
            //on ferme la modal
            $(modalContainer).modal('hide')
        } else {

            $('#SelectionNeedCompletion').show()
            console.log('des cartes ne sont pas renseignées')
        }
    })
}

//check si le numero de carnet est correct et utilisable
function checkFeedBookNumber(feedbookNumber, SelectionNeedFeedBook, feedbookTokensUsedToCheck) {
    //console.log(SelectionNeedFeedBook)
    $.ajax({
        type: "POST",
        url: widgetOfferUrl + "CheckFeedBookAjax/" + feedBookStructureId,
        data: {
            feedbookNumber: feedbookNumber,
            SelectionNeedFeedBook: JSON.stringify(SelectionNeedFeedBook),
            feedbookTokensUsed: JSON.stringify(feedbookTokensUsedToCheck)
        },
        success: function (data) {
            /*
             200 => carnet existe et utilisable
             204 => caret existe, mais inutilisable
             404 => carnet introuvable
             console.log(data);
             */

            switch (data.statusCode) {
                case 200:
                    getFeedBookNumberInfos(feedbookNumber, data.detail)
                    break;
                default:
                    CheckFeedBookError(data.title)
                    break;
            }
        },
        error: function (a, b, c) {
            console.log("CheckFeedBookAjax -> Error")
            console.log(a.responseText)
            var ErrorTitle = a.responseText
            if (a.responseText === ({}).constructor) {
                ErrorTitle = JSON.parse(a.responseText).title
            }
            CheckFeedBookError(ErrorTitle)
            //loadingButtonBootstrapOff('#btnCheckFeedBook')
        }
    });
}

//récupère les informations du carnet de ticket
function getFeedBookNumberInfos(feedbookNumber, GpTokensReceived) {

    console.log("CheckFeedBookAjax ---------")
    console.log(GpTokensReceived);
    $.ajax({
        type: "GET",
        url: widgetOfferUrl + "GetFeedBookAjax/" + feedBookStructureId,
        data: {
            feedbookNumber: feedbookNumber
        },
        success: function (data) {
            addFeedBookToListUsed(data.detail, GpTokensReceived)
            FeedBookOk(GpTokensReceived)
        },
        error: function (a, b, c) {
            console.log("GetFeedBookAjax -> Error")
            console.log(a.responseText)
            CheckFeedBookError(JSON.parse(a.responseText).title)
        },
        complete: function () {
            loadingButtonBootstrapOff('#btnCheckFeedBook')
        }
    });
}

//ajoute la ligne du carnet utilisé
function addFeedBookToListUsed(datadetail, GpTokensReceived) {
    //console.log(datadetail)
    //datadetail.identifiant
    //total ticket = datadetail.listJetons
    //date expiration =  datadetail.validityEndDate
    let validityDateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    var validityEndDate = new Date(datadetail.validityEndDate).toLocaleString(feedBookLangCode, validityDateOptions)

    var JetonsUsed = []
    var TokenIdUsed = []
    $.each(GpTokensReceived, function (i, k) {
        if (k.tokenJeton != "") {
            JetonsUsed.push(k.tokenJeton)
            TokenIdUsed.push(k.tokenId)
        }
    })

    if ($('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').length > 0) {
        var oldjetons = $('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').attr('data-tokensjetonsused').split(',').filter(elm => elm)
        var oldtokensid = $('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').attr('data-tokensidsused').split(',').filter(elm => elm)

        var newjetons = $.merge(oldjetons, JetonsUsed); 
        var newtokensid = $.merge(oldtokensid, TokenIdUsed);
        $('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').attr('data-tokensjetonsused', newjetons.join(','))
        $('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').attr('data-tokensidsused', newtokensid.join(','))
        var html = ""
        var htmlText = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblFeedBookDetail") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblFeedBookDetail") : 'Carnet {FeedBookNumber} ({FeedBookCountRemains} sur {FeedBookCountTotal} tickets restants après utilisation) valide jusqu\'au { EndDate } (trad)')
        htmlText = htmlText.replaceAll("{FeedBookNumber}", datadetail.identifiant).replaceAll("{FeedBookCountRemains}", (datadetail.listJetonsValidesLibres.length - newjetons.length)).replaceAll("{FeedBookCountTotal}", datadetail.listJetons.length).replaceAll("{EndDate}", validityEndDate)
        html += "<i class='text-danger fa-solid fa-trash onefeedbook-delete'></i> " + htmlText
        $('.onefeedbook[data-bookid="' + datadetail.bookId + '"]').html(html)
    } else {
        var html = ""
        html += "<div class='onefeedbook' data-bookid=" + datadetail.bookId + " data-tokensjetonsused='" + JetonsUsed.join(',') + "' data-tokensidsused='" + TokenIdUsed.join(',') + "'>"
        var htmlText = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblFeedBookDetail") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblFeedBookDetail") : 'Carnet {FeedBookNumber} ({FeedBookCountRemains} sur {FeedBookCountTotal} tickets restants après utilisation) valide jusqu\'au { EndDate } (trad)')
        htmlText = htmlText.replaceAll("{FeedBookNumber}", datadetail.identifiant).replaceAll("{FeedBookCountRemains}", (datadetail.listJetonsValidesLibres.length - JetonsUsed.length)).replaceAll("{FeedBookCountTotal}", datadetail.listJetons.length).replaceAll("{EndDate}", validityEndDate)
        html += "<i class='text-danger fa-solid fa-trash onefeedbook-delete'></i> " + htmlText
        html += "</div>"
        $("#validFeedBookList").append(html)
    }
    
   
    initDeleteFeedBookToListUsed()
}


//carte avantage est ok
function FeedBookOk(datadetail) {
    //console.log("carte ok")
    //console.log(datadetail)
    $.each(datadetail, function (i, k) {

        if (k.tokenJeton != "" && $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').length > 0 && $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-complete') === "false") {
            var max = parseInt($('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-max'))
            var actualremains = parseInt($('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"] .selectionNeedFeedBookRemains').text())

            if (actualremains < max) {
                actualremains++
                $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"] .selectionNeedFeedBookRemains').text(actualremains)
                var tokensJetons = $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-tokensjetonsused').split(',').filter(elm => elm)
                tokensJetons.push(k.tokenJeton)
                $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-tokensjetonsused', tokensJetons.join(','))

                var tokensIds = $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-tokensidsused').split(',').filter(elm => elm)
                tokensIds.push(k.tokenId)
                $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-tokensidsused', tokensIds.join(','))


            }
            //si la selection de siège est complete
            if (actualremains >= max) {
                $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').attr('data-complete', true)
                $('.selectionNeedFeedBook[data-gpid="' + k.gestionPlaceId + '"]').removeClass("alert-danger").addClass("alert-success")
            }

            for (var i in SelectionNeedFeedBook) {
                if (SelectionNeedFeedBook[i].gestionPlaceId == k.gestionPlaceId) {
                    SelectionNeedFeedBook[i].seatCount = max - actualremains;
                }
            }
            var feedbookTokensUsedTmp = {
                TokenId: k.tokenId,
                Identifiant : k.tokenJeton
            }
            feedbookTokensUsedToCheck.push(feedbookTokensUsedTmp)
        }
    })
    $('#inputFeedBook').val('')

    sendIframeSize()
}


//intialisation du delete d'un carnet de tickets
function initDeleteFeedBookToListUsed() {
    $("#validFeedBookList .onefeedbook-delete").off('click').on('click', function () {
        var thisTokensUsed = $(this).closest(".onefeedbook").attr("data-tokensjetonsused").split(',')
        //on parcourt les tokens utilisé par ce carnet
        $.each(thisTokensUsed, function (ti, tk) {
            //on parcourt tous les sièges sélectionnés
            $.each($('.selectionNeedFeedBook '), function (si, sk) {
                var selectionTokensJetonsUsed = $(this).attr("data-tokensjetonsused").split(',')
                var selectionTokensIdsUsed = $(this).attr("data-tokensidsused").split(',')
                //si un token du carnet correspond a un token de la selection de siège
                if ($.inArray(tk, selectionTokensJetonsUsed) != -1) {

                    //on réattribue les places restantes à la selection de siège
                    var selectionTokensJetonsUsedIndex = $.inArray(tk, selectionTokensJetonsUsed)
                    selectionTokensJetonsUsed.splice(selectionTokensJetonsUsedIndex, 1)
                    selectionTokensIdsUsed.splice(selectionTokensJetonsUsedIndex, 1)
                    var actualremains = parseInt($(sk).find('.selectionNeedFeedBookRemains').text()) - 1
                    $(sk).find('.selectionNeedFeedBookRemains').html(actualremains)
                    $(sk).attr('data-tokensjetonsused', selectionTokensJetonsUsed.join(',')).attr('data-tokensidsused', selectionTokensIdsUsed.join(',')).attr('data-complete', false).addClass("alert-danger").removeClass("alert-success")
                   
                    //on change les seatCount sur la selection globale
                    var gpid = $(sk).attr('data-gpid')
                    var max = parseInt($(sk).attr('data-max'))
                    for (var i in SelectionNeedFeedBook) {
                        if (SelectionNeedFeedBook[i].gestionPlaceId == gpid) {
                            SelectionNeedFeedBook[i].seatCount = max - actualremains;
                        }
                    }
                    //on recupère les index a supprimer pour feedbookTokensUsed
                    var feedbookTokensUsedIdx = []
                    $.each(feedbookTokensUsedToCheck, function (tui, tuk) {
                        if (tk == tuk.Identifiant) {
                            //console.log(tuk)
                            feedbookTokensUsedIdx.push(tui)
                            
                        }
                    })
                    //on supprime ces index
                    $.each(feedbookTokensUsedIdx, function (tui, tuk) {
                        feedbookTokensUsedToCheck.splice(tuk, 1)
                    })
                    
                }
            })

        })
        //on delete le carnet
        $(this).closest(".onefeedbook").remove()
    })
}

//le carnet à ticket n'est pas valable ou utilisable
function CheckFeedBookError(errorTitle) {
    /*
    200 => carnet existe et utilisable
    204 => caret existe, mais inutilisable
    404 => carnet introuvable
    */
    var templateFeedBookError = ""
    switch (errorTitle) {
        case 204:
        case 404:
            //console.log("carnet existe, mais inutilisable")
            templateFeedBookError = $('#hidden-template-feedbook-error-unusable').html();
            break;
        default:
            //console.log("erreur générique")
            templateFeedBookError = $('#hidden-template-feedbook-error-generic').html();
            break;
    }
    //console.log(templateFeedBookError)
    $("#inputFeedBook").addClass('is-invalid')
    $("#inputFeedBookError").html(templateFeedBookError).addClass("d-inline")
    loadingButtonBootstrapOff('#btnCheckFeedBook')
}
