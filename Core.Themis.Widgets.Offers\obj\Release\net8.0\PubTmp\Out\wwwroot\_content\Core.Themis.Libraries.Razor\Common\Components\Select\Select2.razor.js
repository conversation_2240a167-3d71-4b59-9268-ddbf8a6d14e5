﻿export function init(id, hideSearch, hideDropdown, createTagType, data) {

    let select2Params = {
        theme: "bootstrap4",
    };

    if (data != null)
        select2Params.data = data

    if (hideSearch == true)
        select2Params.minimumResultsForSearch = -1;

    if (createTagType != null)
        addCreateTagToSelect2Params(createTagType, select2Params);

    if (hideDropdown == true) {
        $(`#${id}`)
            .select2(select2Params)
            .on('select2:opening select2:close', (e) => $('body').toggleClass('kill-all-select2-dropdowns', e.type == 'select2:opening'));
    } else {
        $(`#${id}`)
            .select2(select2Params);
    }
}

export function onChangeSelect2(id, dotNetReference) {
    let select = $(`#${id}`);

    select.on('change', () => dotNetReference.invokeMethodAsync('OnSelect2ChangeAsync', select.val(), select.find(':selected').attr('data-additional-info')));
    select.on('select2:unselecting', () => dotNetReference.invokeMethodAsync('OnSelect2ChangeAsync', select.val(), null));
}

export function setSelect2Value(id, value) {
    $(`#${id}`).val(value).trigger('change');
}

export function triggerChangeSelect2(id) {
    $(`#${id}`).trigger('change');
}

function addCreateTagToSelect2Params(createTagType, select2Params) {
    switch (createTagType) {
        case "Email":
            select2Params.createTag = (params) => {
                const regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

                if (!params.term.match(regex))
                    return null;

                return {
                    id: params.term,
                    text: params.term
                }
            }
            break;
    }
}
