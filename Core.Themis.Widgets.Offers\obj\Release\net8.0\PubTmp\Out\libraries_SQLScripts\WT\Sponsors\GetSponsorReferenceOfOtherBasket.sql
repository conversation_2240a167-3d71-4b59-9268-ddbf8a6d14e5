
/*

DECLARE @tableReferenceSponsors table (gestionPlaceId int, referenceSponsor varchar(max))
insert into @tableReferenceSponsors values (55327, '6EB9CC246D86878546E886B07EA4977ED5A9B3CAF0876F00D2195309247F6481')
insert into @tableReferenceSponsors values (55252, '9E587F2AFBF67325016BD6329DA7FA6BA9F3CE144CB7C8F851FAD984C5226666')
insert into @tableReferenceSponsors values (55252, '6EB9CC246D86878546E886B07EA4977ED5A9B3CAF0876F00D2195309247F6481')

DECLARE @pBasketId int = 343731
*/




declare @existTable  int = (SELECT  count(*) FROM sys.tables WHERE name ='sponsor_panier_entrees')
if(@existTable > 0)
BEGIN

    select * from sponsor_panier_entrees  spe
    inner join panier_entree pe on spe.panier_entree_id = pe.panier_entree_id
    inner join panier p on p.panier_id = pe.panier_id 
    where  p.etat in ('C', 'P') 
    and reference_sponsor in (select referenceSponsor from @tableReferenceSponsors)
    and  p.panier_id != @pBasketId

END