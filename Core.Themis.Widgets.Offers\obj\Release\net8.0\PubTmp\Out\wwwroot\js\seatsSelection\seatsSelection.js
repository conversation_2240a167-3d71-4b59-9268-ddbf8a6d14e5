﻿
$(document).ready(function () {

    if ($('#sessionSelect option').length > 0) {

        var firstSession = $('#sessionSelect option:first').val();
        sessionChangedAjax(parseInt(firstSession));

        $('#sessionSelect').on('change', function () {
            sessionChangedAjax(parseInt($(this).val()));
        });
    }
});
function sessionChangedAjax(sessionId) {
    jQuery.ajax({
        type: "GET",
        // url: sWOffersUrl + "CategPriceSelectionAjax/@ViewBag.StructureId/fr/@ViewBag.EventId/" + sessionId + "/123/456/789?token=@ViewBag.PartnerToken",
        url: sWOffersUrl + "CategPriceSelectionAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "?token=" + partnerToken,
        success: function (data) {
            $("#wdgInsertCategPriceSelection").html(data);
            sendIframeSize();

            loadGrilleTarif(sessionId);
            // charge le plan
            loadPlan(sessionId);
        },
        error: function (a, b, c) {
            console.log("CategPriceSelectionAjax -> Error")
            jQuery("#wdgInsertCategPriceSelection").html(a.responseText);
        }
    });




    //$("#wdgInsertCategPriceSelection").load('/CategPriceSelectionAjax/991/fr/@ViewBag.EventId/' + sel.value + '/123/456/789?token=@ViewBag.PartnerToken');

};


function loadGrilleTarif(sessionId) {
  
    var urlSeatPlan = widgetCatalogUrl + "SeatsPlanAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "?"
    jQuery.ajax({
        type: "GET",
        url: urlSeatPlan,
        success: function (data) {
            $("#wdgInsertPlanSalle").html(data);

        },
        error: function (a, b, c) {
            console.log("loadZoneEtageSection -> Error")
            jQuery("#wdgInsertZoneEtageSection").html(a.responseText);
        }
    });
}
function loadPlan(sessionId) {
    // var urlSeatPlan = "@ViewBag.WCatalogUrl" + "SeatsPlanAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "?"
    //urlSeatPlan += "token=@ViewBag.PartnerToken"

    var urlSeatPlan = widgetCatalogUrl + "SeatsPlanAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "?"
    urlSeatPlan += "token=" + partnerToken;
    urlSeatPlan += "&ctg=" + listCategs.join('&ctg=');
    /* for (var catg in listCategs) {
         urlSeatPlan += "&ctg=" + listCategs[catg];
     }*/

    jQuery.ajax({
        type: "GET",
        url: urlSeatPlan,
        success: function (data) {
            $("#wdgInsertPlanSalle").html(data);

            // charge le plan
            sendIframeSize()
        },
        error: function (a, b, c) {
            console.log("CategPriceSelectionAjax -> Error")
            jQuery("#wdgInsertPlanSalle").html(a.responseText);
        }
    });
}
