/* cancelOrder.sql */

DECLARE @OkEtat int 
DECLARE @SQL VARCHAR(max)
DECLARE @Commande_ID int
DECLARE @Manif_Id int 
DECLARE @Seance_ID int
DECLARE @Dossier_id int
DECLARE @Dossier_Etat varchar(10)
DECLARE @Type_Ligne varchar(10)
DECLARE @Operateur_ID int
DECLARE @NumPaiement int
DECLARE @TransRemb int
DECLARE @TransFact int
DECLARE @TransMacptRemb int
DECLARE @TransRembAcpte int
DECLARE @TransAnnul int
DECLARE @TransRembPro int
DECLARE @TransAnnulPro int
DECLARE @IsStatuca int
Declare @Dossier_V int 
Declare @FacturePayee int
DECLARE @Solde decimal(18,10)
DECLARE @Identite_ID int
declare @NbPassage int
 --declare @pOrderId int = 5498
 --declare @pOperatorid int = 9920001

SET @Commande_ID = @pOrderId
SET @Operateur_ID = @pOperatorid

set @NbPassage = 0

--- Procedure Annulation Total d'une commande

--- On vérifie que l'état global de la commande est soit 'P' ou 'B' . Cela évite des pb lors de l'annulation de commandes dont les factures sont partiellement annulées
SET @OkEtat = (SELECT  sum(case etat when  'P' then 0 when  'B' then 0 else 1 end ) FROM commande_ligne_comp WHERE commande_id = @Commande_ID )

IF @OkEtat = 0 -- Si on ne relève aucun annulé ou réservé, on procède à l'annulation
	BEGIN
	
		IF (@Operateur_ID = 0)
		BEGIN
			/* l'operateur n'a pas été determiné en amont, todo aller le chercher ici */
			select top 1 @Operateur_ID = operateur_id FROM commande_ligne_comp WHERE commande_id = @Commande_ID AND etat not IN ('R','A')
			
		END
	

		DECLARE Curseur  cursor for
		SELECT  cl.Manifestation_ID , cl.Seance_id,cl.Dossier_id,clc.Etat,  cl.type_ligne from commande_ligne_comp clc inner join commande_ligne cl on cl.commande_ligne_id = clc.commande_ligne_id 
			WHERE cl.commande_id = @Commande_id and clc.etat in ('P','B')
		
		OPEN curseur
		
		FETCH curseur into @Manif_Id,@Seance_ID,@Dossier_id,@Dossier_Etat,@Type_Ligne
	
		--génération de la requête boucle
		--Les annulations se font Dossier par Dossier. Un DEBITFAC et un CREDIFAC aura toujours un N° de Commande et de dossier. Je rembourse au moment où on tombe sur le combo Dossier_ID-Commande_ID
		WHILE @@fetch_status = 0 
			BEGIN
				
				if @NbPassage = 0 --- numPaiement unique pour toute l'annulation. Sera repris pour mettre à jour DossierSVG et Compte_Client. 
					begin
						SET @NumPaiement = (SELECT TOP 1 compteur+1 from cpt_paiement) 
						UPDATE cpt_paiement SET Compteur=@NumPaiement 
					end
				--- Partie 1 Dossierd	
				if @Type_Ligne = 'DOS'
					begin

					SET @IsStatuca = (select count(*) from sys.columns where name = 'statutca')

					--- Recette. On doit gérer la présence de la colonne StatutCa. D'où les 2 requêtes
					IF @IsStatuca =0 
						BEGIN
							set @SQL = 'INSERT INTO Recette
							SELECT [manifestation_id],[seance_id],[entree_id],'+ replace(str(@Operateur_id),' ','') + ',getdate(),''A'',[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4]
							,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10],[numbillet],[categorie_id],[externe],[type_tarif_id]
							,[dossier_id],[motif],[origine],[technologie]
							from recette where dossier_id = '+ replace(str(@dossier_id),' ','') + ' and Manifestation_id = '+ replace(str(@Manif_Id),' ','') + ' and Seance_ID = '+ replace(str(@Seance_ID),' ','') + ' and type_operation = ''E'''
							print @SQL
							EXEC (@SQL)
						end 
					if @IsStatuca >0 
						BEGIN
							set @SQl = 'INSERT INTO Recette
							SELECT [manifestation_id],[seance_id],[entree_id],'+ replace(str(@Operateur_id),' ','') + ',getdate(),''A'',[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4]
							,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10],[numbillet],[categorie_id],[externe],[type_tarif_id]
							,[dossier_id],[motif],[origine],[technologie],statutca
							FROM recette where dossier_id = '+ replace(str(@dossier_id),' ','') + ' and Manifestation_id = '+ replace(str(@Manif_Id),' ','') + ' and Seance_ID = '+ replace(str(@Seance_ID),' ','') + ' and type_operation = ''E'''
							print @SQL
							EXEC (@SQL)
						END
					--- Le dossier 
					set @SQL = 'UPDATE Dossier_'+ replace(str(@Manif_id),' ','') + ' 
					SET Dossier_v = Dossier_v+1 
					,operateur_id =  '+ replace(str(@Operateur_id),' ','') + ' 
					,Dossier_etat = ''A'', date_operation = getdate()
					,num_paiement = '+ replace(str(@NumPaiement),' ','') + ' 
					WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
					and dossier_id = '+ replace(str(@dossier_id),' ','') + ''

					EXEC(@SQL)		

				
					--- L'historigue du Dossier. On gère différement en fonction du statut du dossier ( P ou B )
					if @Dossier_Etat = 'B'
						begin

							set @SQL = 'INSERT INTO Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
							SELECT 
							[dossier_id],[identite_id],[commande_id],[abon_manif_id],[seance_id]
							, ( select dossier_v from Dossier_'+ replace(str(@Manif_id),' ','') + ' where  seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
										and dossier_id = '+ replace(str(@dossier_id),' ','') + ')
							, '+ replace(str(@Operateur_id),' ','') + '
							,[dossier_montant]
							,[dossier_montant1],[dossier_montant2],[dossier_montant3],[dossier_montant4]
							,[dossier_montant5],[dossier_montant6],[dossier_montant7]
							,[dossier_montant8],[dossier_montant9],[dossier_montant10]
							,[dossier_c]
							,''A''
							,[dossier_icone]
							,[dossier_numero]
							,[dossier_nbplace]
							,[dossier_montantpayer]
							,[dossier_facture]
							,[dossier_client_nom]
							,'+ replace(str(@NumPaiement),' ','') + ' 
							,getdate()
							,''REMB''
							,[filiere_id]
							FROM Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
							WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
							and dossier_id = '+ replace(str(@dossier_id),' ','') + '
							and dossier_etat = ''B'' and Type_operation = ''EDIT''
							'
							print @SQL
							EXEC(@SQL)
						end
					if @Dossier_Etat = 'P'
						begin

							set @SQL = 'INSERT INTO Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
							SELECT 
							[dossier_id],[identite_id],[commande_id],[abon_manif_id],[seance_id]
							,( select dossier_v from Dossier_'+ replace(str(@Manif_id),' ','') + ' where  seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
										and dossier_id = '+ replace(str(@dossier_id),' ','') + ')
							, '+ replace(str(@Operateur_id),' ','') + '
							,[dossier_montant]
							,[dossier_montant1],[dossier_montant2],[dossier_montant3],[dossier_montant4]
							,[dossier_montant5],[dossier_montant6],[dossier_montant7]
							,[dossier_montant8],[dossier_montant9],[dossier_montant10]
							,[dossier_c]
							,''A''
							,[dossier_icone]
							,[dossier_numero]
							,[dossier_nbplace]
							,[dossier_montantpayer]
							,[dossier_facture]
							,[dossier_client_nom]
							,'+ replace(str(@NumPaiement),' ','') + ' 
							,getdate()
							,''REMB''
							,[filiere_id]
							FROM Dossiersvg_'+ replace(str(@Manif_id),' ','') + '
							WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
							and dossier_id = '+ replace(str(@dossier_id),' ','') + '
							and dossier_etat = ''P'' and Type_operation = ''PAIE''
							'
							print @SQL
							EXEC(@SQL)
						end
					--- Suppression des places et du N° de dossier dans la table Entree
					set @SQL = 'UPDATE Entree_'+ replace(str(@Manif_id),' ','') + '
					SET Entree_etat = ''L'', Dossier_id = 0,dateoperation = getdate()
					WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
					and dossier_id = '+ replace(str(@dossier_id),' ','') + ''
				
					EXEC(@SQL)

					--- L'histo des entrées.  Normalement, la gestion est identique pour les P et les B, mais je préfère séparer okazou.
					if @Dossier_Etat = 'B'
						begin
							SET @SQL = 'INSERT INTO Entreesvg_'+ replace(str(@Manif_id),' ','') + '
							SELECT [entree_id],[seance_id],[dossier_id], ''L'',[categorie_id]
							,[type_tarif_id]
							,'+ replace(str(@Operateur_id),' ','') + '
							,[ValeurTarifStockVersion]
							,[numero_billet]
							,[alotissement_id]
							,[reserve_id]
							,[contingent_id]
							,[montant1],[montant2],[montant3],[montant4]
							,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10]
							,getdate(), ( select dossier_v from Dossier_'+ replace(str(@Manif_id),' ','') + ' where  seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
												and dossier_id = '+ replace(str(@dossier_id),' ','') + ')
							FROM Entreesvg_'+ replace(str(@Manif_id),' ','') + '
							WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
							and dossier_id = '+ replace(str(@dossier_id),' ','') + '
							and entree_etat = ''B''						
					'
					end
					exec(@SQL)

					if @Dossier_Etat = 'P'
						begin
							SET @SQL = 'INSERT INTO Entreesvg_'+ replace(str(@Manif_id),' ','') + '
							SELECT [entree_id],[seance_id],[dossier_id], ''L'',[categorie_id]
							,[type_tarif_id]
							,'+ replace(str(@Operateur_id),' ','') + '
							,[ValeurTarifStockVersion]
							,[numero_billet]
							,[alotissement_id]
							,[reserve_id]
							,[contingent_id]
							,[montant1],[montant2],[montant3],[montant4]
							,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10]
							,getdate(), ( select dossier_v from Dossier_'+ replace(str(@Manif_id),' ','') + ' where  seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
												and dossier_id = '+ replace(str(@dossier_id),' ','') + ')
							FROM Entreesvg_'+ replace(str(@Manif_id),' ','') + '
							WHERE seance_id = '+ replace(str(@Seance_ID),' ','') + ' 
							and dossier_id = '+ replace(str(@dossier_id),' ','') + '
							and entree_etat = ''P''						
					'
					end
					exec(@SQL)
				end

				--- Partie 2 Les produits
				if @Type_Ligne = 'PRO'
					begin
						
						--- Dossier Produit
						Update Dossier_produit set dos_prod_etat = 'A' , dos_prod_v = dos_prod_v+1 ,operateur_id= @Operateur_ID ,date_operation = getdate()
						,num_paiement = @NumPaiement
						where dos_prod_id = @dossier_id and commande_id = @commande_id
						
						--- Histo des dossier_produit
						--insert into dossier_produitsvg 
						--select [dos_prod_id],[identite_id],[commande_id],[produit_id],[dos_prod_montant]
						--,[dos_prod_v]+1,@Operateur_ID,'A',[dos_prod_numero],[manifestation_id],[seance_id]
						--,@NumPaiement,[nb_produit],getdate(),[operation_id],[dos_prod_montant1],[dos_prod_montant2]
						--,[dos_prod_montant3],[dos_prod_montant4],[dos_prod_montant5],[dos_prod_montant6],[dos_prod_montant7]
						--,[dos_prod_montant8],[dos_prod_montant9],[dos_prod_montant10]
						--,[dos_prod_stock_id],[dossier_bill_id],[dossier_client_nom],[dossier_c],[dos_prod_fact]
						--from Dossier_produit where dos_prod_id = @dossier_id and commande_id = @commande_id

						--- Modif du stock
						update  produit_stock set restant = restant-1  where 
						produit_stock_id = (select dos_prod_stock_id from dossier_produit where dos_prod_id = @dossier_id and commande_id = @commande_id)

						--- Insertion des annulées dans la table recette_produit. Attention Produit_stock_id = Dos_Prod_ID
						insert into recette_produit 
						select [manifestation_id],[seance_id],[produit_stock_id],@operateur_id,getdate()
						,'A',[date_ouverture_caisse],[montant1],[montant2],[montant3],[montant4]
						,[montant5],[montant6],[montant7],[montant8],[montant9],[montant10]
						,[numbillet],[externe]
						from recette_produit where produit_stock_id = @Dossier_id				
						
						
					end

				-- Partie 4 : Mise à jour des tables centrales : Histodossier et Commande_ligne_comp


				UPDATE Commande_ligne_comp set etat = 'A' ,dossier_icone =1024
				WHERE seance_id = @Seance_ID and manifestation_id = @Manif_Id and dossier_id = @Dossier_id 

				INSERT INTO histodossier 
				SELECT [commande_id],[dossier_v]+1,[type],'A',getdate(),@Operateur_id,[manif_id],[seance_id],[dossier_id]
				FROM histodossier 
				WHERE seance_id = @Seance_ID and manif_id = @Manif_Id and dossier_id = @dossier_id


				--- Partie 5 Le compte Client. 
				declare @SommeFacturePayee decimal(18,10)
				declare @SommeFactureSolde decimal(18,10)

				-- On vérifie si la facture est tout ou partiellement soldée
				set @FacturePayee = (select count(*) from compte_client where commande_id = @commande_id and cc_intitule_operation = 'CREDITFAC')

				-- Attention. On traite les factures une par une. Si il y a une facture pour l'ensemble de la commande. Celle-ci sera traitée au moment du passage sur son dossier_id
				-- Si il y a plusieurs CREDITFAC sur le même dossier, ceux-ci seron traités avec un sum(montant) group by Commande ,Dossier
				if @FacturePayee > 0  
					begin
						set @SommeFacturePayee = (select sum(isnull(montant1,0)) from compte_client where commande_id = @Commande_id and dossier_id = @Dossier_id and seance_id = @Seance_id and cc_intitule_operation = 'CREDITFAC')
						set @SommeFactureSolde = (select sum(isnull(cc_solde,0)) from compte_client where commande_id = @Commande_id and dossier_id = @Dossier_id and seance_id = @Seance_id and cc_intitule_operation = 'DEBITFAC')
					end		
				if @FacturePayee = 0 
					begin
						set @SommeFacturePayee = 0
						set @SommeFactureSolde = (select sum(isnull(montant1,0)) from compte_client where commande_id = @Commande_id and dossier_id = @Dossier_id and seance_id = @Seance_id and cc_intitule_operation = 'DEBITFAC')
					end

					
								
				--- Ligne remboursement du dossier
				SET @TransRemb = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransRemb
				INSERT INTO compte_client 
				SELECT Identite_id,@TransRemb 
				,'REMBDOS',cc_credit,0,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
				,'REMBDOS',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITDOS'
				--- Ligne remboursement du dossier_Produit
				SET @TransRembPro = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransRembPro
				INSERT INTO compte_client 
				SELECT  Identite_id,@TransRembPro 
				,'REMBPRO',cc_credit,0,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
				,'REMBPRO',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITPRO'

				if @SommeFacturePayee >0 --- Si la facture est payée ...
					begin
						--- Je traite les montants payés . Attention le mode de paiement du remboursement est le même que pour le paiement de la facture. A moins qu'on définisse un mode de paiement spécifique.
						set @TransMacptRemb = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransMacptRemb
						INSERT INTO compte_client 
						SELECT Identite_id,@TransMacptRemb 
						,'MACPTREMB',0,cc_credit,0,@operateur_id,getdate(),mode_paiement_id,'','','',@Manif_id,@NumPaiement 
						,'MACPTREMB',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITFAC'

						set @TransRembAcpte = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransRembAcpte
						INSERT INTO compte_client 
						SELECT Identite_id,@TransRembAcpte 
						,'REMBACPTE',cc_credit,0,0,@operateur_id,getdate(),mode_paiement_id,'','','',@Manif_id,@NumPaiement 
						,'REMBACPTE',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITFAC'

						--- Insertion des liens des paiement/commande vers la table compte_transaction. Important pour les états détaillés par mode de paiement dans RodDerniere
						INSERT INTO compte_transaction values (@TransRemb,@TransMacptRemb,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
						INSERT INTO compte_transaction values (@TransRembPro,@TransMacptRemb,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0)

						if @SommeFactureSolde > 0 --- Si la facture est partiellement payée, il faut annuler le reste-à-payer en DECHIRFAC (Annulation de Facture)
							begin
								set @TransFact = (SELECT TOP 1 compteur+1 from cpt_transaction) 
								UPDATE cpt_transaction SET Compteur=@TransFact
								INSERT INTO compte_client 
								SELECT Identite_id,@TransFact 
								,'DECHIRFAC',0,@SommeFactureSolde,0,@operateur_id,getdate(),mode_paiement_id,'','','',@Manif_id,@NumPaiement 
								,'DECHIRFAC',0,@seance_id,@dossier_id,@SommeFactureSolde,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
								from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC'

								update compte_client set cc_solde = 0 where commande_id =@commande_id and cc_intitule_operation = 'DEBITFAC' and dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID

								INSERT INTO compte_transaction values (@TransRemb,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
								INSERT INTO compte_transaction values (@TransRembPro,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0)
							end

					end
				if @SommeFacturePayee =0 --- Si la facture n'est pas payée, on génère une ligne d'annulation dont le montant est le montant Total de la facture
					begin
						set @TransFact = (SELECT TOP 1 compteur+1 from cpt_transaction) 
						UPDATE cpt_transaction SET Compteur=@TransFact
						INSERT INTO compte_client 
						SELECT Identite_id,@TransFact 
						,'DECHIRFAC',0,cc_debit,0,@operateur_id,getdate(),mode_paiement_id,'','','',@Manif_id,@NumPaiement 
						,'DECHIRFAC',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
						from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC'

						update compte_client set cc_solde = 0 where commande_id =@commande_id and cc_intitule_operation = 'DEBITFAC' and dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID

						
						INSERT INTO compte_transaction values (@TransRemb,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
						INSERT INTO compte_transaction values (@TransRembPro,@TransFact,'',@operateur_id,getdate(),'REMB',0,0,0,'',0,0,0,0,0,0,0,0) 
					
					end

				--- Ligne d'annulation du dossier (contrebalance comptable de la ligne de remboursement du dossier)
				set @TransAnnul = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransAnnul
				INSERT INTO compte_client 
				SELECT Identite_id,@TransAnnul 
					,'ANNULDOS',0,cc_credit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
					,'ANNULDOS',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				from compte_client where  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITDOS'
				--- Idem pour les produits
				set @TransAnnulPro = (SELECT TOP 1 compteur+1 from cpt_transaction) 
				UPDATE cpt_transaction SET Compteur=@TransAnnulPro
				INSERT INTO compte_client 
				SELECT Identite_id,@TransAnnulPro 
					,'ANNULPRO',0,cc_credit,0,@operateur_id,getdate(),0,'','','',@Manif_id,@NumPaiement 
					,'ANNULPRO',0,@seance_id,@dossier_id,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,@Commande_id,0,0
				FROM compte_client WHERE  dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'CREDITPRO'


				--- Mise à jour du solde de la fiche client
				SET @Solde = (select sum(cc_debit) from compte_client where dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC')
				SET @Identite_ID = (select identite_id from compte_client where dossier_id = @Dossier_id and Manif_id = @Manif_Id and Seance_ID = @Seance_ID and cc_intitule_operation = 'DEBITFAC')

				UPDATE identite set montant_debit =  montant_debit - @Solde WHERE identite_id = @Identite_ID				 
			    
				set @NbPassage =@NbPassage +1

				FETCH curseur into @Manif_Id,@Seance_ID,@Dossier_id,@Dossier_Etat,@Type_Ligne
				
			END
		CLOSE curseur
		DEALLOCATE curseur  
	END

