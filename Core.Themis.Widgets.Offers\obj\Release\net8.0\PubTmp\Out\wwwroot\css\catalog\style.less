/* Url de la font globale sur le site */
@globalFontUrl: "https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900";

/* Nom de la font globale sur le site */
@globalFontName: "Roboto";

/* couleur de textes */
@globalFontColor: #333333;

/* couleur des lien */
@globalLinkColor: #000000;

/* couleur de background */
@globalBackgroundColor: f8f8f8;

/* couleur principale */
@primaryColor: #000000;

/* couleur de texte des boutons primaire */
@btnPrimaryTextColor: #ffffff;

/* couleur secondaire */
@secondaryColor: #5F5F5F;

/* couleur de texte des boutons secondaire */
@btnSecondaryTextColor: #ffffff;

/* import font custom  */
& when not(@globalFontUrl ="") {
    @import (less) "@{globalFontUrl}";
}

html, body { font-family: Roboto, Verdana, Arial, Helvetica, sans-serif; padding: 0; margin: 0; font-size: 13px; }
body when not (@globalFontName ="") { font-family: "@{globalFontName}", "Helvetica Neue", Helvetica, Verdana, Trebuchet MS; }

//bs4 fix
:root { --bs-border-radius: 0.375rem; --bs-border-width: 1px; }

each(range(0,5), {
    @opacityrange : (@value * 25);
  .opacity-@{opacityrange} {
    opacity: (@opacityrange / 100) !important;
  }
});

each(range(0,5), {
  .gap-@{value} {
    gap: (@value * 0.25rem) !important;
  }
});

each(range(0,5), {
  .gapy-@{value} {
    gap: (@value * 0.25rem) 0 !important;
  }
});

each(range(0,5), {
  .gapx-@{value} {
    gap: 0 (@value * 0.25rem) !important;
  }
});


hr { margin: 1rem 0; color: inherit; border: 0; border-top: var(--bs-border-width) solid; opacity: .25; }
.ratio { position: relative; width: 100%;

    &::before { display: block; padding-top: var(--bs-aspect-ratio); content: ""; }

    > * { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
}

.ratio-1x1 { --bs-aspect-ratio: 100%; }
.ratio-4x3 { --bs-aspect-ratio: calc(3 / 4* 100%); }
.ratio-16x9 { --bs-aspect-ratio: calc(9 / 16* 100%); }
.ratio-21x9 { --bs-aspect-ratio: calc(9 / 21* 100%); }
.nav-underline { --bs-nav-underline-gap: 2rem; --bs-nav-underline-border-width: 0.125rem; --bs-nav-underline-link-active-color: @globalFontColor;  }
.nav-link:hover { cursor: pointer; }



.btn-primary { color: contrast(@primaryColor); background-color: @primaryColor; border-color: @primaryColor; }

.btn-primary:not([disabled]):hover, .btn-primary:not([disabled]):active, .btn-primary:not([disabled]):focus { color: contrast(darken(@primaryColor, 20%)); background-color: darken(@primaryColor, 20%); border-color: darken(@primaryColor, 20%); }

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle { color: contrast(darken(@primaryColor, 20%)); background-color: darken(@primaryColor, 20%) }

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus { }

.btn-primary .badge { color: contrast(@primaryColor); background-color: @primaryColor; }

.btn-secondary { color: contrast(@secondaryColor); background-color: @secondaryColor; border-color: @secondaryColor; }

.btn-secondary:not([disabled]):hover, .btn-secondary:not([disabled]):active, .btn-secondary:not([disabled]):focus,
.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show > .btn-secondary.dropdown-toggle { color: contrast(darken(@secondaryColor, 20%)); background-color: darken(@secondaryColor, 20%); border-color: darken(@secondaryColor, 20%) }

.btn-outline-secondary { color: @secondaryColor; border-color: @secondaryColor; }
.btn-outline-secondary:hover, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle { color: contrast(@secondaryColor); background-color: @secondaryColor; border-color: @secondaryColor; }
.btn-link { color: @primaryColor; background-color: #f0f0f0; border-color: #d9d9d9 }

.btn-link:not([disabled]):hover, .btn-link:not([disabled]):active, .btn-link:not([disabled]):focus { background-color: #aaaaaa; border-color: #d9d9d9 }

.btn[disabled], .btn.disabled, .btn.disabled:hover, .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active { color: #fff; background-color: #aaaaaa; border-color: #878787 }
.btn.focus, .btn:focus {
    box-shadow: 0 0 0 0;
}
.btn { border-radius: 1000px; }
.btn:not([disabled]):hover, .btn:not([disabled]):active, .btn:not([disabled]):focus { text-decoration: none; cursor: pointer }

.btn-check { position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none; }
.btn-check:active + .btn-outline-dark, .btn-check:checked + .btn-outline-dark { color: #fff; background-color: #212529; border-color: #212529; }
.rounded { border-radius: var(--bs-border-radius) !important; }
//
.filter_crit_1 { font-size: 2rem; }

.filter_crit_2 { font-size: 1.2rem; }

.filter_crit_1 .nav-link, .filter_crit_2 .nav-link { 
    padding: .75rem 1rem;
    background-color: transparent;
    border-radius: 10rem;
    transition: background-color 0.2s;
    line-height: 1rem;
    position:relative
}

.filter_crit_1 .nav-link:not(.active):hover, .filter_crit_2 .nav-link:not(.active):hover {
    background-color:rgba(0,0,0,.07);
}
.nav-underline .nav-link:after { content:""; padding-right: 0; padding-left: 0; background-color: transparent;width: calc(100% - 2rem);
    display: block;
    height: 1px;
    bottom:0;
    position: absolute;
}
.nav-underline .nav-link.active {
    font-weight: 700;
}
.nav-underline .nav-link.active:after, .nav-underline .show > .nav-link:after {   background-color: var(--bs-nav-underline-link-active-color); }


.filter_tag { background-color: #000; color: #fff; padding: 0.2rem 0.5rem; border-radius: 3px; }
.filter_tag i:hover {cursor:pointer;}
.filter_by_dates { border: 1px solid #ced4da !important; background-color: #fff !important; color: #495057 !important; }
.eventscatalog_one + .eventscatalog_one {
    margin-top:60px;
}
.eventscatalog_all.grid { -ms-grid-columns: (minmax(0, 1fr))[5]; grid-template-columns: repeat(5, minmax(0, 1fr));display: -ms-grid; display: grid; gap: 1.5rem; }

.eventscatalog_all .badge { font-weight: 400; --bs-badge-font-weight: 400; line-height: 1rem; }
.eventscatalog_all.grid .event_one { padding-left: 0; padding-right: 0; }
.eventscatalog_all.grid .event_one:hover { cursor: pointer; z-index: 5; }
.eventscatalog_all.grid .event_one:hover .event_one_back { pointer-events: auto; }
.eventscatalog_all .event_one .event_one_back { pointer-events: none; }

@media (max-width:1399.98px) {
    .eventscatalog_all.grid { -ms-grid-columns: (minmax(0, 1fr))[4]; grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (max-width:1199.98px) {
    .eventscatalog_all.grid { -ms-grid-columns: (minmax(0, 1fr))[3]; grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (max-width:767.98px) {
    .eventscatalog_all { display: grid; gap: 1.5rem; }
    .eventscatalog_all { -ms-grid-columns: (minmax(0, 1fr))[2]; grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
    .eventscatalog_all .event_one .event_one_front_list { display: none; }
    .eventscatalog_all .event_one {padding-left: 0; padding-right: 0; }
}

@media (min-width:768px) {
    .eventscatalog_all.grid .event_one .event_one_front_list { display: none; }
    .eventscatalog_all.list .event_one .event_one_front, .eventscatalog_all.list .event_one .event_one_back { display: none; }
    .eventscatalog_all.list .event_one { border-bottom: 1px solid #ccc; padding-bottom: 0.75rem; margin-bottom: 0.75rem; }
}

.eventscatalog_all .event_one .event_one_back { position: absolute; }
.event_one .event_one_front, .event_one .event_one_back { -webkit-transition: all 0.3s; transition: all 0.3s; }
.event_one .event_one_front { border: 1px solid transparent; }
.event_one .event_one_back { right: 0; left: 0; top: 0; opacity: 0; background-color: #fff; border: 1px solid #ccc; color: #000; overflow: hidden; box-shadow: 0px 5px 0px #fff; }
.event_one .event_one_back_inner_top { padding-bottom: 2.2rem; padding-left: 1rem; padding-right: 1rem; padding-top: 1rem; }
.event_one .event_one_back_inner_bottom { padding-bottom: 1rem; padding-left: 1rem; padding-right: 1rem; padding-top: 1.2rem; }
.event_one_front .event_one_cta, .event_one_back .event_one_cta { position: absolute; -webkit-transform: translate(0, 33.33%); -ms-transform: translate(0, 33.33%); transform: translate(0, 33.33%); bottom: 0; text-align: center; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-pack: center; -ms-flex-pack: center; justify-content: center; -webkit-box-align: end; -ms-flex-align: end; align-items: end; }
.event_one_cta_background { content: ''; position: absolute; z-index: 1; bottom: 0; left: 0; height: 50%; background-color: #fff; width: 100%; display: block; }
.event_one_front .event_one_cta .btn { position: relative; z-index: 2; }
.event_one_front .event_one_cta:before, .event_one_front .event_one_cta:after { content: ''; display: block; width: 11px; aspect-ratio: 1; -ms-flex-negative: 0; flex-shrink: 0; position: absolute; z-index: 4; bottom: 33.33%; }
.event_one_front .event_one_cta:before { background: url(../images/angle_button_white.svg) no-repeat bottom center; left: 0; -webkit-transform: translate(calc(-100% + 1px), 1px); -ms-transform: translate(calc(-100% + 1px), 1px); transform: translate(calc(-100% + 1px), 1px); }
.event_one_front .event_one_cta:after { background: url(../images/angle_button_white.svg) no-repeat bottom center; -webkit-transform: scaleX(-1) translate(calc(-100% + 1px), 1px); -ms-transform: scaleX(-1) translate(calc(-100% + 1px), 1px); transform: scaleX(-1) translate(calc(-100% + 1px), 1px); right: 0; }

.event_one .eventunavailable {
    opacity: 0.65;
    filter: grayscale(50%);
}
@media (min-width:768px) {
    .event_one:hover .event_one_back { opacity: 1; }
}

@media (max-width:991.98px) {
    .eventscatalog_all.grid .btn-buy, .eventscatalog_all.grid .btn-waitlinglist { --bs-btn-padding-y: 0.25rem; --bs-btn-padding-x: 0.5rem; --bs-btn-font-size: 0.875rem; --bs-btn-border-radius: var(--bs-border-radius-sm); }
}

@media (max-width:767.98px) {
    .btn-buy, .btn-waitlinglist { --bs-btn-padding-y: 0.25rem; --bs-btn-padding-x: 0.5rem; --bs-btn-font-size: 0.875rem; --bs-btn-border-radius: var(--bs-border-radius-sm); }
    .eventscatalog_all .btn-buy, .eventscatalog_all .btn-waitlinglist, .eventscatalog_all .btn-buy:hover, .eventscatalog_all .btn-waitlinglist:hover { border: 3px solid #fff }

}

.eventscatalog_all.grid .btn-buy, .eventscatalog_all.grid .btn-waitlinglist, .eventscatalog_all.grid .btn-buy:hover, .eventscatalog_all.grid .btn-waitlinglist:hover { border: 3px solid #fff }
.eventscatalog_all.grid .event_one_back .btn-buy, .eventscatalog_all.grid .event_one_back .btn-waitlinglist, .eventscatalog_all.grid .event_one_back .btn-buy:hover, .eventscatalog_all.grid .event_one_back .btn-waitlinglist:hover { border: 3px solid #fbfbfb }
.btn-buy .fa-ticket { -webkit-transform: rotate(-45deg); -ms-transform: rotate(-45deg); transform: rotate(-45deg); }
.event_one_badge_full, .event_one_badge_locked { padding: 0.2rem 0.5rem; border-radius: 0.25rem; margin-bottom: 0.25rem; display: inline-block; background-color: #dc3545; color: #fff; }