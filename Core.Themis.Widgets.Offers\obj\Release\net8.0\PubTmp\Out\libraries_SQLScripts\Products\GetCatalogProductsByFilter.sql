﻿--DECLARE @pGroupIds VARCHAR(50) = '';
--DECLARE @pProductFamilyIds VARCHAR(50) = '4';
--DECLARE @pProductSubFamilyIds VARCHAR(50) = '';
--DECLARE @pSearchTerm VARCHAR(MAX) = '';
--DECLARE @pColors VARCHAR(MAX) = 'rouge,bleu,rose';
--DECLARE @pSizes VARCHAR(MAX) = 'm,s';
--DECLARE @pSortBy VARCHAR(MAX) = 'namedesc';
--DECLARE @pLangCode VARCHAR(5) = 'fr';

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode);

DECLARE @TableProductShopWithParams TABLE (name VARCHAR(200) NOT NULL)

INSERT INTO @TableProductShopWithParams
	SELECT DISTINCT p.produit_nom FROM produit p
	INNER JOIN Produit_Lien_Sous_Famille plsf ON p.produit_id = plsf.Produit_id
    INNER JOIN Produit_Famille pf ON plsf.Produit_Famille_ID = pf.Produit_Famille_ID
    INNER JOIN Produit_Sous_Famille psf ON plsf.Produit_Sous_Famille_ID = psf.Produit_Sous_Famille_ID
	WHERE p.internet = 1
	AND pf.Masquer = 0
	AND psf.Masquer = 0
	AND psf.Regroupement_couleur = 1;

WITH ProductsCTE AS (
    SELECT DISTINCT
        p.produit_id AS ProductId,
        p.groupe_id AS GroupId,
        IIF(tn.Traduction = '' OR tn.Traduction IS NULL, p.produit_nom, tn.traduction COLLATE database_default) AS Name,
        IIF(td.Traduction = '' OR td.Traduction IS NULL, p.produit_descrip, td.traduction COLLATE database_default) AS ShortDescription,
        p.vts_grille1 + p.vts_grille2 +
        CASE WHEN s.modecol4 = 'REMISE' THEN -p.vts_grille4 WHEN s.modecol4 = 'TAXE' THEN p.vts_grille4 ELSE 0 END +
        CASE WHEN s.modecol5 = 'REMISE' THEN -p.vts_grille5 WHEN s.modecol5 = 'TAXE' THEN p.vts_grille5 ELSE 0 END +
        CASE WHEN s.modecol6 = 'REMISE' THEN -p.vts_grille6 WHEN s.modecol6 = 'TAXE' THEN p.vts_grille6 ELSE 0 END +
        CASE WHEN s.modecol7 = 'REMISE' THEN -p.vts_grille7 WHEN s.modecol7 = 'TAXE' THEN p.vts_grille7 ELSE 0 END +
        CASE WHEN s.modecol8 = 'REMISE' THEN -p.vts_grille8 WHEN s.modecol8 = 'TAXE' THEN p.vts_grille8 ELSE 0 END +
        CASE WHEN s.modecol9 = 'REMISE' THEN -p.vts_grille9 WHEN s.modecol9 = 'TAXE' THEN p.vts_grille9 ELSE 0 END +
        CASE WHEN s.modecol10 = 'REMISE' THEN -p.vts_grille10 WHEN s.modecol10 = 'TAXE' THEN p.vts_grille10 ELSE 0 END AS Price,
        psf.Regroupement_couleur AS UseParams,
		CASE WHEN fp.calcul_validity_fixed_date IS NOT NULL THEN fp.calcul_validity_fixed_date WHEN acp.Propriete_Code = 'VALIDITE_UTILISATION_DATES' THEN acp.Propriete_Valeur_Date2 ELSE NULL END AS ValidityDate,
		CASE WHEN fp.calcul_validity_delay_days IS NOT NULL AND fp.calcul_validity_delay_days > 0 THEN fp.calcul_validity_delay_days WHEN acp.Propriete_Code = 'VALIDITE_UTILISATION_TEMPS' THEN acp.Propriete_Valeur_Int1 ELSE NULL END AS ValidityPeriod,
		IIF(acp.Propriete_Code = 'VALIDITE_UTILISATION_TEMPS', acp.Propriete_Valeur_Char1, NULL) AS TimeUnit,
        fd.count AS TicketsNumber,
        CASE WHEN psf.Regroupement_couleur = 1 THEN (SELECT name FROM splitstring(p.produit_nom, ' ') WHERE rownum = 2) ELSE NULL END AS ProductSize,
        CASE WHEN psf.Regroupement_couleur = 1 THEN (SELECT name FROM splitstring(p.produit_nom, ' ') WHERE rownum = 1) ELSE NULL END AS ProductColor,
        CASE WHEN psf.Regroupement_couleur = 1 THEN
            (SELECT STUFF(
				(SELECT DISTINCT ', ' +
					(SELECT name FROM splitstring(p2.name, ' ') WHERE splitstring.rownum = 2)
				FROM @TableProductShopWithParams p2
				WHERE
					(SELECT name FROM splitstring(p.produit_nom, ' ') WHERE splitstring.rownum = 0) =
					(SELECT name FROM splitstring(p2.name, ' ') WHERE splitstring.rownum = 0)
				FOR XML PATH(''))
			, 1, 2, ''))
        ELSE NULL END AS OtherSizes,
        CASE WHEN psf.Regroupement_couleur = 1 THEN
            (SELECT STUFF(
				(SELECT DISTINCT ', ' +
					(SELECT name FROM splitstring(p2.name, ' ') WHERE splitstring.rownum = 1)
				FROM @TableProductShopWithParams p2
				WHERE
					(SELECT name FROM splitstring(p.produit_nom, ' ') WHERE splitstring.rownum = 0) =
					(SELECT name FROM splitstring(p2.name, ' ') WHERE splitstring.rownum = 0)
				FOR XML PATH(''))
			, 1, 2, ''))
        ELSE NULL END AS OtherColors
    FROM structure s, produit p
    LEFT JOIN traduction_langue tn ON tn.traduction_id = p.produit_nom_id AND tn.langue_id = @langueId
    LEFT JOIN traduction_langue td ON td.traduction_id = p.produit_descrip_id AND td.langue_id = @langueId
    LEFT JOIN feedbook_produit fp ON fp.produit_id = p.produit_id
    LEFT JOIN feedbooks_description fd ON fd.feedbook_produit_id = p.produit_id
	LEFT JOIN Adhesion_Catalog ac ON ac.Produit_ID = p.produit_id
	LEFT JOIN Adhesion_Catalog_Propriete acp ON acp.Adhesion_Catalog_ID = ac.Adhesion_Catalog_ID AND (acp.Propriete_Code = 'VALIDITE_UTILISATION_TEMPS' OR acp.Propriete_Code = 'VALIDITE_UTILISATION_DATES')
    LEFT JOIN Produit_Lien_Sous_Famille plsf ON p.produit_id = plsf.Produit_id
    LEFT JOIN Produit_Famille pf ON plsf.Produit_Famille_ID = pf.Produit_Famille_ID
    LEFT JOIN Produit_Sous_Famille psf ON plsf.Produit_Sous_Famille_ID = psf.Produit_Sous_Famille_ID
    WHERE
		p.internet = 1 
		AND ((@pGroupIds <> '' AND p.groupe_id IN (SELECT name FROM splitstring(@pGroupIds, ','))) 
		OR (@pProductFamilyIds <> '' AND @pProductSubFamilyIds = '' AND plsf.Produit_Famille_ID IN (SELECT name FROM splitstring(@pProductFamilyIds, ',')) AND pf.Masquer = 0) 
		OR(@pProductSubFamilyIds <> '' AND plsf.Produit_Famille_ID IN (SELECT name FROM splitstring(@pProductFamilyIds, ',')) AND plsf.Produit_Sous_Famille_ID IN (SELECT name FROM splitstring(@pProductSubFamilyIds, ',')) AND pf.Masquer = 0 AND psf.Masquer = 0))
)

SELECT *
FROM ProductsCTE
WHERE
    (@pSearchTerm = '' OR Name LIKE '%' + @pSearchTerm + '%') AND
    (@pColors = '' OR ProductColor IN (SELECT name FROM splitstring(@pColors, ','))) AND
    (@pSizes = '' OR ProductSize IN (SELECT name FROM splitstring(@pSizes, ',')))
ORDER BY
    CASE WHEN @pSortBy = 'nameasc' THEN CONCAT('N', Name) END ASC,
    CASE WHEN @pSortBy = 'namedesc' THEN CONCAT('N', Name) END DESC,
    CASE WHEN @pSortBy = 'priceasc' THEN Price END ASC,
    CASE WHEN @pSortBy = 'pricedesc' THEN Price END DESC;
    