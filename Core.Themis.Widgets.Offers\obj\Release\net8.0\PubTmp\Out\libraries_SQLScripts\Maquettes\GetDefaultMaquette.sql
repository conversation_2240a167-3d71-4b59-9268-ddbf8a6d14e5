﻿

DECLARE 
	@SEA INT = @pseanceid,
	@FILIEREID INT = 0,
	@OPERATEURID INT = 0,
	@TYPETARIFID INT = 0,
	@DOSSIERID INT = @pdossierId,
	@ENTREEID INT = @pentreeid


DECLARE @cmdId int
SELECT @cmdId = commande_id FROM commande_ligne cl WHERE seance_id = @sea AND dossier_id =@DOSSIERID

DECLARE @myCmd int = 0, @myManif int
SELECT @myCmd = commande_id, @myManif = manifestation_id from commande_ligne_comp where seance_id = @SEA and dossier_id = @DOSSIERID and dossier_icone <> 2816
if (@myCmd = 0)
begin
	select 0
end

CREATE TABLE #mytblEntrees (entree_id int) /* tabl temp des entrees */
DECLARE @sql varchar(max)
SET @sql='SELECT entree_id FROM entree_' + convert(varchar, @myManif) + ' WHERE dossier_id =' + convert(varchar, @DOSSIERID) + ' AND entree_etat=''B'' '
INSERT INTO #mytblEntrees EXECUTE (@sql)

SELECT top 1 maquette_id from (

SELECT Maquette_id, 0 as priorite, 0 as step /* recette_complement */
	FROM recette_complement rc
	INNER JOIN recette r on r.recette_id = rc.recette_id 
	INNER JOIN #mytblEntrees e on e.entree_id = r.entree_id 
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = rc.Maquette_id 
	WHERE r.dossier_id =@DOSSIERID AND seance_id = @SEA and manifestation_id =@myManif and maq.masquer ='N' and maq.TypeSupport =4
UNION
	SELECT  maquette_id as maquette_id, 1 as priorite, 1 as step /* link_abo_ferme_maquette */
	FROM commande_ligne cl
	INNER JOIN formule_abonnement fa on fa.form_abon_id = cl.formule_id
	INNER JOIN link_abo_ferme_maquette lafm on lafm.formule_id = fa.form_abon_id and cl.seance_id = lafm.seance_id_ref
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = lafm.Maquette_id 
	WHERE cl.Commande_id =@cmdId and cl.dossier_id =@DOSSIERID and maq.masquer ='N' and maq.TypeSupport =4
UNION
	SELECT gpte.maquette_billet_id as maquette_id, 1 as priorite, 2 as step  /* maquette eventuellement parametrée dans gestion_place_type_envoi tout public */
	FROM gestion_place gp, gestion_place_type_envoi gpte
	INNER join MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = gpte.Maquette_Billet_ID 
	WHERE gp.gestion_place_id = gpte.gestion_place_id 
	AND seance_id = @SEA
	AND type_tarif_id = @TYPETARIFID
	AND gpte.maquette_billet_id is not null and isContrainteIdentite = 0 and maq.masquer ='N' and maq.TypeSupport =4
UNION
	SELECT gpte.maquette_billet_id as maquette_id, 1 as priorite, 3 as step  /* maquette eventuellement parametrée dans gestion_place_type_envoi reliées à une offre */
	FROM gestion_place gp, gestion_place_type_envoi gpte
	INNER JOIN offre_gestion_place ogp on ogp.gestion_place_id = gpte.gestion_place_id
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = gpte.Maquette_Billet_ID 
	WHERE gp.gestion_place_id = gpte.gestion_place_id 
	AND seance_id = @SEA
	AND type_tarif_id = @TYPETARIFID
	AND gpte.maquette_billet_id is not null and isContrainteIdentite = 1 and maq.masquer ='N' and maq.TypeSupport =4
UNION
	SELECT rc.Maquette_id, 1 as priorite, 4 as step FROM recette r /* recette_complement (sans lien à la manif ????????) */
	INNER JOIN recette_complement rc on r.recette_id = rc.recette_id
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = rc.Maquette_ID 
	WHERE dossier_id = @DOSSIERID and seance_id = @SEA and maq.masquer ='N' and maq.TypeSupport =4

UNION
	SELECT * FROM ( /* top 1 priorite de maquette_param */
	SELECT TOP 1 mp.Maquette_id, 1 as priorite,5 as step 
	from MAQUETTE_PARAM mp	
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = mp.maquette_id 
	WHERE maq.masquer ='N' AND maq.TypeSupport =4 AND Type_element = 'OBTENTION' and maq.masquer = 'N' and type_maquette = 'B'
	AND type_table= 'S' and table_id = @SEA ORDER BY mp.PRIORITE
	) s
UNION
	SELECT maq_billet_id as maquette_id, 9999 as priorite, 7 as step  /* maquette par defaut sur la séance */
	FROM seance s 
	INNER JOIN MAQUETTE_BILLET m on
	m.Maquette_Billet_ID = s.maq_billet_id WHERE m.TypeSupport=4 and seance_id = @SEA 
UNION
	SELECT maquette_id, 9999 as priorite, 8 as step /* maquette dans Produit_PDF_LOG_ENVOI */
	FROM Produit_PDF_LOG_ENVOI pple 
	INNER JOIN MAQUETTE_BILLET maq on maq.Maquette_Billet_ID = pple.Maquette_ID 
	WHERE pple.Dossier_id = @DOSSIERID and Commande_id = @cmdId and EnvoiOK=1 and maq.masquer ='N' and maq.TypeSupport =4

) s ORDER BY priorite
DROP TABLE #mytblEntrees