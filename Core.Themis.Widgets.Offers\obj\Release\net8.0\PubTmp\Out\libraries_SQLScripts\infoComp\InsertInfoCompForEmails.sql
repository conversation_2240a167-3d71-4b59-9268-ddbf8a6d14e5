﻿/* InsertInfoCompForEmails.sql */
	SELECT identite_id 
	into #myIdentiteIds
	FROM identite WHERE postal_tel[EMAILCOL] in ([LISTEMAILS])
	
	UPDATE identite_infos_comp SET supprimer='N', valeur4='[VALEUR4]', datemodification=getdate() 
	WHERE identite_id IN (SELECT identite_id from #myIdentiteIds)
	AND info_comp_id=[INFOCOMPID];
	
	INSERT INTO identite_infos_comp (identite_id,info_comp_id,valeur1,valeur2,valeur3,valeur4,supprimer,datecreation,datemodification)
	SELECT identite_id, [INFOCOMPID],'','','','[VALEUR4]','N',getdate(),getdate()
	FROM #myIdentiteIds atid  WHERE identite_id not in (SELECT identite_id FROM identite_infos_comp WHERE info_comp_id=[INFOCOMPID])
	
	drop table #myIdentiteIds


