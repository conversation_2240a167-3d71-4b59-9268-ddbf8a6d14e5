/* _content/BlazorDateRangePicker/Calendar.razor.rz.scp.css */
.next span[b-m5gu62ugh9], .prev span[b-m5gu62ugh9] {
    color: #fff;
    border: solid black;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}

.next span[b-m5gu62ugh9] {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.prev span[b-m5gu62ugh9] {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

th[b-m5gu62ugh9], td[b-m5gu62ugh9] {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

table[b-m5gu62ugh9] {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

td.available:hover[b-m5gu62ugh9], th.available:hover[b-m5gu62ugh9] {
    background-color: #eee;
    border-color: transparent;
    color: inherit;
}

td.week[b-m5gu62ugh9], th.week[b-m5gu62ugh9] {
    font-size: 80%;
    color: #ccc;
}

td.disabled[b-m5gu62ugh9], option.disabled[b-m5gu62ugh9] {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

td.off[b-m5gu62ugh9], td.off.in-range[b-m5gu62ugh9], td.off.start-date[b-m5gu62ugh9], td.off.end-date[b-m5gu62ugh9] {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}

td.in-range[b-m5gu62ugh9] {
    background-color: #ebf4f8;
    border-color: transparent;
    color: #000;
    border-radius: 0;
}

td.start-date[b-m5gu62ugh9] {
    border-radius: 4px 0 0 4px;
}

td.end-date[b-m5gu62ugh9] {
    border-radius: 0 4px 4px 0;
}

td.start-date.end-date[b-m5gu62ugh9] {
    border-radius: 4px;
}

td.active[b-m5gu62ugh9], td.active:hover[b-m5gu62ugh9] {
    background-color: #357ebd;
    border-color: transparent;
    color: #fff;
}

th.month[b-m5gu62ugh9] {
    width: auto;
}

select.monthselect[b-m5gu62ugh9], select.yearselect[b-m5gu62ugh9] {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

select.monthselect[b-m5gu62ugh9] {
    margin-right: 2%;
    width: 56%;
}

select.yearselect[b-m5gu62ugh9] {
    width: 40%;
}

.daterangepicker .drp-buttons[b-m5gu62ugh9] {
    clear: both;
    text-align: right;
    padding: 8px;
    border-top: 1px solid #ddd;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}
/* _content/BlazorDateRangePicker/DateRangePicker.razor.rz.scp.css */
.daterangepicker[b-x77lgbiy4z] {
    position: fixed;
    color: inherit;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 0px;
    left: 0px;
    right: auto;
    z-index: 3001;
    font-family: arial;
    font-size: 15px;
    line-height: 1em;
}

    .daterangepicker[b-x77lgbiy4z]:before, .daterangepicker[b-x77lgbiy4z]:after {
        position: absolute;
        display: inline-block;
        border-bottom-color: rgba(0, 0, 0, 0.2);
        content: '';
    }

    .daterangepicker[b-x77lgbiy4z]:before {
        top: -7px;
        border-right: 7px solid transparent;
        border-left: 7px solid transparent;
        border-bottom: 7px solid #ccc;
    }

    .daterangepicker[b-x77lgbiy4z]:after {
        top: -6px;
        border-right: 6px solid transparent;
        border-bottom: 6px solid #fff;
        border-left: 6px solid transparent;
    }

    .daterangepicker.inline[b-x77lgbiy4z]:before, .daterangepicker.inline[b-x77lgbiy4z]:after {
        content: none;
    }

    .daterangepicker.inline[b-x77lgbiy4z] {
        position: inherit;
        display: inline-block;
    }

    .daterangepicker.opensleft[b-x77lgbiy4z]:before {
        right: 9px;
    }

    .daterangepicker.opensleft[b-x77lgbiy4z]:after {
        right: 10px;
    }

    .daterangepicker.openscenter[b-x77lgbiy4z]:before {
        left: 0;
        right: 0;
        width: 0;
        margin-left: auto;
        margin-right: auto;
    }

    .daterangepicker.openscenter[b-x77lgbiy4z]:after {
        left: 0;
        right: 0;
        width: 0;
        margin-left: auto;
        margin-right: auto;
    }

    .daterangepicker.opensright[b-x77lgbiy4z]:before {
        left: 9px;
    }

    .daterangepicker.opensright[b-x77lgbiy4z]:after {
        left: 10px;
    }

    .daterangepicker.drop-up[b-x77lgbiy4z] {
        margin-top: -7px;
    }

        .daterangepicker.drop-up[b-x77lgbiy4z]:before {
            top: initial;
            bottom: -7px;
            border-bottom: initial;
            border-top: 7px solid #ccc;
        }

        .daterangepicker.drop-up[b-x77lgbiy4z]:after {
            top: initial;
            bottom: -6px;
            border-bottom: initial;
            border-top: 6px solid #fff;
        }

    .daterangepicker.single .daterangepicker .ranges[b-x77lgbiy4z], .daterangepicker.single .drp-calendar[b-x77lgbiy4z] {
        float: none;
    }

    .daterangepicker.single .drp-selected[b-x77lgbiy4z] {
        display: none;
    }

    .daterangepicker.show-calendar .drp-calendar[b-x77lgbiy4z] {
        display: block;
    }

    .daterangepicker.show-calendar .drp-buttons[b-x77lgbiy4z] {
        display: block;
    }

    .daterangepicker.auto-apply .drp-buttons[b-x77lgbiy4z] {
        display: none;
    }

    .daterangepicker .drp-calendar[b-x77lgbiy4z] {
        display: none;
        max-width: 270px;
    }

        .daterangepicker .drp-calendar.left[b-x77lgbiy4z] {
            padding: 8px 0 8px 8px;
        }

        .daterangepicker .drp-calendar.right[b-x77lgbiy4z] {
            padding: 8px;
        }

        .daterangepicker .drp-calendar.single .calendar-table[b-x77lgbiy4z] {
            border: none;
        }

    .daterangepicker .calendar-table[b-x77lgbiy4z] {
        border: 1px solid #fff;
        border-radius: 4px;
        background-color: #fff;
    }

    .daterangepicker .calendar-time[b-x77lgbiy4z] {
        text-align: center;
        margin: 4px auto 0 auto;
        line-height: 30px;
        position: relative;
    }

    .daterangepicker .drp-buttons[b-x77lgbiy4z] {
        clear: both;
        text-align: right;
        padding: 8px;
        border-top: 1px solid #ddd;
        display: none;
        line-height: 12px;
        vertical-align: middle;
    }

    .daterangepicker .drp-selected[b-x77lgbiy4z] {
        display: inline-block;
        font-size: 12px;
        padding-right: 8px;
    }

    .daterangepicker .drp-buttons .btn[b-x77lgbiy4z] {
        margin-left: 8px;
        font-size: 12px;
        font-weight: bold;
        padding: 4px 8px;
    }

    .daterangepicker.show-ranges.single.rtl .drp-calendar.left[b-x77lgbiy4z] {
        border-right: 1px solid #ddd;
    }

    .daterangepicker.show-ranges.single.ltr .drp-calendar.left[b-x77lgbiy4z] {
        border-left: 1px solid #ddd;
    }

    .daterangepicker.show-ranges.rtl .drp-calendar.right[b-x77lgbiy4z] {
        border-right: 1px solid #ddd;
    }

    .daterangepicker.show-ranges.ltr .drp-calendar.left[b-x77lgbiy4z] {
        border-left: 1px solid #ddd;
    }

    .daterangepicker .ranges[b-x77lgbiy4z] {
        float: none;
        text-align: left;
        margin: 0;
    }

    .daterangepicker.show-calendar .ranges[b-x77lgbiy4z] {
        margin-top: 8px;
    }

    .daterangepicker .ranges ul[b-x77lgbiy4z] {
        list-style: none;
        margin: 0 auto;
        padding: 0;
        width: 100%;
    }

    .daterangepicker .ranges li[b-x77lgbiy4z] {
        font-size: 12px;
        padding: 8px 12px;
        cursor: pointer;
    }

        .daterangepicker .ranges li:hover[b-x77lgbiy4z] {
            background-color: #eee;
        }

        .daterangepicker .ranges li.active[b-x77lgbiy4z] {
            background-color: #08c;
            color: #fff;
        }

.daterangepicker-visibility-hidden[b-x77lgbiy4z] {
    visibility: hidden;
}

.daterangepicker-visibility-visible[b-x77lgbiy4z] {
    visibility: visible;
}

/*  Larger Screen Styling */
@media (min-width: 564px) {
    .daterangepicker[b-x77lgbiy4z] {
        width: auto;
    }

        .daterangepicker .ranges ul[b-x77lgbiy4z] {
            width: 140px;
        }

        .daterangepicker.single .ranges ul[b-x77lgbiy4z] {
            width: 100%;
        }

        .daterangepicker.single .drp-calendar.left[b-x77lgbiy4z] {
            clear: none;
        }

        .daterangepicker.single .ranges[b-x77lgbiy4z], .daterangepicker.single .drp-calendar[b-x77lgbiy4z] {
            float: left;
        }

    .daterangepicker[b-x77lgbiy4z] {
        direction: ltr;
        text-align: left;
    }

        .daterangepicker .drp-calendar.left[b-x77lgbiy4z] {
            clear: left;
            margin-right: 0;
        }

            .daterangepicker .drp-calendar.left .calendar-table[b-x77lgbiy4z] {
                border-right: none;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

        .daterangepicker .drp-calendar.right[b-x77lgbiy4z] {
            margin-left: 0;
        }

            .daterangepicker .drp-calendar.right .calendar-table[b-x77lgbiy4z] {
                border-left: none;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

        .daterangepicker .drp-calendar.left .calendar-table[b-x77lgbiy4z] {
            padding-right: 8px;
        }

        .daterangepicker .ranges[b-x77lgbiy4z], .daterangepicker .drp-calendar[b-x77lgbiy4z] {
            float: left;
        }
}

@media (min-width: 730px) {
    .daterangepicker .ranges[b-x77lgbiy4z] {
        width: auto;
    }

    .daterangepicker .ranges[b-x77lgbiy4z] {
        float: left;
    }

    .daterangepicker.rtl .ranges[b-x77lgbiy4z] {
        float: right;
    }

    .daterangepicker .drp-calendar.left[b-x77lgbiy4z] {
        clear: none !important;
    }
}
/* _content/BlazorDateRangePicker/TimePicker.razor.rz.scp.css */
select.hourselect[b-tltfahrhsf], select.minuteselect[b-tltfahrhsf], select.secondselect[b-tltfahrhsf], select.ampmselect[b-tltfahrhsf] {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}

select.disabled[b-tltfahrhsf] {
    color: #ccc;
    cursor: not-allowed;
}
