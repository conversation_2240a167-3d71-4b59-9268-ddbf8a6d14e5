﻿/**** produit détail ****/
#bel_productdetail .bel_product_title {
    margin-bottom: 0.15rem;
}

#bel_productdetail .bel_product_ref {
    font-size:.8em;
    margin-bottom: 10px;
}

#bel_productdetail .bel_product_img {
    width: 100%;
    padding-top: 100%;
    background-color: #fff;
    display: block;
    margin-bottom: 10px;
    position: relative;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;

}

#bel_productdetail .bel_product_price {
    font-size:1.5em;
    font-weight: 600;
    margin-bottom: 7px;
    line-height: .8em;
}

#bel_productdetail .bel_product_price small {
    font-size: 0.5em;
    vertical-align: top;
}

#bel_productdetail .bel_product_oldprice {
    font-size: 0.7em;
    text-decoration: line-through;
    margin-left: 5px;
}

#bel_productdetail .bel_badge_wrapper {
    position: absolute;
    top: 10px;
    left: 10px;
}

#bel_productdetail .bel_badgepromo {
    border: 1px solid solid #b1f2a6;
    border-radius: 2px;
    background-color: #61c850;
    color: #fff;
    padding: 0 5px;
}

#bel_productdetail .bel_badgeoutofstock {
    border: 1px solid solid #B53430;
    border-radius: 2px;
    background-color: #d9534f;
    color: #fff;
    padding: 0 5px;
}

#bel_productdetail #inputs_bel_product_amount_wrapper {
    display: none;
    align-items: flex-end;
}

#bel_productdetail .addnewlineproductvariableamount {
    margin-left: 10px;
    border-radius: 100px;
}

#btn_bel_addproduct {
    padding-left: 40px;
    padding-right: 40px;
}
.bel_description_wrapper {
    text-align: justify;
    margin-bottom: 15px;
}

#input_bel_product_number, #input_bel_product_amount_original, .input_bel_product_amount_wrapper {
    display: none;
}

[id^=input_bel_product_amount] {
    /*font-size: 24px;*/
}

[id^=input_bel_product_amount] + .input-group .input-group-append .input-group-text, [id^=input_bel_product_amount] + .input-group .input-group-prepend .input-group-text {
    /*font-size: 24px;*/
    padding: 0 .75rem;
}

[id^=input_bel_product_number], [id^=input_bel_product_amount] {
    padding-top: 19px;
    padding-bottom: 19px;
}

#cardsContentWrapper .deleteConsumersForProduct:hover {
    cursor: pointer;
    color: darken(#dc3545,15%) !important;
}