INSERT INTO dossier_[eventID]
([dossier_id]
,[identite_id]
,[commande_id]
,[abon_manif_id]
,[seance_id]
,[dossier_v] 
,[operateur_id] 
,[dossier_montant] 
,[dossier_montant1] 
,[dossier_montant2] 
,[dossier_montant3] 
,[dossier_montant4] 
,[dossier_montant5] 
,[dossier_montant6] 
,[dossier_montant7] 
,[dossier_montant8] 
,[dossier_montant9] 
,[dossier_montant10] 
,[dossier_c] 
,[dossier_etat] 
,[dossier_icone] 
,[dossier_numero] 
,[dossier_nbplace] 
,[dossier_montantpayer] 
,[dossier_facture] 
,[dossier_client_nom] 
,[num_paiement] 
,[date_operation] 
,[operation_id] 
,[filiere_id] )  
VALUES(@pdossierid
,@pidentiteId
,@porderId
,@pabonManifId
,@psessionId
,@pdossierV
,@poperateurId
,@pMontant/100
,@pMontant1/100
,@pMontant2/100
,@pMontant3/100
,@pMontant4/100
,@pMontant5/100
,@pMontant6/100
,@pMontant7/100
,@pMontant8/100
,@pMontant9/100
,@pMontant10/100
,@pdossierC
,@pdossierEtat
,@pdossierIcone
,@pdossierNumero
,@pdossierNbrPlaces
,@pdossierMontantPayer/100
,@pdossierFacture
,@pdossierNomClient
,@pdossierNumPaiement
,getdate()
,@pdossieroperation_id
,@pdossierfiliere_id);

INSERT INTO dossiersvg_[eventID]
([dossier_id]
,[identite_id]
,[commande_id]
,[abon_manif_id]
,[seance_id]
,[dossier_v] 
,[operateur_id] 
,[dossier_montant] 
,[dossier_montant1] 
,[dossier_montant2] 
,[dossier_montant3] 
,[dossier_montant4] 
,[dossier_montant5] 
,[dossier_montant6] 
,[dossier_montant7] 
,[dossier_montant8] 
,[dossier_montant9] 
,[dossier_montant10] 
,[dossier_c] 
,[dossier_etat] 
,[dossier_icone] 
,[dossier_numero] 
,[dossier_nbplace] 
,[dossier_montantpayer] 
,[dossier_facture] 
,[dossier_client_nom] 
,[num_paiement] 
,[date_operation] 
,[type_operation] 
,[filiere_id] ) 
VALUES( @pdossierid
,@pidentiteId
,@porderId
,@pabonManifId
,@psessionId
,@pdossierV
,@poperateurId
,@pMontant/100
,@pMontant1/100
,@pMontant2/100
,@pMontant3/100
,@pMontant4/100
,@pMontant5/100
,@pMontant6/100
,@pMontant7/100
,@pMontant8/100
,@pMontant9/100
,@pMontant10/100
,@pdossierC
,@pdossierEtat
,@pdossierIcone
,@pdossierNumero
,@pdossierNbrPlaces
,@pdossierMontantPayer/100
,@pdossierFacture
,@pdossierNomClient
,@pdossierNumPaiement
,getdate()
,@ptype_operation
,@pdossierfiliere_id);