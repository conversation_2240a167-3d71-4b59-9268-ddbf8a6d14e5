﻿


/*  ** getPaniersToDo_createcmd */
--declare @delayTo int = 10
--declare @delaySince int = 6000000
--declare @thisetape varchar(50) = 'CREATIONCMD'
--declare @etapeBefore varchar(50) = 'REFLAGPLACES'

DECLARE @NewVersion int = 0
IF EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'action_id'
          AND Object_ID = Object_ID(N'logs_etapes_creationCmds'))
	and EXISTS (SELECT 1 
           FROM INFORMATION_SCHEMA.TABLES 
           WHERE TABLE_TYPE='BASE TABLE' 
           AND TABLE_NAME='panier_action_payment')
BEGIN
	SELECT @NewVersion= COUNT(* ) fROM panier_action_payment
END

IF (@NewVersion>0)
BEGIN

SELECT null as code, null as lecEtat,  p.panier_id, structure_id, act.action_id, act.typePaiement,  act.etat, p.date_operation, p.date_paiement,
identite_id, web_user_id, p.etat, commande_id, act.transaction_id, act.certificate, act.card_number, act.card_type, email, adresse_livraison_id, p.langcode, p.coupon_promo, p.tunnel
,(select profil_acheteur_id from Users where user_id = p.web_user_id) as  paId
FROM panier p 
inner join panier_action_payment act on act.panier_id= p.panier_id

 WHERE p.etat='P' and
  act.etat='P'
AND act.date_paiement<dateadd(second, -@delayTo,getdate()) 
AND act.date_paiement>dateadd(second, -@delaySince,getdate())

and act.action_id not in ( -- action que je n'ai pas déjà fait
	select action_id FROM logs_etapes_creationCmds lec 
	inner join logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	where lec.panier_id =p.panier_id  AND code = @thisetape and etat<>0 ) 
and (
	act.action_id in (  -- et action que l'etape d'avant a fini correctement
	select action_id FROM logs_etapes_creationCmds lec 
	inner join logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	where lec.panier_id =p.panier_id  AND code = @etapeBefore and lec.etat in (2,3) ) 

	OR act.action_id in (  -- et action que l'etape d'avant a fini correctement
	select action_id FROM logs_etapes_creationCmds lec 
	inner join logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	where lec.panier_id =p.panier_id  AND code = @thisetape and lec.etat in (0) ) 

	)
	AND structure_id  = @structuresId


	UNION

	SELECT loeref.code, lec.etat as lecEtat,  p.panier_id, structure_id, 0 as action_id, '' as typeActionPaiement, p.etat, p.date_operation, p.date_paiement,
	identite_id, web_user_id, p.etat, commande_id, p.transaction_id, p.certificate, p.card_number, p.card_type, p.email, p.adresse_livraison_id, p.langcode, p.coupon_promo, p.tunnel
	,(select profil_acheteur_id from Users where user_id = p.web_user_id) as  paId
		FROM panier p 
			LEFT OUTER JOIN panier_action_payment act on p.panier_id = act.panier_id
	 LEFT OUTER JOIN logs_etapes_creationCmds lec 
	 ON lec.panier_id = p.panier_id and log_etape_id = (SELECT max(log_etape_id) FROM logs_etapes_creationCmds lec2 WHERE lec2.panier_id = p.panier_id) 
	 LEFT OUTER JOIN logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	 WHERE p.etat='P'
	 	  and act.action_id is null
	AND p.date_paiement<dateadd(second, -@delayTo,getdate()) 
	AND p.date_paiement>dateadd(second, -@delaySince,getdate())
	AND ((code = @thisetape AND lec.etat = 0 /* create cmd */) OR (code = @etapeBefore AND (lec.etat = 2 or lec.etat=3) /* reflag fin */ )) 
	AND structure_id  = @structuresId



ORDER BY p.panier_id desc

END
ELSE
BEGIN /* pas d'action dans les logs ou n'est pas renseigné : à l'ancienne */
	SELECT top 100 loeref.code, lec.etat as lecEtat,  p.panier_id, structure_id, 0 as action_id, '' as typePaiement, p.etat, p.date_operation, p.date_paiement,
	identite_id, web_user_id, commande_id, transaction_id, certificate, card_number, card_type, email, adresse_livraison_id, p.langcode, p.coupon_promo, p.tunnel
	,(select profil_acheteur_id from Users where user_id = p.web_user_id) as  paId
	FROM panier p 
	 LEFT OUTER JOIN logs_etapes_creationCmds lec 
	 ON lec.panier_id = p.panier_id and log_etape_id = (SELECT max(log_etape_id) FROM logs_etapes_creationCmds lec2 WHERE lec2.panier_id = p.panier_id) 
	 LEFT OUTER JOIN logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	 WHERE p.etat='P'
	AND date_paiement<dateadd(second, -@delayTo,getdate()) 
	AND date_paiement>dateadd(second, -@delaySince,getdate())
	AND ((code = @thisetape AND lec.etat = 0 /* create cmd */) OR (code = @etapeBefore AND (lec.etat = 2 or lec.etat=3) /* reflag fin */ )) 
	AND structure_id = @structuresId
	ORDER BY p.panier_id desc
END