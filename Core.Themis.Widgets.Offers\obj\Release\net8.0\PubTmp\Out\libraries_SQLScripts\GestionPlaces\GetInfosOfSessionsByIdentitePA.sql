﻿/* calendrier : seances, grille de tarif, dispo de gestion place */

DECLARE @const_placementlibre int; set @const_placementlibre =32;
DECLARE @const_priseauto int; set @const_priseauto =16;
DECLARE @const_vueplacement int; set @const_vueplacement =8;
DECLARE @const_choixsurplan int; set @const_choixsurplan =1;


--DECLARE @myOffre int
--set @myOffre = @pofferId

declare @myEventId int
set @myEventId = @peventId

DECLARE @DateDeb as datetime
DECLARE @DateFin as datetime 
SET @DateDeb = @dateFrom
SET @DateFin = @dateTo

declare @myidentite int
set @myidentite = @pidentityId
declare @mybuyerprofilId int
set @mybuyerprofilId = @pbuyerprofilId

declare @iamrevendeur int = 0;
select @iamrevendeur = is_revendeur from profil_acheteur pa where id = @mybuyerprofilId 


CREATE TABLE #myoffres 
( 
	offre_id  INT, 
	offre_nom VARCHAR(200) 
) 
EXEC [Sp_ws_getoffres_totable] 
@myidentite, 
@mybuyerprofilId 

declare @offreCount int
select @offreCount =count(*) from #myoffres;

declare @manif_id int
declare @sql varchar(max)

--CREATE TABLE #myManifs (manif_id int)
create TABLE #myManifsGpIds (manif_id int, gestion_place_id int)
CREATE TABLE #GrilleTarif (session_id int,manif_id int, gestion_place_id int, type_tarif_id int, 
type_tarif_nom VARCHAR(50),
categ_id int, 
vts_grille1 decimal(18,2) ,
vts_grille2 decimal(18,2) ,
vts_grille3 decimal(18,2) ,
vts_grille4 decimal(18,2) ,
vts_grille5 decimal(18,2) ,
vts_grille6 decimal(18,2) ,
vts_grille7 decimal(18,2) ,
vts_grille8 decimal(18,2) ,
vts_grille9 decimal(18,2) ,
vts_grille10 decimal(18,2) ,
vts_valeur decimal(18,2) ,
vts_id int
);

IF (@offreCount> 0)	
BEGIN
			INSERT INTO #myManifsGpIds 
			select distinct manif_id, gp.gestion_place_id from gestion_place gp
			inner join offre_gestion_place gpo on gpo.gestion_place_id = gp.gestion_place_id 
			inner join seance s on s.seance_id=gp.seance_id 
			inner join #myoffres myoff on myoff.offre_id = gpo.offre_id
				where /*gp.isvalide=1*/ manif_id=@myEventId and s.seance_date_deb>getdate()
END

if (@iamrevendeur=0)
begin


	insert into #myManifsGpIds select distinct manif_id, gestion_place_id from gestion_place gp 
			inner join seance s on s.seance_id=gp.seance_id 
			where /*isvalide=1 and*/
				isContrainteIdentite=0 and manif_id=@myEventId and s.seance_date_deb>getdate()
				
	INSERT INTO #myManifsGpIds /* add les gestion places des offres d'adhesion mises en avant */
						select distinct gp.manif_id, gp.gestion_place_id 
						from offre o
						inner join offre_gestion_place ogp on ogp.offre_id = o.offre_id
						
						inner join gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
						inner join seance s on s.seance_id=gp.seance_id 
						inner join Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
						inner join Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID

                        inner join Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1

		where acat.Mise_En_Avant =1 and isvalide =1	and gp.manif_id = @myEventId and s.seance_date_deb>getdate()			
end



/* supp les gestion_place qui ne sont pas dans le bon intervalle de date */
DELETE #myManifsGpIds WHERE gestion_place_id NOT IN (
			select gestion_place_id FROM gestion_place gp 
			INNER JOIN seance s on s.seance_id = gp.seance_id
			where seance_date_deb > @DateDeb
			--where (seance_date_deb between @DateDeb and @DateFin OR seance_date_fin between @DateDeb and @DateFin)
		)

/* supp les manifs bloquées */		
DELETE #myManifsGpIds where manif_id in (select manifestation_id from GP_manifestation where islock =1)		

DECLARE cur_manifs CURSOR SCROLL FOR
	SELECT DISTINCT manif_id from #myManifsGpIds 

OPEN cur_manifs;

--select * from gestion_place

FETCH NEXT FROM cur_manifs INTO @manif_id
WHILE @@FETCH_STATUS = 0
BEGIN
	--SELECT @manif_id

		set @sql = 'INSERT INTO #GrilleTarif SELECT vts.seance_id, ' +  convert(varchar,@manif_id) + ' as manif_id, gp.gestion_place_id, tt.type_tarif_id, type_tarif_nom,
	vts.categ_id, vts_grille1 ,
	vts_grille2 ,
	vts_grille3 ,
	vts_grille4 ,
	vts_grille5 ,
	vts_grille6 ,
	vts_grille7 ,
	vts_grille8 ,
	vts_grille9 ,
	vts_grille10,
	vts_valeur,
	vts_id
	FROM valeur_tarif_stock' + convert(varchar,@manif_id) + ' vts' + CHAR(13) +
	'INNER JOIN type_tarif tt on tt.type_tarif_id = vts.type_tarif_id ' +CHAR(13) + 
	'INNER JOIN gestion_place gp on vts.categ_id = gp.categ_id and gp.type_tarif_id =vts.type_tarif_id and vts.seance_id=gp.seance_id ' +CHAR(13) +

	'INNER JOIN #myManifsGpIds gpFiltree ON gpFiltree.gestion_place_id=gp.gestion_place_id '

	set @SQL+= CHAR(9) + 'WHERE vts_v=(SELECT MAX(vts_v) FROM valeur_tarif_stock' +  convert(varchar,@manif_id) + ' vts2 
			WHERE 
			vts2.tarif_logique_id=vts.tarif_logique_id
			and vts2.seance_id=vts.seance_id
			and vts2.categ_id= vts.categ_id
			and vts2.type_tarif_id= vts.type_tarif_id)
			AND vts_grille1>=0 ' +CHAR(13)


    set @sql += CHAR(13) + 'order by vts.seance_id, vts.categ_id, vts.type_tarif_id'

	 			print @sql
	exec (@sql)


	FETCH NEXT FROM cur_manifs INTO @manif_id
END

CLOSE cur_manifs
DEALLOCATE cur_manifs

SELECT m.*, s.*, l.*, gp.*, cat.*, tt.*
FROM #GrilleTarif gt 
INNER JOIN seance s ON s.seance_id = gt.session_id
INNER JOIN lieu l on s.lieu_id = l.lieu_id
INNER JOIN manifestation m ON m.manifestation_id=s.manifestation_id 
INNER JOIN gestion_place gp ON gp.gestion_place_id=gt.gestion_place_id
INNER JOIN categorie cat ON cat.categ_id = gp.categ_id
INNER JOIN type_tarif tt ON tt.type_tarif_id = gp.type_tarif_id
INNER JOIN structure on 1=1
WHERE gp.isvalide =1 
AND gp.seance_id NOT IN (SELECT seance_id FROM GP_seance WHERE islock =1) /* supp les séances bloquées */
ORDER BY s.manifestation_id, s.seance_date_deb, cat.pref_affichage, tt.pref_affichage

DROP TABLE #myManifsGpIds
DROP TABLE #GrilleTarif
DROP TABLE #myoffres

