﻿/* Url de la font globale sur le site */
@globalFontUrl: "https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900";

/* Nom de la font globale sur le site */
@globalFontName: "Roboto";

/* couleur de textes */
@globalFontColor: #333333;

/* couleur des lien */
@globalLinkColor: #000000;

/* couleur de background */
@globalBackgroundColor: f8f8f8;

/* couleur principale */
@primaryColor: #000000;

/* couleur de texte des boutons primaire */
@btnPrimaryTextColor: #ffffff;

/* couleur secondaire */
@secondaryColor: #5F5F5F;

/* couleur de texte des boutons secondaire */
@btnSecondaryTextColor: #ffffff;
/*Couleurs des catégorie*/
@seatSelected: #088eb2;

@categ1: #308abe;
@categ2: #b55fc5;
@categ3: #ffa733;
@categ4: #d2b25e;
@categ5: #56d3d8;
@categ6: #308abe;
@categ7: #f7d664;
@categ8: #f2bc00;
@categ9: #d4a61a;
@categ10: #cc99ff;
@categ11: #cc33ff;
@categ12: #9900cc;
@categ13: #ddc097;
@categ14: #ddc097;
@categ15: #ddc097;
@categ16: #ddc097;
@categ17: #ddc097;
@categ18: #ddc097;
@categ19: #ddc097;
@categ20: #ddc097;
@categ21: #ddc097;
@categ22: #ddc097;
@categ23: #ddc097;
@categ24: #ddc097;
@categ25: #ddc097;
@categ26: #ddc097;
@categ27: #ddc097;
@categ28: #ddc097;
@categ29: #ddc097;
@categ30: #ddc097;
@categ31: #ddc097;
@categ32: #ddc097;
@categ33: #ddc097;
@categ34: #ddc097;
@categ35: #ddc097;
@categ36: #ddc097;
@categ37: #ddc097;
@categ38: #ddc097;
@categ39: #ddc097;
@categ40: #ddc097;
@categ41: #ddc097;
@categ42: #ddc097;
@categ43: #ddc097;
@categ44: #ddc097;
@categ45: #ddc097;
@categ46: #ddc097;
@categ47: #ddc097;
@categ48: #ddc097;
@categ49: #ddc097;
@categ50: #ddc097;
/* import font custom  */
& when not(@globalFontUrl ="") {
    @import (less) "@{globalFontUrl}";
}

/********* GENERAL ********/

body {
    background-color: @globalBackgroundColor;
}


body {
    color: @globalFontColor;
    font-family: "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

body when not (@globalFontName ="") {
    font-family: "@{globalFontName}", "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

:focus, :focus-within {
    outline: none !important;
    box-shadow: 0 0 0 0  !important;
}

a {
    color: @globalLinkColor;
    cursor: pointer;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

a:hover, a:focus {
    color: darken(@globalLinkColor, 20%)
}
[data-toggle="collapse"] .collapseArrow {
    transition: all .3s ease;
    transform: rotate(0deg);
    transform-origin: 50% 50%;
}

[data-toggle="collapse"][aria-expanded="true"] .collapseArrow {
    transform: rotate(0deg);
}

[data-toggle="collapse"][aria-expanded="true"] .collapseArrow.collapseArrowUp {
    transform: rotate(-180deg);
}
.tooltip-lg > .tooltip-inner {
    max-width: 300px;
}

.tooltip-light.bs-tooltip-auto[x-placement^=top] .arrow::before, .tooltip-light.bs-tooltip-top .arrow::before {
    border-top-color: #fefefe;
}

.tooltip-light.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .tooltip-light.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #fefefe;
}

.tooltip-light.bs-tooltip-auto[x-placement^=left] .arrow::before, .tooltip-light.bs-tooltip-left .arrow::before {
    border-left-color: #fefefe;
}

.tooltip-light.bs-tooltip-auto[x-placement^=right] .arrow::before, .tooltip-light.bs-tooltip-right .arrow::before {
    border-right-color: #fefefe;
}

.tooltip-light .tooltip-inner {
    color: #000;
    background-color: #fefefe;
}

.tooltip-nopadding .tooltip-inner {
    padding: 0
}
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.btn {
    border-radius: 100px;
    padding-left: 30px;
    padding-right: 30px;
}
.btn-decrement, .btn-increment {
    padding-left: 0px;
    padding-right: 0px;
}
.nav-link.btn {
    display: block;
}

.nav-pills .nav-link.btn {
    border-radius: 100px;
}
.btn-with-arrow-above:after, .btn-with-arrow-below:after {
    font-family: "Font Awesome 6 Free";
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-weight: 900;
    margin-left: 7px;
}

.btn-with-arrow-below:not(.collapsed):after {
    content: "\f077";
}

.btn-with-arrow-below:after {
    content: "\f078";
}

.btn-with-arrow-above:not(.collapsed):after {
    content: "\f078";
}

.btn-with-arrow-above:after {
    content: "\f077";
}

.btn:not([disabled]):hover, .btn:not([disabled]):active, .btn:not([disabled]):focus {
    text-decoration: none;
    cursor: pointer
}

.btn-primary {
    color: @btnPrimaryTextColor;
    background-color: @primaryColor;
    border-color: @primaryColor;
}

.btn-primary:not([disabled]):hover, .btn-primary:not([disabled]):active, .btn-primary:not([disabled]):focus {
    background-color: darken(@primaryColor, 20%);
    border-color: darken(@primaryColor, 20%);
}

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle {
    background-color: darken(@primaryColor, 20%)
}

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus {
}

.btn-primary .badge {
    color: @primaryColor;
    background-color: @btnPrimaryTextColor;
}

.btn-secondary {
    color: @btnSecondaryTextColor;
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}

.btn-secondary:not([disabled]):hover, .btn-secondary:not([disabled]):active, .btn-secondary:not([disabled]):focus {
    background-color: darken(@secondaryColor, 20%);
    border-color: darken(@secondaryColor, 20%)
}
.btn-outline-secondary {
    color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-outline-secondary:hover, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle {
    color: contrast(@secondaryColor);
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-link {
    color: @primaryColor;
    background-color: #f0f0f0;
    border-color: #d9d9d9
}

.btn-link:not([disabled]):hover, .btn-link:not([disabled]):active, .btn-link:not([disabled]):focus {
    background-color: #aaaaaa;
    border-color: #d9d9d9
}

.btn[disabled], .btn.disabled, .btn.disabled:hover, .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
    color: #fff;
    background-color: #aaaaaa;
    border-color: #878787
}

.table-primary, .table-primary > td, .table-primary > th {
    background-color: fade(@primaryColor, 10%);
}

.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-primary thead th {
    border-color: fade(@primaryColor, 10%);
}

.custom-control-input:checked ~ .custom-control-label::before {
    color: @btnPrimaryTextColor;
    border-color: @primaryColor;
    background-color: @primaryColor;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 50%);
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
    border-color: fade(@primaryColor, 30%);
}

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
}

@media (min-width: 576px) {
    .modal-xl-wide {
        max-width: 90%;
    }
}

@media (min-width: 992px) {
    .modal-xl-wide {
        max-width: 800px;
    }
}

@media (min-width: 1200px) {
    .modal-xl-wide {
        max-width: 1140px;
    }
}
.boxedBlock {
    border-radius: 4px;
    position: relative;
    z-index: 1;
    margin-bottom: 15px;
    background-color: #fff;
    padding: 15px;
    box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    border-radius: 5px;
}
.boxedBlock.nopaddingX {
    padding-left: 0;
    padding-right: 0;
}
.boxedBlock.nopaddingY {
    padding-top: 0;
    padding-bottom: 0;
}
.boxedBlock h2 {
    /*color: @primaryColor;*/
    font-size: 1.5rem;
}
.boxedBlock.nopaddingY h2 {
    padding-top: 15px;

}
/********** ONELINE ***********/
.boxedBlock.nopaddingX .oneSummaryLine {
    padding-left: 15px;
    padding-right: 15px;
}

#summaryWrapper .oneSummaryLine {
    padding-top: 7px;
    padding-bottom: 7px;
}

#summaryWrapper .oneSummaryLine:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#summaryWrapper .oneSummaryLine .productLieuWrapper, #summaryWrapper .oneSummaryLine .productTarifCategWrapper, #summaryWrapper .oneSummaryLine .productCountWrapper, #summaryWrapper .oneSummaryLine .productSeatsWrapper, #summaryWrapper .oneSummaryLine .productAttachedTo {
    color: fade(@globalFontColor, 40%);
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus {
    margin: 0 1px;
    margin-left: 10px;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus ~ .pastilleStatus {
    margin: 0 1px;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus {
    display: inline-block;
    width: 9px;
    height: 9px;
    border-radius: 100px;
    border: 1px solid transparent;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus[data-status="paid"] {
    background-color: #fff;
    border-color: #28a745;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus[data-status="edited"] {
    background-color: #28a745;
    border-color: #28a745;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus[data-status="reserved"] {
    background-color: #ffc107;
    border-color: #ffc107;
}

#summaryWrapper .oneSummaryLine .productCountWrapper .pastilleStatus[data-status="canceled"] {
    background-color: #dc3545;
    border-color: #dc3545;
}

#summaryWrapper .oneSummaryLine .productsSessionBtn[aria-expanded=false] .productsSessionNotCollapsed, 
#summaryWrapper .oneSummaryLine .productsEventBtn[aria-expanded=false] .productsEventNotCollapsed {
    display:none;
}
#summaryWrapper .oneSummaryLine .productsSessionBtn[aria-expanded=true] .productsSessionCollapsed,
#summaryWrapper .oneSummaryLine .productsEventBtn[aria-expanded=true] .productsEventCollapsed{
    display:none;
}

#summaryWrapper .oneSummaryLine .productDetailsCollapseBtn, 
#summaryWrapper .oneSummaryLine .productsSessionBtn,
#summaryWrapper .oneSummaryLine .productsEventBtn{
    color: lighten(@globalFontColor, 30%);
    border-color: lighten(@globalFontColor, 30%);
}

#summaryWrapper .oneSummaryLine .productDetailsCollapseBtn:hover, 
#summaryWrapper .oneSummaryLine .productDetailsCollapseBtn:not(:disabled):not(.disabled).active, 
#summaryWrapper .oneSummaryLine .productDetailsCollapseBtn:not(:disabled):not(.disabled):active, 
.show > #summaryWrapper .oneSummaryLine .productDetailsCollapseBtn.dropdown-toggle,

#summaryWrapper .oneSummaryLine .productsSessionBtn:hover,
#summaryWrapper .oneSummaryLine .productsSessionBtn:not(:disabled):not(.disabled).active,
#summaryWrapper .oneSummaryLine .productsSessionBtn:not(:disabled):not(.disabled):active,
.show > #summaryWrapper .oneSummaryLine .productsSessionBtn.dropdown-toggle,

#summaryWrapper .oneSummaryLine .productsEventBtn:hover,
#summaryWrapper .oneSummaryLine .productsEventBtn:not(:disabled):not(.disabled).active,
#summaryWrapper .oneSummaryLine .productsEventBtn:not(:disabled):not(.disabled):active,
.show > #summaryWrapper .oneSummaryLine .productsEventBtn.dropdown-toggle
{
    color: contrast(lighten(@globalFontColor, 30%));
    background-color: lighten(@globalFontColor, 30%);
    border-color: lighten(@globalFontColor, 30%);
}

#summaryWrapper .oneSummaryLine .productIcon {
    width: 18px;
    display: inline-block;
    text-align: center;
}

#summaryWrapper .oneSummaryLine .productIcon .fa-ticket-alt, #summaryWrapper .oneSummaryLine .productIcon .fa-ticket {
    transform: rotate(-45deg);
}

#summaryWrapper .oneSummaryLine .productSeatsWrapper {
    border-left: 3px dotted @secondaryColor;
    padding-left: 15px;
}

#summaryWrapper .oneSummaryLine .productSeatsWrapper svg {
    margin-right: 5px;
}

#summaryWrapper .oneSummaryLine .productDetailsWrapper:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, .05);
}

#summaryWrapper .oneSummaryLine .productSeatDetailZESC {
    display: block;
}

#summaryWrapper .oneSummaryLine .productSeatDetail {
    display: inline-block;
    background-color: #888;
    color: #fff;
    padding: 0px 4px 1px 4px;
    border-radius: 4px;
    margin-right: 7px;
}

#summaryWrapper .oneSummaryLine .productSeatStatus {
    display: inline-block;
    background-color: #888;
    color: #fff;
    padding: 0px 4px 2px 4px;
    border-radius: 4px;
}

#summaryWrapper .oneSummaryLine .productSeatStatus[data-status="edited"] {
    background-color: #007bff;
    color: #fff;
}

#summaryWrapper .oneSummaryLine .productSeatStatus[data-status="reserved"] {
    background-color: #ffc107;
    color: #fff;
}

#summaryWrapper .oneSummaryLine .productSeatStatus[data-status="paid"] {
    background-color: #28a745;
    color: #fff;
}

#summaryWrapper .oneSummaryLine .productSeatStatus[data-status="canceled"] {
    background-color: #dc3545;
    color: #fff;
}

#summaryWrapper .oneSummaryLine .productSeatDetailZESC span:not(:empty):not(:first-child):before {
    content: '-';
    margin: 0 3px;
}

#summaryWrapper .oneSummaryLine .priceTotalWrapper {
    font-weight: 700;
    line-height: 1em;
    margin-bottom: 7px;
    font-size: 1.3em;
}
#summaryWrapper .oneSummaryLine .priceTotalWrapper .feeamount {
    font-size: .8rem;
    opacity:.7
}
#summaryWrapper .oneSummaryLine .priceUnitWrapper {
    font-weight: normal;
}

#summaryWrapper .oneSummaryLine .productName, #summaryWrapper .oneSummaryLine .EventName {
    font-weight: 600;
}

#summaryWrapper .oneSummaryLine .productName {
    display: flex;
    align-items: center;
    flex-wrap:wrap;
}


#summaryWrapper .oneSummaryLine .productName .img-adhesion {
    display: inline-block;
    width: 25px;
    height: 25px;
    background-color: #d0410c;
    color: #ffffff;
    text-align: center;
    line-height: 26px;
    border-radius: 1000px;
    font-size: .8em;
    margin-right: 5px;
}

#summaryWrapper .oneSummaryLine .collapeIconWrapper {
    color: fade(@globalFontColor, 50%);
}

#summaryWrapper .oneSummaryLine .oneSession [data-toggle="collapse"]:hover, #summaryWrapper .oneSummaryLine.oneProduct [data-toggle="collapse"]:hover {
    cursor: pointer;
}

#summaryWrapper .productSeatDetailZESC span:not(:empty):not(:first-child):before {
    content: '-';
    margin: 0 3px;
}

#summaryWrapper .oneEvent:not(:last-child), #summaryWrapper .oneProduct:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 15px;
}

#summaryWrapper .oneEvent:not(:first-child), #summaryWrapper .oneProduct:not(:first-child) {
    padding-top: 15px;
}

#summaryWrapper .oneEvent .eventName {
    font-size: 1.3em;
    font-weight: 600;
}
/*produits supp */
 #summaryMoreProducts .oneProduct .productName {
     font-size: 0.875rem;
 }
/*produit dans un event (produit manif, produit séance) */
#summaryWrapper .oneEvent .oneProduct .productName, #summaryWrapper .oneProduct .priceUnitWrapper {
    font-size:  0.875rem;
}
#summaryWrapper .oneEvent .oneProduct .priceUnitWrapper {
    font-size: .875em;
}
#summaryWrapper .oneEvent .oneProduct .priceUnitWrapper, #summaryWrapper .oneEvent .oneProduct .priceUnitWrapper .priceUnitTTC {
    font-weight: 600;
    color: fade(@globalFontColor, 40%);
}
#summaryWrapper .oneEvent .oneSession:not(:first-child) {
    padding-top: 7px;
}

#summaryWrapper .oneEvent .oneSession:not(:last-child) {
    padding-bottom: 7px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#summaryWrapper .oneProduct .productAttachedTo,
#summaryWrapper .oneProduct .collapeIconWrapper,
#summaryWrapper .oneProduct .productSeatsWrapper,
#summaryWrapper .oneEvent .productSeatsWrapper,
#summaryWrapper .oneEvent .collapeIconWrapper,
#summaryWrapper .oneEvent .productSessionWrapper,
#summaryWrapper .oneEvent .productLieuWrapper {
    font-size: .875rem;
}

#summaryWrapper .oneProduct {
    position: relative;
    padding-left: 15px;
}
#summaryWrapper .oneProduct.notMandatory {
    opacity: .65
}
#summaryWrapper .oneProduct.notMandatory.active {
    opacity: 1
}
#summaryWrapper .oneProduct.notMandatory.focusin {
    opacity: 1
}
#summaryWrapper .oneProductWithDelete {
    padding-left: 30px;
}

#summaryWrapper .oneProduct .productNameWrapper {
    display: flex;
    align-items: center;
    gap: 5px;
}

#summaryWrapper .oneProduct .productNameWrapper .productImg {
    max-width: 50px;
    margin-right: 7px;
}

#summaryWrapper .oneEvent .oneSession {
    border-left: 2px solid fade(@globalFontColor,20%);
    padding-left: 15px;
    position: relative;
}

#summaryWrapper .oneProduct .basketDeleteProduct:hover, #summaryWrapper .oneEvent .oneSession .basketDeleteSession:hover {
    cursor: pointer;
    color: darken(#dc3545, 20%) !important;
}

#summaryWrapper .oneProduct .basketDeleteProduct {
    position: absolute;
    z-index: 5;
    left: 15px;
    top: 18px;
    transform: translate(-50%, 0);
    background-color: #fff;
}
#summaryWrapper .oneProduct .customSpinnerUpDown {
    position:relative;
}
#summaryWrapper .oneProduct .customSpinnerUpDown .btn-decrement, #summaryWrapper .oneProduct .customSpinnerUpDown .btn-increment {
    display:none
}
#summaryWrapper .oneProduct .customSpinnerShowOnFocus .btn-decrement, #summaryWrapper .oneProduct .customSpinnerShowOnFocus .btn-increment {
    display: inline-block;
    z-index:2;
}
#summaryWrapper .oneProduct .customSpinnerUpDown .btn-decrement {
    position:absolute;
    bottom:0;
    left:0;
    right:0;
    transform:translateY(100%);
    border-radius: 0px 0px 10px 10px;
}
#summaryWrapper .oneProduct .customSpinnerUpDown .btn-increment {
    position:absolute;
    top:0;
    left:0;
    right:0;
    transform:translateY(-100%);
    border-radius: 10px 10px 0px 0px;
}
#summaryWrapper .oneProduct .customSpinnerLeftButton {
    gap: 5px;
    vertical-align: middle;
}
#summaryWrapper .oneProduct .inptProductVariableUnit  {
    width: 130px;
}
#summaryWrapper .oneProduct .inptProductVariableAmount {
    width: 90px;
}
#summaryWrapper .oneProduct .inptProductVariableUnit, #summaryWrapper .oneProduct .inptProductVariableAmount {
    font-weight: 700;
    font-size:inherit !important;
    height: auto;
}
 #summaryWrapper .oneProduct .inptProductVariableAmount {
     
    border-color: @globalFontColor;
     padding: 0;
     text-align: right;
    border-width: 0;
    border-bottom-width: 1px;
 }
#summaryWrapper .oneEvent .oneSession .basketDeleteSession {
    position: absolute;
    z-index: 5;
    left: 0;
    top: 0;
    transform: translate(-50%, 0);
    background-color: #fff;
}

#summaryWrapper .oneEvent .productIcon, #summaryWrapper .oneProduct .productIcon {
    width: 18px;
    display: inline-block;
    text-align: center;
}

#summaryWrapper .oneEvent .priceTotalWrapper .priceTotalHT, #summaryWrapper .oneProduct .priceTotalWrapper .priceTotalHT {
    display: block;
    font-size: 0.8em;
    opacity: 0.5;
}

#summaryWrapper .oneEvent .priceTotal, #summaryWrapper .oneProduct .priceTotal {
    font-weight: 700;
}

#summaryWrapper .oneEvent .collapeIconWrapper, #summaryWrapper .oneProduct .collapeIconWrapper {
    color: rgba(32, 32, 32, 0.5);
}

#summaryWrapper .oneEvent .productSeatsWrapper, #summaryWrapper .oneProduct .productSeatsWrapper {
    border-left: 2px solid fade(@globalFontColor,20%);
    padding-left: 15px;
}

#summaryWrapper .oneEvent .productDetailsWrapper:not(:last-child), #summaryWrapper .oneProduct .productDetailsWrapper:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#summaryWrapper .oneEvent .productDetails, #summaryWrapper .oneProduct .productDetails {
    margin-bottom: 10px;
    margin-top: 10px;
}

#summaryWrapper .oneEvent .productSeatDetail, #summaryWrapper .oneProduct .productSeatDetail {
    display: inline-block;
    background-color: fade(@globalFontColor, 40%);
    color: contrast(fade(@globalFontColor, 40%));
    padding: 5px 10px;
    border-radius: 4px;
    margin-right: 7px;
}

#summaryWrapper .oneEvent .priceUnitWrapper, #summaryWrapper .oneProduct .priceUnitWrapper {
    font-weight: normal;
    line-height:1rem
}

#summaryWrapper .oneEvent .priceUnitWrapper .priceUnitTTC, #summaryWrapper .oneProduct .priceUnitWrapper .priceUnitTTC {
    font-weight: 600;
}

#summaryWrapper .oneEvent .priceUnitWrapper .priceUnitHT, #summaryWrapper .oneProduct .priceUnitWrapper .priceUnitHT {
    font-size: .8em;
}

#summaryWrapper .ticketready.notready {
    display: none;
}

#summaryWrapper .boxedBlock.nopadding .oneSummaryLine {
    padding-left: 15px;
    padding-right: 15px;
}

#summaryWrapper .bg-primary-5 {
    background-color: fade(@primaryColor, 5%);
}

#summaryWrapper .bg-primary-12 {
    background-color: fade(@primaryColor, 12%);
}

#summaryWrapper .bg-primary-20 {
    background-color: fade(@primaryColor, 20%)
}

#summaryWrapper .bg-primary-80 {
    color: contrast(fade(@primaryColor, 80%));
    background-color: fade(@primaryColor, 80%)
}
#summaryMoreProducts .oneProduct .priceUnitWrapper, #summaryWrapper .oneProduct .priceUnitWrapper .priceUnitTTC {
    font-weight: 600;
    color: fade(@globalFontColor, 40%);
}
/** big total **/
#basketTotalCmdWrapper .oneSummaryLine,
#basketFeesWrapper .oneSummaryLine {
    padding-left: 15px;
    padding-right: 15px;
}

#totalTVABigWrapper {
    font-size: 0.875em;
}

.oneSummaryLine.summaryLineTotalOrder.bigTotalWrapper {
    font-size: 1.3em;
    color: @btnPrimaryTextColor;
    background-color: fade(@primaryColor, 100%)
}

.oneSummaryLine.summaryLineTotalOrder.bigTotalWrapper .priceTotal {
    font-weight: 700;
}

@media (min-width:992px) {
    #summaryWrapper .oneSummaryLine .productSeatDetailCoupleTarifCateg span:not(:empty) {
        display: block;
    }

    #summaryWrapper .oneSummaryLine .productSeatDetailZESC {
        display: block;
    }

    #summaryWrapper .oneEvent .productSeatDetailZESC {
        display: block;
    }

    #summaryWrapper .oneEvent .productSeatDetailCoupleTarifCateg span:not(:empty) {
        display: block;
    }
}

@media (max-width:991.98px) {
    #summaryWrapper .oneSummaryLine .productSeatDetailCoupleTarifCateg span:not(:empty):not(:first-child):before {
        content: '-';
        margin: 0 3px;
    }

    #summaryWrapper .productSeatDetailCoupleTarifCateg span:not(:empty):not(:first-child):before {
        content: '-';
        margin: 0 3px;
    }
}
/************ BIG MAP *********/
#eventMap, #eventMapInner {
    max-height: 400px;
    overflow: hidden;
    position: relative;
}

#divPlanSalleWrapperAll {
    margin: auto;
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: 50% 50%;
}

#divPlanSalleWrapper {
    transform-origin: 0;
}

#divPlanSalle {
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
}

#svgPlanSalle {
    overflow: overlay;
    position: relative;
    z-index: 2;
}

#svgPlanSalle .trait {
    stroke: rgba(0, 0, 0, 0.3);
    stroke-width: 1;
}

#svgPlanSalle .poteau {
    fill: transparent;
    stroke: rgba(0, 0, 0, 0.3);
    stroke-width: 2;
}

#svgPlanSalle .textrang {
    font-weight: bold;
    fill: rgba(0, 0, 0, 0.3);
}

#svgPlanSalle .textlong {
    fill: #000000;
    font-size: 16px;
    opacity: 0.5;
}

#divForBackgroundSalle {
    position: absolute;
    top: 0;
    z-index: 1;
}


svg .siegeall {
    -webkit-transition: 1s ease;
    -o-transition: 1s ease;
    transition: 1s ease;
    stroke: white;
}

svg .siegeall[data-free="False"], #svgPlanSalle .pole .poleInnerColor {
    fill: #ccc;
}

#svgPlanSalle .pole .poleBorderColor {
    fill: darken(#ccc, 50%);
}

svg .siegeall.mineTemp, svg .siegeall.mine {
    fill: @seatSelected;
    animation: seatSelected 1s ease-in-out infinite reverse;
    stroke-width: 1px;
    stroke: darken(@seatSelected,30%)
}

svg .siegeall[data-free="True"]:hover {
    cursor: pointer;
}

#svgPlanSalleMini .siegeall.NotHighted {
    fill: rgba(0, 0, 0, .3);
}

svg .siegeall.disabled {
    fill: #ccc;
    pointer-events: none;
}

.tooltip .imgViewFromSeat {
    max-width: 200px;
    height: 130px;
}

.tooltip .imgViewFromSeat img {
    max-height: 100%;
}

.tooltip #rankAndSeat {
    min-width: 100px;
}

.tooltip #rankAndSeat .rankAndSeat {
    background-color: #eee;
    text-transform: uppercase;
    line-height: 1em;
    padding-top: 15px;
    padding-bottom: 15px;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    white-space: nowrap;
}

@-webkit-keyframes seatSelected {
    0% {
        fill: @seatSelected;
        stroke-width: 1px;
    }

    50% {
        fill: lighten(@seatSelected, 50%);
        stroke-width: 3px;
    }

    100% {
        fill: @seatSelected;
        stroke-width: 1px;
    }
}

@keyframes seatSelected {
    0% {
        fill: @seatSelected;
        stroke-width: 1px;
    }

    50% {
        fill: lighten(@seatSelected, 50%);
        stroke-width: 3px;
    }

    100% {
        fill: @seatSelected;
        stroke-width: 1px;
    }
}

/**************** MINI BASKET ************/
#minibasketContent .minibasketOneProduct:not(:last-child) {
    border-bottom:1px solid rgba(0,0,0,.15);
}
#minibasketContent .minibasketOneProduct {
    padding-top:1rem;
    padding-bottom:1rem;
}
#minibasketContent .minibasketProductTitle {
    font-size:1.3rem
}
#minibasketContent .minibasketProductCount {
    font-size:1rem;
    font-style:italic;
}
#minibasketContent .minibasketProductAmount {
    font-weight:700;
    font-size:1.3rem;
}

#minibasketWrapper #minibasketTotalWrapper {
    padding-top:1rem;
    padding-bottom:1rem;
    font-weight:700;
    font-size:1.3rem;
}