﻿function lessReady() {
    $(window).resize()
    console.log('insurance.js lessReady READY')
}

$(window).resize(function () {
    sendIframeSize()
});

$(document).ready(function () {
    console.log('insurance.js READY')
    $(window).resize()
    formatDevise()
    initBtnInsurance()
    initInsuranceAmountUpdated()
    initChangeModalAndCollapse()
});

//formatage des devises
function formatDevise() {
    $.each($('[data-pricetoformat]'), function (i, k) {
        $(k).html(SetDeviseCode(parseInt($(k).attr('data-pricetoformat'))))
    })
}

function initBtnInsurance() {
    $("#insuranceCheck").off('change').on('change', function () {
        if ($(this).is(':checked')) {
            //ajout
            pushInsuranceLineAjax()
        } else {
            //delete
            deleteInsuranceLineAjax()
        }

    })
}



function pushInsuranceLineAjax() {
    ProdInsurance.count = 1
    $.ajax({
        type: "POST",
        url: sWOffersUrl + structureId + "/Insurance/addInsuranceAjax/" + BasketId + "/" + langCode,
        data: {
            token: partnerToken,
            insurance: ProdInsurance
        },
        success: function (data) {
            switch (data.value.code) {
                case 200:
                    pushInsuranceLine()
                    break;
                case 404:
                    //pas de basket
                    console.log("addInsuranceAjax : basket introuvable")
                    break;
                case 500:
                    //pas reussi a ajouter l'assurance
                    console.log("addInsuranceAjax : pas reussi a ajouter l'assurance")
                    break;
                case 403:
                    //mauvais hash
                    console.log("addInsuranceAjax : mauvais hash")
                    break;
                default:
                    break;
            }
        },
        error: function (a, b, c) {
            console.log("addInsuranceAjax -> Error")
        }
    });
}
function pushInsuranceLine() {
    var template = $('#hidden-template-rowInsurance').html()
    var templatefilled = template.replaceAll("[insurance_name]", ProdInsurance.productName).replaceAll("[insurance_amountttccents]", ProdInsurance.amountTTCCents).replaceAll('[insurance_amountttc]', SetDeviseCode(parseInt(ProdInsurance.amountTTCCents)))
    $('#InsuranceContentWrapper').html(templatefilled)
    $("#insuranceCheck").prop("checked", true)
    var msg = {
        "action": "insuranceAddedOrDeleted",
        "amount": ProdInsurance.amountTTCCents
    }
    window.parent.postMessage(msg, '*')
    sendIframeSize()
}
function deleteInsuranceLineAjax() {
    $.ajax({
        type: "POST",
        url: sWOffersUrl + structureId + "/Insurance/deleteInsuranceAjax/" + BasketId + "/" + langCode,
        data: {
            token: partnerToken,
            insurance: ProdInsurance
        },
        success: function (data) {
            deleteInsuranceLine()
        },
        error: function (a, b, c) {
            console.log("deleteInsuranceAjax -> Error")
        }
    });
}

function deleteInsuranceLine() {
    $('#InsuranceContentWrapper').html("")
    $("#insuranceCheck").prop("checked", false)
    var msg = {
        "action": "insuranceAddedOrDeleted",
        "amount": 0
    }
    window.parent.postMessage(msg, '*')
    sendIframeSize()
}

function initInsuranceAmountUpdated() {
    if (Insurance.hasInsurance) {
        pushInsuranceLine()
    }
    if (Insurance.hasInsurance && Insurance.newAmount != Insurance.oldAmount) {
        console.log('product has changed')
        var msg = {
            "action": "insuranceAmountUpdated",
            "newamount": Insurance.newAmount,
            "oldamount": Insurance.oldAmount
        }
        window.parent.postMessage(msg, '*')

    }
}