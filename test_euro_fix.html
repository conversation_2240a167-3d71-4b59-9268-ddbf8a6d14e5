<!DOCTYPE html>
<html>
<head>
    <title>Test Euro Fix</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test des fonctions de nettoyage des valeurs Euro</h1>
    
    <div>
        <h2>Test des valeurs problématiques :</h2>
        <ul id="test-results"></ul>
    </div>

    <script>
        // Fonction utilitaire pour nettoyer les valeurs numériques avec des symboles d'euro
        function cleanNumericValue(value) {
            if (typeof value === 'string') {
                // Remplace les caractères d'euro malformés et autres caractères non numériques
                return value.replace(/[â‚¬€,\s]/g, '').replace(/[^\d.-]/g, '');
            }
            return value;
        }

        // Fonction parseFloat sécurisée qui nettoie d'abord la valeur
        function safeParseFloat(value) {
            var cleanValue = cleanNumericValue(value);
            var result = parseFloat(cleanValue);
            return isNaN(result) ? 0 : result;
        }

        // Tests
        var testValues = [
            "37,90 â‚¬",
            "14,96 €",
            "25.50€",
            "100,00 â‚¬",
            "0,50€",
            "1234.56",
            "invalid",
            "",
            null,
            undefined
        ];

        var results = $('#test-results');
        
        testValues.forEach(function(testValue) {
            var original = testValue;
            var cleaned = cleanNumericValue(testValue);
            var parsed = safeParseFloat(testValue);
            var oldParsed = parseFloat(testValue);
            
            results.append('<li><strong>Original:</strong> "' + original + '" → <strong>Cleaned:</strong> "' + cleaned + '" → <strong>safeParseFloat:</strong> ' + parsed + ' → <strong>parseFloat:</strong> ' + oldParsed + '</li>');
        });
    </script>
</body>
</html>
