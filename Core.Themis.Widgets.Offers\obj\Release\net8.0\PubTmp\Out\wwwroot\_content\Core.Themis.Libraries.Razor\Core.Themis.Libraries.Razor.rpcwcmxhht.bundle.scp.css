/* _content/Core.Themis.Libraries.Razor/Common/Components/Sortable/SortableList.razor.rz.scp.css */
/* 
  you need the ::deep identifier if you are using scoped styles like this
  because scoped styles are only applied to markup in the component, not
  to the markup inside the render fragment.
*/

[b-3l2pgwgbg6] .sortable-ghost {
    visibility: hidden;
}

[b-3l2pgwgbg6] .sortable-fallback {
    opacity: 1 !important
}
/* _content/Core.Themis.Libraries.Razor/Common/Components/Toggle.razor.rz.scp.css */
.toggle-container[b-baenymr98i] {
    transition: opacity 0.5s ease-in-out;
}

.toggle-hidden[b-baenymr98i] {
    opacity: 0;
}

.toggle-visible[b-baenymr98i] {
    opacity: 1;
}

.toggle-header[b-baenymr98i] {
    text-decoration: underline;
    cursor: pointer
}
