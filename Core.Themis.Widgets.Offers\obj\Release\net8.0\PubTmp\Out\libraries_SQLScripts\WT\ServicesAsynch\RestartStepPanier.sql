﻿DECLARE @thisLogEtapeEchouee int =0
DECLARE @actionId int =0
DECLARE @etapeId int =0
SELECT @thisLogEtapeEchouee = min(log_etape_id) FROM logs_etapes_creationCmds WHERE panier_id =@pbasketId and etat=99
SELECT @actionId = action_id, @etapeId = etape_id FROM logs_etapes_creationCmds WHERE panier_id =@pbasketId and log_etape_id = @thisLogEtapeEchouee

if @actionId is not null 
begin
	DELETE logs_etapes_creationCmds WHERE panier_id =@pbasketId and action_id = @actionId and etape_id = @etapeId and etat>0
end
else
begin
	DELETE logs_etapes_creationCmds WHERE panier_id =@pbasketId and action_id is null and etape_id = @etapeId and etat>0
end