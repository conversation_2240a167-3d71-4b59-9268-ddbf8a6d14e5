﻿/*
DECLARE @pEventsTypesId int = 0
*/

SELECT DISTINCT mgg.* from manifestation_groupe_genre mgg
		INNER JOIN manifestation_genre mg ON mg.groupe_id = mgg.id
		INNER JOIN manifestation m ON mg.id = m.ID_genre 
		INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id
		WHERE gp.isvalide = 1 AND mgr.type_evenement in (select Name from splitstring(@pEventsTypesId, ','))


/*
IF @pEventTypeId >= 0
	BEGIN
		SELECT DISTINCT mgg.* from manifestation_groupe_genre mgg
		INNER JOIN manifestation_genre mg ON mg.groupe_id = mgg.id
		INNER JOIN manifestation m ON mg.id = m.ID_genre 
		INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id
		WHERE gp.isvalide = 1 AND mgr.type_evenement = @pEventTypeId
	END
ELSE
	BEGIN
		SELECT DISTINCT mgg.* from manifestation_groupe_genre mgg
		INNER JOIN manifestation_genre mg ON mg.groupe_id = mgg.id
		INNER JOIN manifestation m ON mg.id = m.ID_genre 
		INNER JOIN gestion_place gp ON gp.manif_id = m.manifestation_id
		INNER JOIN manifestation_groupe mgr ON mgr.manif_groupe_id = m.manifestation_groupe_id
		WHERE gp.isvalide = 1
	END
	*/