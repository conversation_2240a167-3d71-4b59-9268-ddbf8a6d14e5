﻿/* Url de la font globale sur le site */
@globalFontUrl: "https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900";

/* Nom de la font globale sur le site */
@globalFontName: "Roboto";

/* couleur de textes */
@globalFontColor: #333333;

/* couleur des lien */
@globalLinkColor: #000000;

/* couleur de background */
@globalBackgroundColor: transparent;

/* couleur principale */
@primaryColor: #000000;

/* couleur de texte des boutons primaire */
@btnPrimaryTextColor: #ffffff;

/* couleur secondaire */
@secondaryColor: #5F5F5F;

/* couleur de texte des boutons secondaire */
@btnSecondaryTextColor: #ffffff;
/*Couleurs des catégorie*/
@seatSelected: #088eb2;

@categ1: #308abe;
@categ2: #b55fc5;
@categ3: #ffa733;
@categ4: #d2b25e;
@categ5: #56d3d8;
@categ6: #308abe;
@categ7: #f7d664;
@categ8: #f2bc00;
@categ9: #d4a61a;
@categ10: #cc99ff;
@categ11: #cc33ff;
@categ12: #9900cc;
@categ13: #ddc097;
@categ14: #ddc097;
@categ15: #ddc097;
@categ16: #ddc097;
@categ17: #ddc097;
@categ18: #ddc097;
@categ19: #ddc097;
@categ20: #ddc097;
@categ21: #ddc097;
@categ22: #ddc097;
@categ23: #ddc097;
@categ24: #ddc097;
@categ25: #ddc097;
@categ26: #ddc097;
@categ27: #ddc097;
@categ28: #ddc097;
@categ29: #ddc097;
@categ30: #ddc097;
@categ31: #ddc097;
@categ32: #ddc097;
@categ33: #ddc097;
@categ34: #ddc097;
@categ35: #ddc097;
@categ36: #ddc097;
@categ37: #ddc097;
@categ38: #ddc097;
@categ39: #ddc097;
@categ40: #ddc097;
@categ41: #ddc097;
@categ42: #ddc097;
@categ43: #ddc097;
@categ44: #ddc097;
@categ45: #ddc097;
@categ46: #ddc097;
@categ47: #ddc097;
@categ48: #ddc097;
@categ49: #ddc097;
@categ50: #ddc097;
@categ51: #ddc097;
@categ52: #ddc097;
@categ53: #ddc097;
@categ54: #ddc097;
@categ55: #ddc097;
@categ56: #ddc097;
@categ57: #ddc097;
@categ58: #ddc097;
@categ59: #ddc097;
@categ60: #ddc097;
@categ61: #ddc097;
@categ62: #ddc097;
@categ63: #ddc097;
@categ64: #ddc097;
@categ65: #ddc097;
@categ66: #ddc097;
@categ67: #ddc097;
@categ68: #ddc097;
@categ69: #ddc097;
@categ70: #ddc097;
@categ71: #ddc097;
@categ72: #ddc097;
@categ73: #ddc097;
@categ74: #ddc097;
@categ75: #ddc097;
@categ76: #ddc097;
@categ77: #ddc097;
@categ78: #ddc097;
@categ79: #ddc097;
@categ80: #ddc097;
@categ81: #ddc097;
@categ82: #ddc097;
@categ83: #ddc097;
@categ84: #ddc097;
@categ85: #ddc097;
@categ86: #ddc097;
@categ87: #ddc097;
@categ88: #ddc097;
@categ89: #ddc097;
@categ90: #ddc097;
@categ91: #ddc097;
@categ92: #ddc097;
@categ93: #ddc097;
@categ94: #ddc097;
@categ95: #ddc097;
@categ96: #ddc097;
@categ97: #ddc097;
@categ98: #ddc097;
@categ99: #ddc097;

@panoratio:"5/4";
/* import font custom  */
& when not(@globalFontUrl ="") {
    @import (less) "@{globalFontUrl}";
}

/********* GENERAL ********/
body {
    background-color: @globalBackgroundColor;
}

body {
    color: @globalFontColor;
    font-family: "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

body when not (@globalFontName ="") {
    font-family: "@{globalFontName}", "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

:focus, :focus-within {
    outline: none !important;
    box-shadow: 0 0 0 0  !important;
}

a {
    color: @globalLinkColor;
    cursor: pointer;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

a:hover, a:focus {
    color: darken(@globalLinkColor, 20%)
}
.tooltip-lg > .tooltip-inner {
    max-width: 300px;
}
.tooltip-light.bs-tooltip-auto[x-placement^=top] .arrow::before, .tooltip-light.bs-tooltip-top .arrow::before {
    border-top-color: #fefefe;
}
.tooltip-light.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .tooltip-light.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #fefefe;
}
.tooltip-light.bs-tooltip-auto[x-placement^=left] .arrow::before, .tooltip-light.bs-tooltip-left .arrow::before {
    border-left-color: #fefefe;
}

.tooltip-light.bs-tooltip-auto[x-placement^=right] .arrow::before, .tooltip-light.bs-tooltip-right .arrow::before {
    border-right-color: #fefefe;
}
.tooltip-light .tooltip-inner {
    color: #000;
    background-color: #fefefe;
}
.tooltip-nopadding .tooltip-inner {
    padding:0
}
.tooltip-seatview, .tooltip-seatview * {
    pointer-events:none
}
.tooltip-seatview.show {
    opacity:1;
}
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.btn {
    border-radius : 100px;
    padding-left:30px;
    padding-right:30px;
}
.btn.initialpadding {
    padding: 0.375rem 0.75rem;
}
.btn-decrement, .btn-increment {
    padding-left: 0px;
    padding-right: 0px;
}
.nav-link.btn {
    display: block;
}

.nav-pills .nav-link.btn {
    border-radius: 100px;
}
.btn-with-arrow-above:after, .btn-with-arrow-below:after {
    font-family: "Font Awesome 6 Free";
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-weight: 900;
    margin-left: 7px;
}

.btn-with-arrow-below:not(.collapsed):after {
    content: "\f077";
}

.btn-with-arrow-below:after {
    content: "\f078";
}

.btn-with-arrow-above:not(.collapsed):after {
    content: "\f078";
}

.btn-with-arrow-above:after {
    content: "\f077";
}

.btn:not([disabled]):hover, .btn:not([disabled]):active, .btn:not([disabled]):focus {
    text-decoration: none;
    cursor: pointer
}

.btn-primary {
    color: @btnPrimaryTextColor;
    background-color: @primaryColor;
    border-color: @primaryColor;
}

.btn-primary:not([disabled]):hover, .btn-primary:not([disabled]):active, .btn-primary:not([disabled]):focus {
    background-color: darken(@primaryColor, 20%);
    border-color: darken(@primaryColor, 20%);
}

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle {
    background-color: darken(@primaryColor, 20%)
}

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus {
}

.btn-primary .badge {
    color: @primaryColor;
    background-color: @btnPrimaryTextColor;
}

.btn-secondary {
    color: @btnSecondaryTextColor;
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}

.btn-secondary:not([disabled]):hover, .btn-secondary:not([disabled]):active, .btn-secondary:not([disabled]):focus,
.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show>.btn-secondary.dropdown-toggle {
    background-color: darken(@secondaryColor, 20%);
    border-color: darken(@secondaryColor, 20%)
}

.btn-outline-secondary {
    color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-outline-secondary:hover, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle {
    color: contrast(@secondaryColor);
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-link {
    color: @primaryColor;
    background-color: #f0f0f0;
    border-color: #d9d9d9
}

.btn-link:not([disabled]):hover, .btn-link:not([disabled]):active, .btn-link:not([disabled]):focus {
    background-color: #aaaaaa;
    border-color: #d9d9d9
}

.btn[disabled], .btn.disabled, .btn.disabled:hover, .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
    color: #fff;
    background-color: #aaaaaa;
    border-color: #878787
}

.table-primary, .table-primary > td, .table-primary > th {
    background-color: fade(@primaryColor, 10%);
}

.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-primary thead th {
    border-color: fade(@primaryColor, 10%);
}

.custom-control-input:checked ~ .custom-control-label::before {
    color: @btnPrimaryTextColor;
    border-color: @primaryColor;
    background-color: @primaryColor;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 50%);
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
    border-color: fade(@primaryColor, 30%);
}

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
}

@media (min-width: 576px) {
    .modal-xl-wide {
        max-width: 95%;
    }
}

@media (min-width: 992px) {
    .modal-xl-wide {
        max-width: 800px;
    }
}

@media (min-width: 1200px) {
    .modal-xl-wide {
        max-width: 1140px;
    }
}

/* couleurs des categories */
.loopCategColors(@count) when (@count > 0) {
  .loopCategColors((@count - 1));
  @categcount: "categ@{count}";
  #oneManifs .categorieName.categNo@{count} { border-color: @@categcount; }
  #oneManifs .seatsByCategory.categNo@{count} .badge { background-color: @@categcount; color:contrast(@@categcount); }
  svg .categNo@{count} { background-color: @@categcount; fill: @@categcount; }
  #modalLinkConsumersToPrices #AllPricesMasterAdhesion .categorieName.categNo@{count} { border-color: @@categcount; }
  #modalLinkAvantageCardToPrices #AllPricesAdvantage .categorieName.categNo@{count} { border-color: @@categcount; }
  .seatPlan-category-item.categNo@{count} { background-color: @@categcount; }
}
.loopCategColors(99);
/******UN EVENT********/
#calendarCategsPricesWrapper {
    display: flex;
    overflow: hidden;
    transition: all .3s ease;
    height: auto;
}

#calendarCategsPricesWrapper h4 {
    padding-bottom: 15px;
}

#calendarCategsPricesWrapper h4:after {
    padding-top: 15px;
    border-bottom: 1px solid @secondaryColor;
    content: '';
    height: auto;
    width: 25%;
    display: block;
    margin: auto;
}

#calendarWrapper, #categsAndPricesWrapper {
    padding: 1px;
    width: 100%;
    flex-shrink: 0;
    height: 100%;
    transition: all .3s ease;
}
/* calendar */
.calendar .calendar-hd { border-radius: 15px 15px 0 0; padding: 0; background-color: @secondaryColor;  }
.calendar .view-month .calendar-hd { border-radius: 15px; }
.calendar .week { background-color: fade(@secondaryColor, 35%); border-radius: 0 0 15px 15px; }
.calendar .day_6, .calendar .day_0 { background-color: fade(@secondaryColor, 50%); border-radius:0; }
.calendar .calendar-hd, .calendar .week { color: @btnSecondaryTextColor; }
.calendar .calendar-arrow { position: relative; }
.calendar .calendar-arrow, .calendar  .calendar-display { color: @btnPrimaryTextColor; }
.calendar-arrow span:hover, .calendar-display .m, .calendar-display:hover { color: @btnPrimaryTextColor; opacity: .75; }
.calendar .calendar-views .notavailable { color: #fff; }
.calendar .calendar-views .notavailable .dot {
    background-image: url('../../img/sessionnotavailable.png');
    background-color: red;
}
.calendar .calendar-views .days li.selected.notavailable .dot {
    background-image: url('../../img/sessionnotavailable.png');
}
.calendar .calendar-views .dot { border-color: @secondaryColor; }
.calendar .calendar-views .days li.selected .dot { background: @primaryColor; border-color: @primaryColor; }
.calendar .calendar-views .days li.selected.withevent, .calendar .calendar-views .month-items li.selected { color: @btnPrimaryTextColor; }
.calendar .calendar-views .dot { border-color: @secondaryColor; }
.calendar .calendar-views .days li.selected.notavailable .dot { background-color: desaturate(@secondaryColor, 50%); }
.calendar .calendar-views .notavailable .dot { border-color: desaturate(@secondaryColor, 50%); background-color: desaturate(@secondaryColor, 50%); }

/* Masquer spécifiquement le bouton "Datum auswählen" du calendrier */
.calendar .calendar-label,
.calendar .calendar-btn,
.calendar .calendar-select-date,
.calendar .date-select-button,
.calendar-footer button,
.calendar-actions button,
.calendar .calendar-bottom button {
    display: none !important;
}

/* Masquer les boutons de sélection de date dans le conteneur du calendrier, mais pas le texte informatif */
#calendarjsWrapper .calendar-label,
#calendarjsWrapper .date-select-button,
#calendarjs .calendar-label,
#calendarjs .date-select-button {
    display: none !important;
}

/* S'assurer que le texte informatif reste visible et n'apparaît pas comme un bouton */
.calendar .calendar-info,
.calendar .info-text,
.calendar .message,
.calendar .notice,
.calendar button:not([type]):not(.calendar-nav):not(.calendar-arrow),
.calendar input[type="button"]:not(.calendar-nav) {
    display: block !important;
    background: none !important;
    border: none !important;
    padding: 10px !important;
    text-align: center !important;
    cursor: default !important;
    box-shadow: none !important;
    outline: none !important;
    color: inherit !important;
    font-weight: normal !important;
}

/* Masquer spécifiquement les boutons de navigation et de sélection */
.calendar button[type="button"],
.calendar .calendar-nav,
.calendar .calendar-arrow button,
.calendar .btn-primary,
.calendar .btn-secondary {
    display: none !important;
}

#alleventhours { display: flex; justify-content: center; flex-wrap: wrap; }
#alleventhours:not(:empty) { margin-bottom: 20px; }

#alleventhours .onehour:not(.notavailable):not(.disabled):hover { cursor: pointer; }
#alleventhours .onehour .beforehover, #alleventhours .onehour .afterhover, #alleventhours .onehour .invisible { width: 100%; }
#alleventhours .onehour .beforehover { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); transition: all .4s ease; }
#alleventhours .onehour .afterhover { position: absolute; left: 50%; top: 200%; transform: translate(-50%, -50%); transition: all .4s ease; font-weight: bold; }
#alleventhours .onehour:not(.notavailable):not(.disabled):hover > .afterhover, #alleventhours .onehour.selected > .afterhover { top: 50%; }
#alleventhours .onehour:not(.notavailable):not(.disabled):hover > .beforehover, #alleventhours .onehour.selected > .beforehover { top: -200%; }
#alleventhours .onehour { text-align: center; transition: padding .3s ease, background-color .3s ease; margin: 0 7px 5px 7px; background-color: transparent; border: 2px solid @secondaryColor; padding: 5px 15px; border-radius: 1000px; position: relative; overflow: hidden; }
#alleventhours .onehour.selected { background-color: @primaryColor; color: @btnPrimaryTextColor; border-color: @primaryColor; padding: 5px 6%; }
#alleventhours .onehour.notavailable {
    color: @btnSecondaryTextColor;
    background-color: desaturate(@secondaryColor, 50%);
    border-color: desaturate(@secondaryColor, 50%);
    background-image: url('../../img/sessionnotavailable.png');
}
#alleventhours .onehour.disabled { 
     opacity:.5;
}

#alleventhours .noevent { background: #bbb; color: #fff; content: ""; height: 40px; line-height: 40px; padding: 0 6%; display: block; border-radius: 1000px; }
#selectedSeance, #TarifBackToPlan {
    margin-bottom: 10px;
}

#alleventhours .onehour.notavailable.nodisponible {
    background-color: transparent !important; 
    color: #000000 !important; 
    border: none !important;
    padding: 0 !important;
    margin: 0 7px 5px 7px !important; 
    font-size: inherit !important; 
    line-height: normal !important;
    box-shadow: none !important; 
}



#selectedSeance.linked:before, #TarifBackToPlan:before {
    content: "\f053";
    margin-right: 10px;
    font-family: "Font Awesome 6 Free";
    font-weight: bold;
}
/* infos */
#resumeAddShoppingCartAUTO, #resumeAddShoppingCartPLAN {
    /*pointer-events: none;
    background-image: linear-gradient(transparent, @globalBackgroundColor, @globalBackgroundColor);
    padding-top: 30px;
    padding-bottom: 15px;*/
    text-align: right;
}
#resumePreShoppingCartPLAN .validSelectionBigMap {position:relative; overflow:hidden;}
#resumePreShoppingCartPLAN .validSelectionBigMap .beforehover, #resumePreShoppingCartPLAN .validSelectionBigMap .afterhover, #resumePreShoppingCartPLAN .validSelectionBigMap .invisible { width: 100%; }
#resumePreShoppingCartPLAN .validSelectionBigMap .beforehover { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);  }
#resumePreShoppingCartPLAN .validSelectionBigMap .afterhover { position: absolute; left: 50%; top: 150%; transform: translate(-50%, -50%); }

#resumePreShoppingCartPLAN .validSelectionBigMap.flip .beforehover,
#resumePreShoppingCartPLAN .validSelectionBigMap.flip .afterhover {
    transition: all .4s ease;
}
#resumePreShoppingCartPLAN .validSelectionBigMap.flip > .afterhover { top: 50%; }
#resumePreShoppingCartPLAN .validSelectionBigMap.flip > .beforehover { top: -200%; }

/* auto ou plan */
#oneManifs #AutoAndSeatPlan {
    margin-top: 15px;
}

#TitleFreePlacement, #TitleChoiceAuto, #TitleChoicePlan {
    text-align:center;
    font-weight:600;
}
#oneManifs #AutoAndSeatPlan .nav-separ {
    padding: .5rem 1rem;
}

#oneManifs #ChoiceAuto, #oneManifs #ChoicePlan {
    padding: 15px 0;
}

#oneManifs .SeePlanSalleGrilleTarif, #oneManifs .SeePlanSallePano {
    padding-top:15px;
}
#seatPlan-imgPlan-item {
    margin-bottom:20px;
}
.seatPlan-category-item-wrapper {
    text-align:left;
}
.seatPlan-category-item-name {
    font-weight:700;
}
.seatPlan-category-item-price {
    display:block;
    font-size:.8em;
    line-height:1.3em;
}
.seatPlan-category-item-pricename {
    display:block;
    font-size:.8em;
    line-height:1.2em;
    font-style:italic;
    opacity:.8;
}
#seatPlan-categories-container {
    gap: 15px;
    display: flex;
    justify-content: center;
    flex-wrap:wrap;
}
.seatPlan-category-item {
    height: 15px;
    width: 15px;
    border-radius: 100px;
    display: inline-block;
}

/* selection des categ/tarifs */
#oneManifs .categWrapper {
    border-bottom: 1px solid #ccc;
    margin-bottom: 15px;
}

#oneManifs .categheadWrapper {
    text-align: center;
    margin-bottom: 15px;
}

#oneManifs .categheadWrapper:hover {
    cursor: pointer;
}

#oneManifs .categorieName {
    display:inline-block;
    text-transform: uppercase;
    padding-bottom: 7px;
    margin-bottom:7px;
    border-bottom: 3px solid #ccc;
}

#oneManifs .categheadWrapper .categorieName:after {
    font-family: "Font Awesome 6 Free";
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-weight: 900;
    margin-left: 7px;
}

#oneManifs .categheadWrapper:not(.collapsed) .categorieName:after {
    content: "\f077";
}

#oneManifs .categheadWrapper.collapsed .categorieName:after {
    content: "\f078";
}

#oneManifs .categWrapper .offerGroup:not(:last-child),#oneManifs .categWrapper .tarifLigne:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, .05);
    margin-bottom: 4px;
}
#oneManifs .categWrapper .offerGroup {
    position: relative;
}
#oneManifs .categWrapper .offerGroup[data-multiplegp="True"]:not([data-offerid="0"]):before {
    content: '';
    width: 3px;
    background-color: @primaryColor;
    display: block;
    position: absolute;
    left: 0;
    top: 15px;
    height: calc(100% - 30px);
}
#oneManifs .categWrapper .offerGroup {
    padding-left: 10px;
}
#oneManifs .categWrapper .tarifLigne.activated .tarifName {
    line-height: 1.2rem;
}
#oneManifs .categWrapper .tarifLigne .tarifName, #oneManifs .categWrapper .tarifLigne .categorieSelect {
    opacity: .5
}
#oneManifs .categWrapper .tarifLigne.activated .tarifName, #oneManifs .categWrapper .tarifLigne.activated .categorieSelect {
    opacity: initial;
}

#oneManifs .categWrapper .tarifLigne {
    padding-bottom: 4px;
    padding-top: 4px;
}

#oneManifs .categWrapper .tarifLigne .amount {
    color: @secondaryColor;
    font-weight: 700;
}
#oneManifs .categWrapper .tarifLigne .feeamount {
   opacity:.7
}

#oneManifs .customSpinner input, #oneManifs .customSpinner input:focus, #oneManifs .customSpinner input:valid, #oneManifs .customSpinner input:invalid {
    color: @secondaryColor;
    font-weight: 700;
    background-color: transparent;
    border-color: transparent !important;
    box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
}

#oneManifs .customSpinner input:disabled {
    opacity:.5;
}

#oneManifs .customSpinner .input-group-append .btn {
    border-top-left-radius: 100px;
    border-bottom-left-radius: 100px;
}

#oneManifs .customSpinner .input-group-prepend .btn {
    border-top-right-radius: 100px;
    border-bottom-right-radius: 100px;
}

#oneManifs .customSpinner .input-group-append .btn.disabled, #oneManifs .customSpinner .input-group-prepend .btn.disabled {
    pointer-events: none;
}

#oneManifs .totalPlacesWrapper {
    border-radius: 4px;
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, .05)
}

#oneManifs .totalPlacesWrapper #totalPlacesAmount {
    font-weight: bold;
    color: @primaryColor;
}

.addPlaces .totalPlacesAmount {
    font-weight:700;
}

@media (max-width:767.98px) {
    #oneManifs .tarifName, #oneManifs .eventName, #oneManifs .eventImage, #oneManifs .eventPlace {
        text-align: center;
    }
}

/**/
#oneManifs .seatsByCategory .badge {
    font-size: 100%;
}

/*********modal link consumers to product adhesion **********/
#modalLinkConsumersToPrices .oneAdhesionProduct .img-adhesion {
    display: inline-block;
    width: 62px;
    height: 62px;
    background-color: @primaryColor;
    color: contrast( @primaryColor);
    text-align: center;
    line-height: 65px;
    border-radius: 1000px;
    font-size: 2.15em;
}
#modalLinkConsumersToPrices .oneAdhesionProductAdded .img-adhesion {
    display: inline-block;
    width: 28px;
    height: 28px;
    background-color: @primaryColor;
    color: contrast( @primaryColor);
    text-align: center;
    line-height: 30px;
    border-radius: 1000px;
    font-size: 1em;
}
#modalLinkConsumersToPrices .oneAdhesionProduct .highlight, #modalLinkConsumersToPrices .oneAdhesionProductAdded .highlight {
    color: @primaryColor;
}
#modalLinkConsumersToPrices .oneAdhesionProduct .productAdhesionWrapper {
    line-height: 1.3rem;
}

#modalLinkConsumersToPrices #AllPricesMasterAdhesion select.error, #modalLinkConsumersToPrices #AllPricesMasterAdhesion input.error, #modalLinkConsumersToPrices #AllPricesMasterAdhesion label.error {
    color: #dc3545;
    border-color: #dc3545;
}
#modalLinkConsumersToPrices #AllPricesMasterAdhesion .form-row:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, .05);
    margin-bottom: 4px;
    padding-bottom: 6px;
}
#modalLinkConsumersToPrices #AllPricesMasterAdhesion .selectComeWith option:disabled {
   display:none;
}

#modalLinkConsumersToPrices #AllPricesMasterAdhesion .categWrapper:not(:last-child) {
    border-bottom: 1px solid #ccc;
    margin-bottom: 15px;
}

#modalLinkConsumersToPrices #AllPricesMasterAdhesion .categheadWrapper {
    text-align: center;
    margin-bottom: 15px;
}

#modalLinkConsumersToPrices #AllPricesMasterAdhesion .categheadWrapper:hover {
    cursor: pointer;
}

#modalLinkConsumersToPrices #AllPricesMasterAdhesion .categorieName {
    text-transform: uppercase;
    border-bottom: 3px solid #ccc;
    margin-bottom: 20px;
    display: inline-block;
}
#modalLinkConsumersToPrices .selectConsumersForPrice option.highlight {
    font-weight: bold;
}
/*********modal advatange card **********/
#modalLinkAvantageCardToPrices #AllPricesAdvantage .categorieName {
    text-transform: uppercase;
    border-bottom: 3px solid #ccc;
    margin-bottom: 20px;
    display: inline-block;
}
#modalLinkAvantageCardToPrices #AllPricesAdvantage .categWrapper:not(:last-child) {
    border-bottom: 1px solid #ccc;
    margin-bottom: 15px;
}

#modalLinkAvantageCardToPrices #AllPricesAdvantage select.error, #modalLinkAvantageCardToPrices #AllPricesAdvantage input.error, #modalLinkAvantageCardToPrices #AllPricesAdvantage label.error {
    color: #dc3545;
    border-color: #dc3545;
}
#modalLinkAvantageCardToPrices #AllPricesAdvantage .form-row:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, .05);
    margin-bottom: 4px;
    padding-bottom: 6px;
}
/*********modal carnet de ticket **********/
.onefeedbook-delete:hover {
    cursor:pointer;
}

/************ PANO & BIG MAP ***************/
#panoAndBigMapWrapper {
    display: flex;
    overflow: hidden;
    transition: all .3s ease;
    height: auto;
}

#panoWrapper, #bigMapWrapper, #mapGrilleTarifWrapper {
    padding: 1px;
    width: 100%;
    flex-shrink: 0;
    height: 100%;
    transition: all .3s ease;
}

#backToPano:before {
    content: "\f053";
    margin-right: 10px;
    font-family: "Font Awesome 6 Free";
    font-weight: bold;
}

/************ PANO ***************/
#pano {
    /*height: 400px !important;*/
    aspect-ratio: @panoratio;
}
/************ BIG MAP *********/
#eventMap {
    box-shadow: inset 0px 0px 20px 0px rgba(0, 0, 0, .15);
    max-height: 400px;
    overflow: hidden;
    position: relative;
}

#eventMapInner {
    max-height: 400px;
    overflow: hidden;
    position: relative;
}

#eventMap, #eventMapInner {
    height:400px 
}

#eventMap, #eventMap * {
    touch-action: none;
    user-select: none;
}

#mapcontrols {
    padding-top: 5px;
    text-align: center;
}

#editPricesSelection .removeSeatFromSelection {
    color: #D71921;
}

#editPricesSelection .removeSeatFromSelection:hover {
    cursor: pointer;
}

#divPlanSalleWrapperAll {
    margin: auto;
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: 50% 50%;
}

#divPlanSalleWrapper {
    transform-origin: 0;
}

#divPlanSalle {
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
}
/* fix svg text overlap glitch */
#svgPlanSalle.fixtextglitch {
   margin-bottom: 1px;
}
#svgPlanSalle {
    overflow: overlay;
    position: relative;
    z-index: 2;
}

#svgPlanSalle .trait {
    stroke: rgba(0, 0, 0, 0.3);
    stroke-width: 1;
}

#svgPlanSalle .poteau {
    fill: transparent;
    stroke: rgba(0, 0, 0, 0.3);
    stroke-width: 2;
}

#svgPlanSalle .textrang {
    font-weight: bold;
    fill: rgba(0, 0, 0, 0.3);
}

#svgPlanSalle .textlong {
    fill: #000000;
    font-size: 16px;
    opacity: 0.5;
}

#divForBackgroundSalle {
    position: absolute;
    top: 0;
    z-index: 1;
}

svg .siegeall.simple {
    -webkit-transition: 1s ease;
    -o-transition: 1s ease;
    transition: 1s ease;
    stroke: white;
}
svg .siegeall.detailled .siegebg {
     -webkit-transition: 1s ease;
    -o-transition: 1s ease;
    transition: 1s ease;
    stroke: black;
}
svg .siegeall .siegeInner {
    transform-origin: 9.5px 9.5px;
}
svg .siegeall .siegedark { opacity: 0.15; }
svg .siegeall .siege50 { opacity: 0.4; }
svg .siegeall .siegelight { fill: #FFFFFF; }
svg .siegeall[data-orientation="S"] .siegeInner  {
    transform : rotate(0deg);
}
svg .siegeall[data-orientation="SO"] .siegeInner  {
    transform : rotate(45deg);
}
svg .siegeall[data-orientation="SE"] .siegeInner  {
    transform : rotate(-45deg);
}
svg .siegeall[data-orientation="N"] .siegeInner {
    transform : rotate(180deg);
}
svg .siegeall[data-orientation="NO"] .siegeInner  {
     transform : rotate(135deg);
}
svg .siegeall[data-orientation="NE"] .siegeInner {
     transform : rotate(225deg);
}
svg .siegeall[data-orientation="E"] .siegeInner {
    transform : rotate(270deg);
}
svg .siegeall[data-orientation="O"] .siegeInner {
    transform : rotate(90deg);
}
svg .siegeall[data-free="False"], svg .siegeall[data-free="False"] .siegebg, #svgPlanSalle .pole .poleInnerColor {
    fill: #ccc;
}
#svgPlanSalle .pole .poleBorderColor {
    fill: darken(#ccc, 50%);
}
svg .siegeall.simple.mineTemp, svg .siegeall.simple.mine, svg.PlanPhoto .siegeall.mineTemp, svg.PlanPhoto .siegeall.mine {
    fill: @seatSelected;
    animation: seatSelected 1s ease-in-out infinite reverse;
    stroke-width: 1px;
    stroke: darken(@seatSelected,30%)
}
svg .siegeall.detailled.mineTemp .siegebg, svg .siegeall.mine .siegebg{
    fill: @seatSelected;
    animation: seatSelected 1s ease-in-out infinite reverse;
    stroke-width: 1px;
    stroke: darken(@seatSelected,30%)
}

svg .siegeall[data-free="True"]:hover {
    cursor: pointer;
}

#svgPlanSalleMini .siegeall.NotHighted {
    fill: rgba(0, 0, 0, .3);
}

svg .siegeall.simple.disabled {
    fill: #ccc;
    pointer-events: none;
}
svg .siegeall.detailled.disabled .siegebg {
    fill: #ccc;
    pointer-events: none;
}

svg .siegeall[data-reserveid="7480021"] {
background-image: url('nouveau.svg');
}

.tooltip-seatview .tooltip-inner {
    background-color:#eee;
}
.tooltip-seatview.bs-tooltip-auto[x-placement^=top] .arrow::before, .tooltip-seatview.bs-tooltip-top .arrow::before {
    border-top-color: #eee;
}
/*.tooltip-seatview .imgViewFromSeat {
    max-width : 200px;
    height : 130px;
}*/
.tooltip-seatview .imgViewFromSeat img {
    max-width: 100%;
}
.tooltip-seatview .rankAndSeat {
    
    text-transform: uppercase;
    line-height: 1em;
    padding: 1rem;
    border-left: 1px solid #fff;
    border-right: 1px solid #fff;
    
    white-space: nowrap;
}
.tooltip-seatview .denomination {
    border-top :2px solid #fff;
}

@-webkit-keyframes seatSelected {
    0% {
        fill: @seatSelected;
        stroke-width: 1px;
    }

    50% {
        fill: lighten(@seatSelected, 50%);
        stroke-width: 3px;
    }

    100% {
        fill: @seatSelected;
        stroke-width: 1px;
    }
}

@keyframes seatSelected {
    0% {
        fill: @seatSelected;
        stroke-width: 1px;
    }

    50% {
        fill: lighten(@seatSelected, 50%);
        stroke-width: 3px;
    }

    100% {
        fill: @seatSelected;
        stroke-width: 1px;
    }
}