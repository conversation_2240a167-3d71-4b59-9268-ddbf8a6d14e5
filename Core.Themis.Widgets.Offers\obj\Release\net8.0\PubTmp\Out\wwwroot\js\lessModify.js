if(SettingsMerge!=null && SettingsMerge != undefined && SettingsMerge != "") {
	var btnBuyBackgroundColor2 = "''"
	if(SettingsMerge.global.btnBuyBackgroundColor2 != "") {
		btnBuyBackgroundColor2 = SettingsMerge.global.btnBuyBackgroundColor2
	}

	var homeModularBlocksColor = SettingsMerge.global.primaryColor;
	if (SettingsIndivMerge != undefined && SettingsIndivMerge.home.blocksColor != undefined) {
		homeModularBlocksColor = SettingsIndivMerge.home.blocksColor
	}

   	var lessJson = {
		//global
		"@globalFontName": '"' + SettingsMerge.global.globalFontName + '"',
		"@globalFontColor": SettingsMerge.global.globalFontColor,
		"@globalLinkColor": SettingsMerge.global.globalLinkColor,
		"@globalBackgroundColor": SettingsMerge.global.globalBackgroundColor,
		"@globalBackgroundImage": '"' + SettingsMerge.global.globalBackgroundImage + '"',
		"@welcomeBackgroundImage": '"' + SettingsMerge.global.welcomeBackgroundImage + '"',
		"@btnBuyBackgroundColor1": SettingsMerge.global.btnBuyBackgroundColor1,
		"@btnBuyBackgroundColor2": btnBuyBackgroundColor2,
		"@btnBuyTextColor": SettingsMerge.global.btnBuyTextColor,
		"@primaryColor": SettingsMerge.global.primaryColor,
		"@btnPrimaryTextColor": SettingsMerge.global.btnPrimaryTextColor,
		"@secondaryColor": SettingsMerge.global.secondaryColor,
		"@btnSecondaryTextColor": SettingsMerge.global.btnSecondaryTextColor,
		"@menuHeaderColor": SettingsMerge.global.menuHeaderColor,
		"@menuHeaderTextColor": SettingsMerge.global.menuHeaderTextColor,
		"@topNavBarBackgroundColor": SettingsMerge.global.topNavBarBackgroundColor,
		"@topNavBarTextColor": SettingsMerge.global.topNavBarTextColor,
		//plan
		"@seatSelected": SettingsMerge.plan.seatSelected,
		/*"@categ1": SettingsMerge.plan.categ1,
		"@categ2": SettingsMerge.plan.categ2,
		"@categ3": SettingsMerge.plan.categ3,
		"@categ4": SettingsMerge.plan.categ4,
		"@categ5": SettingsMerge.plan.categ5,
		"@categ6": SettingsMerge.plan.categ6,
		"@categ7": SettingsMerge.plan.categ7,
		"@categ8": SettingsMerge.plan.categ8,
		"@categ9": SettingsMerge.plan.categ9,
		"@categ10": SettingsMerge.plan.categ10,
		"@categ11": SettingsMerge.plan.categ11,
		"@categ12": SettingsMerge.plan.categ12,
		"@categ13": SettingsMerge.plan.categ13,
		"@categ14": SettingsMerge.plan.categ14,
		"@categ15": SettingsMerge.plan.categ15,
		"@categ16": SettingsMerge.plan.categ16,
		"@categ17": SettingsMerge.plan.categ17,
		"@categ18": SettingsMerge.plan.categ18,
		"@categ19": SettingsMerge.plan.categ19,
		"@categ20": SettingsMerge.plan.categ20,
		"@categ21": SettingsMerge.plan.categ21,
		"@categ22": SettingsMerge.plan.categ22,
		"@categ23": SettingsMerge.plan.categ23,
		"@categ24": SettingsMerge.plan.categ24,
		"@categ25": SettingsMerge.plan.categ25,
		"@categ26": SettingsMerge.plan.categ26,
		"@categ27": SettingsMerge.plan.categ27,
		"@categ28": SettingsMerge.plan.categ28,
		"@categ29": SettingsMerge.plan.categ29,
		"@categ30": SettingsMerge.plan.categ30,
		"@categ31": SettingsMerge.plan.categ31,
		"@categ32": SettingsMerge.plan.categ32,
		"@categ33": SettingsMerge.plan.categ33,
		"@categ34": SettingsMerge.plan.categ34,
		"@categ35": SettingsMerge.plan.categ35,
		"@categ36": SettingsMerge.plan.categ36,
		"@categ37": SettingsMerge.plan.categ37,
		"@categ38": SettingsMerge.plan.categ38,
		"@categ39": SettingsMerge.plan.categ39,
		"@categ40": SettingsMerge.plan.categ40,
		"@categ41": SettingsMerge.plan.categ41,
		"@categ42": SettingsMerge.plan.categ42,
		"@categ43": SettingsMerge.plan.categ43,
		"@categ44": SettingsMerge.plan.categ44,
		"@categ45": SettingsMerge.plan.categ45,
		"@categ46": SettingsMerge.plan.categ46,
		"@categ47": SettingsMerge.plan.categ47,
		"@categ48": SettingsMerge.plan.categ48,
		"@categ49": SettingsMerge.plan.categ49,
		"@categ50": SettingsMerge.plan.categ50,*/
		"@panoratio": SettingsMerge.pano.ratio,
		//homemodular
		"@homemodularblockscolor": homeModularBlocksColor
	}
	$.each(SettingsMerge.plan, function (i, k) {
		var regexcat = new RegExp(/^categ[1-9]\d*$/gm);
		if (regexcat.test(i)) {
			lessJson["@" + i] = SettingsMerge.plan[i]
		}
	})
	if(SettingsMerge.global.globalFontUrl != "" && SettingsMerge.global.globalFontUrl != undefined) {
        lessJson["@globalFontUrl"] = '"' + SettingsMerge.global.globalFontUrl + '"'
    }	
}
less.pageLoadFinished.then(function () {
	less.modifyVars(lessJson);
	less.refreshStyles();
	console.log('less.modifyVars DONE')
	if ($.isFunction(window.lessReady)){ 
    	lessReady()
	}else {
		lessReadyfunc.apply()
	}
    
});