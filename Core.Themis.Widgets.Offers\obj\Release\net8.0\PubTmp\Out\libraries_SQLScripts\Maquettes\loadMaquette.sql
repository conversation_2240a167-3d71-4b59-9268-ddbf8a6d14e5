--select ,maquette_billet_id from maquette_billet
--WHERE maquette_billet_id = @pmaquetteId

SELECT taille_x, taille_y, taille_coupon_1, taille_coupon_2, fond_bmp, code, typesupport,
maquette_billet_id, typemaquette 
FROM maquette_billet WHERE maquette_billet_id = @pmaquetteId

SELECT driver, description, position, champs_no,maquette_billet_id FROM (
	SELECT driver,
	CASE driver WHEN 'CHAMPS' THEN ligne else '' END as description, 
	CASE WHEN driver = @pdriver THEN ligne ELSE '' END as Position, 
	champs_no,maquette_billet_id FROM ligne_maquette_billet
	WHERE maquette_billet_id = @pmaquetteId and driver in ('CHAMPS', @pdriver)
) t ORDER BY champs_no, driver