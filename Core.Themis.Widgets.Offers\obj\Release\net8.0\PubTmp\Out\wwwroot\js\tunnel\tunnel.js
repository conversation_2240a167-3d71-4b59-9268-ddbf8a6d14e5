﻿$(document).ready(function () {
  
    initChangeModalAndCollapse()
   
    sendIframeSize()

    console.log('tunnel is ready')

    // lauch session
    tunnel_callSession();




   
}); 


function tunnel_callSession() {
    //alert(resultCreateUser.id);
    //document.getElementById("WebUserId").innerHTML = resultCreateUser.id;

    //var langCode = "fr"
    //var webUserId = 55
    //var ForceDate = '0';
    //var ForceSession = 491;
    //var structureid = 994; //si vous voulez changer de structure id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?structureid=991 
    //var eventid = 32; //si vous voulez changer de event id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?eventid=60
    //var identityid = 0; //si vous voulez changer de identityid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?identityid=473 
    //var buyerprofilid = 0; //si vous voulez changer de buyerprofilid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?buyerprofilid=789 
    // call widget offre core


    console.log("tunnel_callSession");
    console.log("mySettings:" + mySettings);



    SphereOffers.Session({
        selector: '#widgetsContainer',
        partnerName: partnername,
        structureId: structureId,
        langCode: langCode,
        eventId: eventId,
        identityId: identityId,
        webUserId: webUserId,
        forceDate: ForceDate,
        forceSession: ForceSession,
        buyerProfilId: buyerProfilId,
        mySettings: mySettings,
        useContext: "tunnel",
        //signature: "CIZIT64et7R67PZGLvyxpgAruU8cttRh3M8kOZkroP0="
        signature: widgetSignatureCallsGet
    })
}

function tunnel_callBasket(_basketId) {
    console.log("tunnel_callBasket");
    SphereOffers.Basket({
        selector: '#widgetsContainer',
        partnerName: partnername,
        structureId: structureId,
        langCode: langCode,
        basketId: _basketId,
        lasteventId: eventId,
        identityId: identityId,
        webUserId: webUserId,
        buyerProfilId: buyerProfilId,
        mySettings: mySettings,
        signature: widgetSignatureCallsGet,
        useContext: "tunnel",
        //signature: "CIZIT64et7R67PZGLvyxpgAruU8cttRh3M8kOZkroP0="
    })
}




// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
// (custom pour la page "Events")
function lessReady() {
    sendIframeSize()
    console.log('tunnel.js lessReady READY')

}



    $(document).ready(function() {
        window.addEventListener('message', iframeListener);	
})

    /******** أcoute des iframes ******/
function iframeListener(event) {
        // ecoute les events + informe le parent

    switch (event.data.action) {

        case ("parentwindowGoTo"):
            window.location = event.data.purlToGo;
            break;
    	//si redirection via url
            case ("urltogo"):
                
                var msg = {
                    "action": "urltogo",
                    "url": event.data.url
                }
                window.parent.postMessage(msg, "*")
                break;

            //window.location = event.data.purlToGo;
         break;
        // ajout au panier
        case ("addBasket"):

            ///// call basket widget
            basketId = event.data.basketId;

            tunnel_callBasket(event.data.basketId)

            var msg = {
                "action": "addBasket",
                "basketId": event.data.basketId,
                "hash": event.data.hash
                }

                //alert(identite.identiteId)
                //alert("loginOk")
            window.parent.postMessage(msg, "*")
            break;

        case "loginOk":
            console.log(event.data)

      
                identityId = event.data.identiteId;
      

                tunnel_callBasket(basketId)

                var msg = {
                    "action": "loginOk",
                    "identiteId": event.data.identiteId,
                    "hashKey": event.data.hashKey,
                }


                window.parent.postMessage(msg, "*")

            
            break;
        case ("backtolastevent"):
            tunnel_callSession(event.data.basketId)
            break;


    //si ajout au panier
    case ("getCommentairesSeance"):

    break;
    }

   
}



