﻿/* getinfoResaForBasket */

declare @LANGUAGEID int = @pLangId
declare @COMMANDEID int = @pOrderId

DECLARE @SQL2 VARCHAR(4000), @SQLTRADUCTION VARCHAR(2000),@SQL_MONTANT  VARCHAR(3000)
DECLARE @SQL VARCHAR(4000),@SQLINNERMAQUETTEID VARCHAR(2000),@SQLMAQUETTEID VARCHAR(2000),@PRODUIT VARCHAR(50)
DECLARE  @DOSSIERID INT,@MANIFID INT,@FORMULEID INT, @SEANCEID INT,@NBENVOI INT,@PRODUITID INT,  @ISPDF VARCHAR(10)
DECLARE @AUTREMONTANT DECIMAL(18,10)
DECLARE @SQLLISTFRAIS VARCHAR(2000), @SQLLISTPRODUIT VARCHAR(2000)
--Set @LANGUAGEID=0
--set @COMMANDEID=318686
CREATE TABLE #T2 (entree_id INT, reference_unique_physique_id INT, type_tarif_id INT,
categorie_id INT, seance_id INT, manifestation_id INT, formule_id INT, type_envoi_id INT, 
maquette_id INT,montant DECIMAL(18,10),frais DECIMAL(18,10) )

-- FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 
DECLARE LISTDOS CURSOR SCROLL FOR 
	SELECT  cl.formule_id,clp.dossier_id, clp.manifestation_id,clp.seance_id, montant4+montant5+montant6+montant7+montant8+montant9 +montant10 as AutreMontant
	FROM commande_ligne_comp clp 
	INNER JOIN commande_ligne cl on cl.commande_ligne_id=clp.commande_ligne_id 
	WHERE clp.etat='R' and cl.type_ligne='DOS' and  clp.commande_id= @COMMANDEID;

DECLARE LISTENVOI CURSOR SCROLL FOR 
	SELECT cl.formule_id,cl.manifestation_id, cl.seance_id, p.produit_id,p.produit_nom, case when p.maquettebillet_id > 0 then 'PDF' else 'NOTPDF' end
	FROM commande_ligne cl 
	INNER JOIN commande_ligne_comp clp on clp.commande_ligne_id =cl.commande_ligne_id
	INNER JOIN dossier_produit d on d.dos_prod_id = cl.dossier_id
	INNER JOIN produit p on p.produit_id=d.produit_id
	WHERE clp.etat='R' and cl.type_ligne='PRO' and p.internet=1 and p.groupe_id=6
	AND  clp.commande_id= @COMMANDEID;

--DECLARE LISTMAQUETTE CURSOR SCROLL FOR select maquette_billet_id from VueGestionPlaceFormuleSeanceTarifTypeEnvoi
--where formule_id=@FORMULEID and manif_id=@MANIFID and seance_id=@SEANCEID and produit_id=@PRODUITID;

SET @PRODUITID =0 ;

OPEN LISTENVOI; 
FETCH NEXT FROM LISTENVOI INTO @FORMULEID,@MANIFID,@SEANCEID, @PRODUITID, @PRODUIT, @ISPDF ; 
SET @NBENVOI =0 ;

WHILE @@FETCH_STATUS=0 
BEGIN
	SET @NBENVOI = @NBENVOI +1;
	FETCH NEXT FROM LISTENVOI INTO @FORMULEID,@MANIFID,@SEANCEID, @PRODUITID, @PRODUIT, @ISPDF ; 
END
CLOSE LISTENVOI; 
DEALLOCATE LISTENVOI; 

--PRINT @NBENVOI;
SET @SQL ='' ;

OPEN LISTDOS; 
 FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

WHILE @@FETCH_STATUS=0 
BEGIN 
	SET @SQLMAQUETTEID='0 as maquette_id';
	SET @SQLINNERMAQUETTEID='';

	IF @NBENVOI = 1 and @ISPDF ='PDF' /* un seul mode obtention ET produit.maquettebillet_id > 0 */
		BEGIN
		IF @FORMULEID > 0 
		BEGIN
			SET @SQLMAQUETTEID='isnull(v.maquette_billet_id,0) as maquette_id';
			SET @SQLINNERMAQUETTEID= 'LEFT OUTER JOIN VueGestionPlaceFormuleSeanceTarifTypeEnvoi v on
				v.formule_id= '+LTRIM(STR(@FORMULEID)) +
				'and v.manif_id='+LTRIM(STR(@MANIFID)) + 
				'and v.seance_id='+LTRIM(STR(@SEANCEID)) +  
				'and v.type_tarif_id=e.type_tarif_id'+  
				'and v.produit_id='+LTRIM(STR(@PRODUITID))  ;
		END
		ELSE
		BEGIN
			SET @SQLMAQUETTEID='isnull(v.maquette_id,0) as maquette_id';
			SET @SQLINNERMAQUETTEID= 'LEFT OUTER JOIN VueGestionPlaceManifSeanceCategTarifTypeEnvoi v on
				v.categ_id= e.categorie_id' +
				' and v.manif_id='+LTRIM(STR(@MANIFID)) + 
				' and v.seance_id='+LTRIM(STR(@SEANCEID)) +  
				' and v.type_tarif_id=e.type_tarif_id'+  
				' and v.produit_id='+LTRIM(STR(@PRODUITID));
				SET @SQLINNERMAQUETTEID = ' LEFT OUTER JOIN MAQUETTE_PARAM v ON type_element=''OBTENTION'' AND table_id = '+LTRIM(STR(@SEANCEID)) + ' and masquer=''N'' and type_table=''S'' '
		END
	END

	If @SQL<>''
		SET @SQL = @SQL  + ' UNION ';

	IF @AUTREMONTANT > 0
		SET @SQL_MONTANT = ',e.montant1 + e.montant2 + case  when modecol4=''REMISE''  then - e.montant4 
			when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  e.montant4
			else 0 END +
			case  when modecol5=''REMISE''  then - e.montant5
			when modecol5=''TAXE'' or modecol5=''COMMISSION'' then  e.montant5
			else 0 END +
			case  when modecol6=''REMISE''   then - e.montant6
			when modecol6=''TAXE'' or modecol6=''COMMISSION'' then  e.montant6
			else 0 END +
			case  when modecol7=''REMISE''  then - e.montant7
			when modecol7=''TAXE'' or modecol7=''COMMISSION'' then  e.montant7
			else 0 END +
			case  when modecol8=''REMISE''  then - e.montant8
			when modecol8=''TAXE'' or modecol8=''COMMISSION'' then  e.montant8
			else 0 END +
			case  when modecol9=''REMISE''  then - e.montant9
			when modecol9=''TAXE''or modecol9=''COMMISSION'' then  e.montant9
			else 0 END +
			case  when modecol10=''REMISE''  then - e.montant10
			when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  e.montant10
			else 0 END as montant, e.montant2 as frais	from structure,';
	ELSE
		SET @SQL_MONTANT = ',e.montant1 + e.montant2 as montant, e.montant2 as frais from ';

--PRINT @SQL_MONTANT;

	SET @SQL = 'INSERT INTO #T2 select entree_id, reference_unique_physique_id,e.type_tarif_id, e.categorie_id, e.seance_id,  '
		+LTRIM(STR(@MANIFID)) +' as manifestation_id ,'+LTRIM(STR(@FORMULEID)) +' as formule_id,'
		+LTRIM(STR(@PRODUITID)) +' as type_envoi_id,'+ @SQLMAQUETTEID
		+ @SQL_MONTANT + 'entree_'+LTRIM(STR(@MANIFID)) +' e '+ @SQLINNERMAQUETTEID+' WHERE e.entree_etat = ''R'' and e.dossier_id =' + LTRIM(STR(@DOSSIERID));
	EXEC(@SQL )	
--PRINT @SQL;

FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

END

CLOSE LISTDOS; 
DEALLOCATE LISTDOS; 

SELECT T1.formule_id ,
	case when tfa.form_abon_nom is null then fa.form_abon_nom else tfa.form_abon_nom end as formule_nom,
	l.lieu_id , 
	case when tl.lieu_nom is null then l.lieu_nom else tl.lieu_nom end as lieu_nom,
	m.manifestation_id , 
	case when tm.manifestation_nom is null then m.manifestation_nom else tm.manifestation_nom end manifestation_nom ,
	s.seance_id ,seance_date_deb,entree_id , 
	pos_x, pos_y, siege , rang , 
	c.categ_id ,
	case when tc.categ_nom is null then c.categ_nom else tc.categ_nom end as categ_nom,
	z.zone_id ,
	case when tz.zone_nom is null then z.zone_nom else tz.zone_nom end as zone_nom,
	sec.section_id , 
	case when tsec.section_nom is null then sec.section_nom else tsec.section_nom end as section_nom,
	et.etage_id , 
	case when tet.etage_nom is null then et.etage_nom else tet.etage_nom end as etage_nom, 
	rlp.denomination_id,
	case when tde.denom_nom is null then de.denom_nom else tde.denom_nom end as denom_nom,
	tt.type_tarif_id,
	case when ttt.type_tarif_nom is null then tt.type_tarif_nom else ttt.type_tarif_nom end as type_tarif_nom,
	montant, frais,
	type_envoi_id,
	case when tp.produit_nom is null then isnull(p.produit_nom,'') else tp.produit_nom end as type_envoi,
	maquette_id
	FROM #T2 as T1  
	INNER JOIN categorie c on c.categ_id=T1.categorie_id
	LEFT JOIN traduction_categorie tc on tc.categ_id=c.categ_id and tc.langue_id=@LANGUAGEID

	INNER JOIN type_tarif tt on tt.type_tarif_id=T1.type_tarif_id
	LEFT JOIN traduction_type_tarif ttt on ttt.type_tarif_id=tt.type_tarif_id and ttt.langue_id=@LANGUAGEID

	INNER JOIN seance s on s.seance_id=T1.seance_id 

	INNER JOIN manifestation m on m.manifestation_id=T1.manifestation_id
	LEFT JOIN traduction_manifestation tm on m.manifestation_id=tm.manifestation_id and tm.langue_id=@LANGUAGEID 

	INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id=T1.reference_unique_physique_id 

	LEFT OUTER JOIN formule_abonnement fa on fa.form_abon_id=T1.formule_id
	LEFT OUTER JOIN traduction_formule_abonnement tfa on fa.form_abon_id=tfa.form_abon_id and tfa.langue_id=@LANGUAGEID

	INNER JOIN zone z on z.zone_id=rlp.zone_id 
	LEFT JOIN traduction_zone tz on z.zone_id=tz.zone_id and tz.langue_id=@LANGUAGEID

	INNER JOIN section sec on  sec.section_id=rlp.section_id
	LEFT JOIN traduction_section tsec on sec.section_id=tsec.section_id and tsec.langue_id=@LANGUAGEID

	INNER JOIN denomination de on de.denom_id=rlp.denomination_id  
	LEFT JOIN traduction_denomination tde on tde.denom_id=de.denom_id and tde.langue_id=@LANGUAGEID 

	INNER JOIN etage et on  et.etage_id = rlp.etage_id
	LEFT JOIN traduction_etage tet on et.etage_id = tet.etage_id and tet.langue_id=@LANGUAGEID 

	INNER JOIN lieu l on  l.lieu_id = rlp.lieu_id 
	LEFT JOIN traduction_lieu tl on l.lieu_id = tl.lieu_id and tl.langue_id=@LANGUAGEID

	LEFT OUTER JOIN PRODUIT P ON P.PRODUIT_ID=T1.TYPE_ENVOI_ID 
	LEFT OUTER JOIN traduction_produit tp on tp.produit_id=T1.type_envoi_id and tp.langue_id=@LANGUAGEID

/*************** mode d'obtention */
SELECT cl.formule_id,cl.manifestation_id, cl.seance_id, p.produit_id,
		'MO' as type_ligne,
	case when tp.produit_nom is null then p.produit_nom else tp.produit_nom end as produit_nom,
	clp.nombre ,
		clp.montant1 + clp.montant2 + case when modecol4='REMISE'  then - clp.montant4 
	WHEN modecol4='TAXE' or modecol4='COMMISSION' then  clp.montant4 ELSE 0 END +
	CASE  when modecol5='REMISE'  then - clp.montant5
	WHEN modecol5='TAXE' or modecol5='COMMISSION' then  clp.montant5 ELSE 0 END +
	CASE  when modecol6='REMISE'   then - clp.montant6
	WHEN modecol6='TAXE' or modecol6='COMMISSION' then  clp.montant6 ELSE 0 END +
	CASE  when modecol7='REMISE'  then - clp.montant7
	WHEN modecol7='TAXE' or modecol7='COMMISSION' then  clp.montant7 ELSE 0 END +
	CASE  when modecol8='REMISE'  then - clp.montant8 WHEN modecol8='TAXE' or modecol8='COMMISSION' then  clp.montant8 ELSE 0 END +
	CASE  when modecol9='REMISE'  then - clp.montant9
	WHEN modecol9='TAXE'or modecol9='COMMISSION' then  clp.montant9 ELSE 0 END +
	CASE  when modecol10='REMISE'  then - clp.montant10
	WHEN modecol10='TAXE' or modecol10='COMMISSION' then  clp.montant10 ELSE 0 END as montant,
	montant2 as frais,
	0 as maquette_id
	FROM  structure, commande_ligne cl 
	INNER JOIN commande_ligne_comp clp on clp.commande_ligne_id =cl.commande_ligne_id
	INNER JOIN dossier_produit d on d.dos_prod_id = cl.dossier_id
	INNER JOIN produit p on p.produit_id=d.produit_id
	LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LANGUAGEID
	WHERE clp.etat='R' and cl.type_ligne='PRO'  and p.internet=1 and ( p.groupe_id=6)
	 and  clp.commande_id=@COMMANDEID

/*************** produits */
SELECT cl.formule_id,cl.manifestation_id, cl.seance_id, p.produit_id,  clp.etat,

	CASE when p.groupe_id = 12 then 'PRESA' else 'PROD' end as type_ligne,

	case when tp.produit_nom is null then p.produit_nom else tp.produit_nom end as produit_nom,
	clp.nombre ,
	clp.montant1 + clp.montant2 + case when modecol4='REMISE'  then - clp.montant4 
	WHEN modecol4='TAXE' or modecol4='COMMISSION' then  clp.montant4 ELSE 0 END +
	CASE  when modecol5='REMISE'  then - clp.montant5
	WHEN modecol5='TAXE' or modecol5='COMMISSION' then  clp.montant5 ELSE 0 END +
	CASE  when modecol6='REMISE'   then - clp.montant6
	WHEN modecol6='TAXE' or modecol6='COMMISSION' then  clp.montant6 ELSE 0 END +
	CASE  when modecol7='REMISE'  then - clp.montant7
	WHEN modecol7='TAXE' or modecol7='COMMISSION' then  clp.montant7 ELSE 0 END +
	CASE  when modecol8='REMISE'  then - clp.montant8 WHEN modecol8='TAXE' or modecol8='COMMISSION' then  clp.montant8 ELSE 0 END +
	CASE  when modecol9='REMISE'  then - clp.montant9
	WHEN modecol9='TAXE'or modecol9='COMMISSION' then  clp.montant9 ELSE 0 END +
	CASE  when modecol10='REMISE'  then - clp.montant10
	WHEN modecol10='TAXE' or modecol10='COMMISSION' then  clp.montant10 ELSE 0 END as montant, 
	montant2 as frais,
	CASE when @NBENVOI = 1 and @ISPDF ='PDF' THEN 
		CASE WHEN p.MaquettePDF_ID is null THEN 0 ELSE  p.MaquettePDF_ID END
	ELSE 0 
	END as maquette_id
	FROM  structure, commande_ligne cl 
	INNER JOIN commande_ligne_comp clp on clp.commande_ligne_id =cl.commande_ligne_id
	INNER JOIN dossier_produit d on d.dos_prod_id = cl.dossier_id
	INNER JOIN produit p on p.produit_id=d.produit_id
	LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LANGUAGEID
	WHERE cl.type_ligne='PRO'   and (p.groupe_id<>6)  and clp.etat<>'A'
	AND clp.commande_id=@COMMANDEID


DROP TABLE #T2
