﻿var arrSeatsFlagged = []

//chargement du pano ou plan
function initPanoOrPlan(session, isUniqueSession) {
    //urlPano = "/files/" + numToNDigitStr(structureId, 4) + "/Indiv/images/pano/pano" + placeId + ".html";
    if ($("#panoInner").html() == "") {
        $('#panoInner').attr('data-isuniquesession', isUniqueSession )
        var lstCategs = []
        $.each(thisSessionLoadedPlan.categories, function (i, k) {
            lstCategs.push(k.categId)
        })
        refreshChoiceByPlan(session, [0], [0], [0], lstCategs, isUniqueSession, false)
    } else {
        if (SettingsMerge.sessions.showSeePlan) {

            loadSeePlan(session.sessionId, '.SeePlanSallePano');
        }
        sendIframeSize();
    }
}

//load le plan (rodrigue ou photo)
function refreshChoiceByPlan(session, zoneIds, floorIds, sectionIds, lstCategs, isUniqueSession, haspano) {
    //console.log("refreshChoiceByPlan")
    //console.log(zoneIds + " " + floorIds + " " + sectionIds)
    var zoneIds = (zoneIds != undefined) ? zoneIds : [0];
    var floorIds = (floorIds != undefined) ? floorIds : [0];
    var sectionIds = (sectionIds != undefined) ? sectionIds : [0];

    /*if (SettingsMerge.plan.ShowTheWholePlan != undefined) {
        if ((session.place.placeId in SettingsMerge.plan.ShowTheWholePlan && SettingsMerge.plan.ShowTheWholePlan[session.place.placeId] == true) || (!(session.place.placeId in SettingsMerge.plan.ShowTheWholePlan) && SettingsMerge.plan.ShowTheWholePlan.default == true) ) {
             //si il y a une configuration pour ce lieu
            zoneIds = [0];
            floorIds = [0];
            sectionIds = [0];
        } 
    }*/

    var lstCategs = (lstCategs != undefined) ? lstCategs : [];
    var listGps = (listgpidPlan != undefined) ? listgpidPlan : [];
    var haspano = (haspano != undefined) ? haspano : true;
    var placeId = session.place.placeId;
    var sessionId = session.sessionId;
    
    var basketId = (Basket != null && Basket.basketId != null) ? Basket.basketId : 0;

    var datas = {
        webUserId: webUserId, //0 pour ne pas remonter les sièges deja dans le panier, a changer lorsqu'on voudra le faire
        categsId: lstCategs,
        listgpidPlan: listGps,
        token: partnerToken,
        zoneIds: zoneIds,
        floorIds: floorIds,
        sectionIds: sectionIds
    }
    /*if (multipleZESIds != undefined) {
        datas["multipleZESIds"] = JSON.stringify(multipleZESIds)
    }*/

    $.ajax({
        type: "POST",        
        url: sWOffersUrl + "Session/ChoiceSeatsPlanAjax/" + structureId + "/" + basketId + "/" + buyerProfilId + "/" + identityId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + placeId,
        data: datas,
        success: function (data) {
            loadingButtonBootstrapOff($('#calendarjsWrapper .days li'))

            getSeatingPlans(structureId, session.place.placeId)
            DrawPlanChoiceSeat(data, isUniqueSession, haspano)
            countAllPlacesSelectedPrePlan()
            writeEventLocationName(session)
            sendIframeSize();
            formatDevise()
        },
        error: function (a, b, c) {
            console.log("ChoiceSeatsPlanAjax -> Error")
        }
    });
}
//écrit le nom de la localisation du plan (zone, etage, section)
function writeEventLocationName(data) {
    var eventName = ""
    if (data.listZones != undefined) {
            if (data.listZones.zoneName != "Uni") {
                eventName = data.listZones.zoneName
            }
        if (data.listZones.listFloors != undefined ) {
            if (data.listZones.listFloors.floorName != "Uni") {
                eventName = data.listZones.listFloors.floorName
            }
            if (data.listZones.listFloors.listSections != undefined) {
                if (data.listZones.listFloors.listSections.sectionName != "Uni") {
                    eventName = data.listZones.listFloors.listSections.sectionName
                }
            }
        }
    }
   
    $('#eventMapLocation').html(eventName)

}
//dessine le plan
function DrawPlanChoiceSeat(data, isUniqueSession, haspano) {
    loadingBootstrapOff('#panoInner')
    if (data != '' && data != undefined) {
        $('#bigMapWrapper').html(data);
        initFixSVGDraw(data, isUniqueSession, haspano)
    }
}


function initFixSVGDraw(data, isUniqueSession, haspano) {
    var svgSize;
    try {
        svgSize = $('#svgPlanSalle')[0].getBBox(); // ici Firefox bug si hidden (cf https://jsfiddle.net/dekkard/b0t5mbea/)
    } catch (err) {
        svgSize = {
            height: 0,
            width: 0
        };

    }
    if (svgSize.height == 0 && svgSize.width == 0) {
        console.log("*********svg not drawed yet");
        setTimeout(function () { initFixSVGDraw(data, isUniqueSession, haspano) }, 100);
    } else {
        $('#bigMapWrapper #mapGrilleTarifWrapper [data-toggle="tooltip"]').tooltip({
            template: '<div class="tooltip tooltip-lg" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
        })

        if ($('#svgPlanSalle').children().length > 0) {
            //console.log(svgSize)
            var PlanMaxTop, PlanMaxLeft, PlanMaxRight;
            //var childrenCX = parseInt($('#svgPlanSalle').children().first().attr('cx'))
            //var childrenCY = parseInt($('#svgPlanSalle').children().first().attr('cy'))

            $.each($('#svgPlanSalle').children(), function (i, k) {
                if (PlanMaxTop == undefined || $(k).attr('data-posy') < PlanMaxTop.attr('data-posy')) {
                    PlanMaxTop = $(k);
                }

                if (PlanMaxLeft == undefined || $(k).attr('data-posx') < PlanMaxLeft.attr('data-posx')) {
                    PlanMaxLeft = $(k);
                }
            })

            $.each($('#svgPlanSalle .textlong'), function (i, k) {
                var color = "#" + $(k).attr('data-fontcolor');
                var fontsize = $(k).data('fontsize') * 1.35 + "px";
                $(k).css('fill', color);
                $(k).css('font-size', fontsize);
                if ($(k).data('fontbold') == 1) {
                    $(k).css('font-weight', 'bold');
                }
            })
            $('#svgPlanSalle').css('height', svgSize.height /*+ parseInt(PlanMaxTop.attr('data-posy')) + childrenCY*/).css('width', svgSize.width /*+ parseInt(PlanMaxLeft.attr('data-posx')) + childrenCX*/);

            if ($("#imgBackgroundSalle").length > 0 && $("#imgBackgroundSalle").attr('src') != "") {
                /*$('#divPlanSalleWrapperAll').css('width', ($("#imgBackgroundSalle").outerWidth()) + 'px');
                $('#eventMap, #divPlanSalleWrapperAll').css('height', ($("#imgBackgroundSalle").outerHeight()) + 'px');
                $('#divPlanSalleWrapperAll').attr('data-width', $('#divPlanSalleWrapperAll').width());
                $('#divPlanSalleWrapperAll').attr('data-height', $('#divPlanSalleWrapperAll').height());
                initScaleAndReplaceBigMap("#bigMapWrapper");*/
                //img loaded
                $('#imgBackgroundSalle').on('load', function () {
                    $('#svgPlanSalle').css('height', $("#imgBackgroundSalle").outerHeight()).css('width', $("#imgBackgroundSalle").outerWidth());
                    $('#divPlanSalleWrapperAll').css('width', ($("#imgBackgroundSalle").outerWidth()) + 'px');
                    //$('#eventMap,#eventMapInner, #divPlanSalleWrapperAll').css('height', ($("#imgBackgroundSalle").outerHeight()) + 'px');
                    $('#divPlanSalleWrapperAll').css('height', ($("#imgBackgroundSalle").outerHeight()) + 'px');
                    $('#divPlanSalleWrapperAll').attr('data-width', $('#divPlanSalleWrapperAll').width());
                    $('#divPlanSalleWrapperAll').attr('data-height', $('#divPlanSalleWrapperAll').height());
                    initScaleAndReplaceBigMap("#bigMapWrapper");
                    $('#divPlanSalleWrapper').css('opacity', 1);
                    DragBigMap()
                    initSeatsInteraction()
                    sendIframeSize()
                });
            } else {
                $('#svgPlanSalle').attr("viewBox", "0 0 " + svgSize.width + " " + svgSize.height)
                $('#divPlanSalleWrapperAll').css('width', (svgSize.width) + 'px');
                //$('#eventMap, #eventMapInner,#divPlanSalleWrapperAll').css('height', (svgSize.height) + 'px');
                $('#divPlanSalleWrapperAll').css('height', (svgSize.height) + 'px');
                $('#divPlanSalleWrapperAll').attr('data-width', $('#divPlanSalleWrapperAll').width());
                $('#divPlanSalleWrapperAll').attr('data-height', $('#divPlanSalleWrapperAll').height());
                initScaleAndReplaceBigMap("#bigMapWrapper");
                $('#divPlanSalleWrapper').css('opacity', 1);
                DragBigMap()
                initSeatsInteraction()
            }
           
        }
       
        sendIframeSize()

        

        if (haspano) {
            //si il y a un pano on affiche le lien de retour pano
            $('#bigMapWrapper').prepend('<div class="text-center mb-2"><a href="" id="backToPano" class="linked">' + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblBackToPano") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblBackToPano") : 'Retour au plan') + '</a></div>')
            $('#backToPano').off("click").click(function (e) {
                e.preventDefault()
                shouldUnflagSeats(function () {
                    if (panofov != undefined) {
                        krpano().call('zoomto(' + panofov + ',smooth())');
                    }
                    SwitchToIndex('#panoAndBigMapWrapper', '#panoWrapper', 300, function () {
                        sendIframeSize()
                    })
                })
            })
            SwitchToIndex('#panoAndBigMapWrapper', '#bigMapWrapper', 300)
        } else {
            //si il y a pas de pano on passe directement au plan
            SwitchToIndex('#panoAndBigMapWrapper', '#bigMapWrapper', 0)
        }

        //revenir au plan
        $('#TarifBackToPlan').off('click').on('click', function (e) {
            e.preventDefault();
            SwitchToIndex('#panoAndBigMapWrapper', '#bigMapWrapper', 300, function () {
                sendIframeSize()
            })
        })

        //valider la selection du plan
        $('#resumePreShoppingCartPLAN .validSelectionBigMap').off('click').on('click', function () {
            if (!$(this).hasClass('disabled')) {
                var sessionid = thisSessionLoadedPlan.session.sessionId
                var categids = []
                $.each(arrSeatsFlagged, function (i, k) {
                    if ($.inArray(categids, k.categid) == -1) {
                        categids.push(k.categid)
                    }
                })
                refreshGrilleTarif(sessionid, isUniqueSession, undefined, 'plan', categids)
                SwitchToIndex('#panoAndBigMapWrapper', '#mapGrilleTarifWrapper', 300)
            }
        })
    }
}


function initSeatsInteraction() {
    //lorsque l'utilisateur clique sur un siège sur le plan
    $('#svgPlanSalle .siegeall[data-free="True"]').off('click').on('click', function (e) {
        $('#resumePreShoppingCartPLAN .alertMaxSeatReached').find(".alertwrapperMaxSeatReached").remove()
        var seatid = $(this).data("seatid");
        var categId = $(this).data("categid");
        var sessionid = thisSessionLoadedPlan.session.sessionId
        if ($('#svgPlanSalle .siegeall[data-free="True"][data-seatid="' + seatid + '"]').hasClass('mine')) {
            unflagSeat([seatid], eventId, sessionid)
        } else {
            if ((arrSeatsFlagged.length + 1) <= thisSessionLoadedPlan.session.nbMaxOnSeance) {
                //si ne nombre max de place sur la séance n'est pas encore atteint, on flag
                flagSeat(seatid, eventId, sessionid, categId)
            } else {
                //sinon alert
                //console.log("nb max atteint : " + (arrSeatsFlagged.length + 1) + " / " + thisSessionLoadedPlan.session.nbMaxOnSeance)
                var templatealertMaxSeat = $('#hidden-template-alert_maxseatreached').html();
                $('#resumePreShoppingCartPLAN .alertMaxSeatReached').append(templatealertMaxSeat.replace("{SeatsNumber}", thisSessionLoadedPlan.session.nbMaxOnSeance))
            }

        }
        sendIframeSize()
    });
    //lorsque l'utilisateur survol un siège sur le plan
    $('#svgPlanSalle .siegeall[data-free="True"]').off('show.bs.tooltip')
    $('#svgPlanSalle .siegeall[data-free="True"]').tooltip({
        template: '<div class="tooltip tooltip-lg tooltip-light tooltip-nopadding tooltip-seatview" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',
        container: 'body',
        placement: 'top',
        boundary: 'viewport'

    })
    $('#svgPlanSalle .siegeall[data-free="True"]').on('show.bs.tooltip', function (e) {

        var hasImage = false;
        var template = $("#hidden-template-tooltip-viewfromseat").html()
        var iindex = $(e.target).attr('data-iindex')
        if (xmlVueFromSeat != undefined && $(xmlVueFromSeat).find('row[iindex="' + iindex + '"]') != undefined) {
            hasImage = true;
            template = template.replaceAll('[img]', '<div class="imgViewFromSeat"><img id="tooltip_img_' + iindex + '" style="display: none;" src="' + $(xmlVueFromSeat).find('row[iindex="' + iindex + '"]').attr("web_picture") + '" /></div>')
        } else {
            template = template.replaceAll('[img]', "")
        }
        template = template.replaceAll('[ranktxt]', ((GetTranslationTerm(TranslationsList, "Widget_Session_LblRank") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblRank") : 'Rang'))
        template = template.replaceAll('[rank]', $(e.target).attr('data-rank'))
        template = template.replaceAll('[seattxt]', ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSeat") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSeat") : 'Siège'))
        template = template.replaceAll('[seat]', $(e.target).attr('data-seat'))

        template = template.replaceAll('[denominationtxt]', ((GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination") != '') ? GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination") : ''))
        template = template.replaceAll('[denomination]', $(e.target).attr('data-denomination'))



        $(e.target).attr("data-original-title", template);
        $(e.target).tooltip('update')

    })
    $('#svgPlanSalle .siegeall[data-free="True"]').on('inserted.bs.tooltip', function (e) {
        var iindex = $(e.target).attr('data-iindex')
        $(e.target).tooltip('update')
        //$('#tooltip_img_' + iindex).hide()
        $('#tooltip_img_' + iindex).off('load').on('load', function () {
            //$('#tooltip_img_' + iindex).show()
            $(e.target).tooltip('update')
        });
    })
    $('#svgPlanSalle .siegeall[data-pmr="True"]').off('show.bs.tooltip')
    $('#svgPlanSalle .siegeall[data-pmr="True"]').tooltip({
        container: 'body',
        placement: 'top',
        boundary: 'viewport'

    })
    $('#svgPlanSalle .siegeall[data-pmr="True"]').on('show.bs.tooltip', function (e) {
        $(e.target).attr("data-original-title", ((GetTranslationTerm(TranslationsList, "Widget_Session_MsgSeatPersonReducedMobility") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_MsgSeatPersonReducedMobility") : 'Pour réserver cet emplacement PMR, merci de contacter la billetterie') );
        $(e.target).tooltip('update')

    })
    $('#svgPlanSalle .siegeall[data-pmr="True"]').on('inserted.bs.tooltip', function (e) {
        var iindex = $(e.target).attr('data-iindex')
        $(e.target).tooltip('update')
        //$('#tooltip_img_' + iindex).hide()
        $('#tooltip_img_' + iindex).off('load').on('load', function () {
            //$('#tooltip_img_' + iindex).show()
            $(e.target).tooltip('update')
        });
    })
}

//unflag des sieges via plan
function unflagSeat(seatIds, eventId, sessionId) {
    var url = widgetOfferUrl + structureId + "/unflagajax/" + (Basket.basketId || 0) + "/" + eventId + "/" + sessionId + "/" + webUserId + "/" + langCode;
    $.ajax({
        type: "POST",
        url: url,
        data: {
            //categsId: categIds,
            seatsId: seatIds,
            token: partnerToken
        },
        success: function (data) {
            $.each(seatIds, function (i, k) {
                $('#svgPlanSalle .siegeall[data-free="True"][data-seatid="' + k + '"]').removeClass('mine')
                var indexToRemove = arrSeatsFlagged.findIndex(x => x.seatid === k);
                if (indexToRemove > -1) {
                    arrSeatsFlagged.splice(indexToRemove, 1);
                }
            })
            countAllPlacesSelectedPrePlan()
        },
        error: function (a, b, c) {
            console.log("unflagajax -> Error")
        }
    });
}
//flag des sieges via plan
function flagSeat(seatid, eventId, sessionId, categId) {
    //{structureId}/flagseatsmanuallyajax/{basketId}/{eventId}/{sessionId}/{webUserId}/{categId}/{langCode}
    var url = widgetOfferUrl + structureId + "/flagseatsmanuallyajax/" + eventId + "/" + sessionId + "/" + webUserId + "/" + categId+"/" +langCode;
    $.ajax({
        type: "POST",
        url: url,
        data: {
            seatsId: [seatid],
            token: partnerToken
        },
        success: function (data) {
            var obj = { "seatid": seatid, "categid": categId, "eventid": eventId, "sessionid": sessionId }
            $('#svgPlanSalle .siegeall[data-free="True"][data-seatid="' + seatid + '"]').addClass('mine')
            arrSeatsFlagged.push(obj)
            countAllPlacesSelectedPrePlan()
            //$('#modal_ask_after_seatchoice').modal('show')
        },
        error: function (a, b, c) {
            console.log("flagseatsmanuallyajax -> Error")
        }
    });
}

/** Calcul toutes les places selectionné pour le PLAN **/
function countAllPlacesSelectedPrePlan() {
    var countTotalPlaces = 0
    $.each(arrSeatsFlagged, function (i, k) {
        var nbPlace = 1
        countTotalPlaces += nbPlace
    })
        
    var txtplace = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replace("{SeatsNumber}", countTotalPlaces)


    $("#resumePreShoppingCartPLAN .validSelectionBigMap").addClass("flip").delay(400).queue(function () {
        $(this).removeClass("flip").dequeue();
    });

   

    if (countTotalPlaces > 0) {
        setTimeout(function () {
            $('#resumePreShoppingCartPLAN .totalPlacesNumber').html(txtplace)
        }, 100);
        
        $('#modal_ask_after_seatchoice .totalPlacesNumber').html(countTotalPlaces)
        $("#resumePreShoppingCartPLAN .validSelectionBigMap").removeClass('disabled').attr("disabled", false);
    } else {
        setTimeout(function () {
            $('#resumePreShoppingCartPLAN .totalPlacesNumber').html(txtplace)
        }, 100);
        $('#modal_ask_after_seatchoice .totalPlacesNumber').html(countTotalPlaces)
        $("#resumePreShoppingCartPLAN .validSelectionBigMap").addClass('disabled').attr("disabled", true);
    }
}



//initialise le drag de la big map
function DragBigMap() {

    
    var gestureArea = document.getElementById('eventMapInner')
    var scaleElement = document.getElementById('divPlanSalleWrapperAll')
    var MovableElement = {
        x: scaleElement.getAttribute('data-x'),
        y: scaleElement.getAttribute('data-y'),
        scale: Number(scaleElement.getAttribute('data-scale'))
    }

    $('#eventMapInner').attr('data-scale', $('#divPlanSalleWrapperAll').attr('data-scale') )

   // console.log("MovableElement.scale " + MovableElement.scale)
    interact(gestureArea).unset()
    interact(gestureArea)
        .gesturable({
            listeners: {
                start(event) {

                },
                move(event) {
                    
                    var currentScale = event.scale * MovableElement.scale
                    if (currentScale < 0.2) {
                        currentScale = 0.2
                    }
                    if (currentScale > 3) {
                        currentScale = 3
                    }
                    //console.log("ds "+event.ds)
                    //console.log(event.scale)
                    //console.log("currentScale " + currentScale + " -> MovableElement.scale " + MovableElement.scale)

                    scaleElement.setAttribute('data-scale', currentScale)
                    //gestureArea.setAttribute('data-scale', currentScale)
                    // uses the dragMoveListener from the draggable demo above
                    dragMoveListener(event, scaleElement)
                },
                end(event) {
                    MovableElement.scale = MovableElement.scale * event.scale
                    
                    console.log("gesturable end")
                    dragMoveListenerEnd(event, scaleElement)
                    //console.log(MovableElement.scale)
                }
            }
        })
        .draggable({
            inertia: true,
            listeners: {
                move(event) {
                    dragMoveListener(event, scaleElement)
                },
                end(event) {
                    dragMoveListenerEnd(event, scaleElement)
                    console.log("draggable end")
                }
                
            },
            onend: function (event) {
                dragMoveListenerEnd(event, scaleElement)
                console.log("draggable end")
            }
        })

    window.dragMoveListener = dragMoveListener;

    ///////////MAP CONTROLS//////////////

    //zoom scroll - CAS-99524-N6V5F6
    $("#eventMapInner").off('mousewheel').on('mousewheel', function (e) {
        e.preventDefault();
        if (e.originalEvent.wheelDelta / 120 > 0) {
            zoomPlus(2)
        } else {
            zoomMinus(2)
        }
    });
    //up
    $("#upControl")
        .on('mousedown touchstart', function () {
            moveUp()
            console.log('MOVEUP')
            moveInterval = setInterval(moveUp, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(moveInterval);
            console.log('MOVEUP STOP')
            moveEnd()
        });


    //down
    $("#downControl")
        .on('mousedown touchstart', function () {
            moveDown()
            moveInterval = setInterval(moveDown, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(moveInterval);
            moveEnd()
        });



    //left
    $("#leftControl")
        .on('mousedown touchstart', function () {
            moveLeft()
            moveInterval = setInterval(moveLeft, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(moveInterval);
            moveEnd()
        });


    //right
    $("#rightControl")
        .on('mousedown touchstart', function () {
            moveRight()
            moveInterval = setInterval(moveRight, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(moveInterval);
            moveEnd()
        });

    //zoom plus 
    $("#zoomPlusControl")
        .on('mousedown touchstart', function (e) {
            zoomPlus()
            zoomInterval = setInterval(zoomPlus, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(zoomInterval);
            zoomEnd()
        });

    //zoom plus 
    $("#zoomMinusControl")
        .on('mousedown touchstart', function (e) {
            zoomMinus()
            zoomInterval = setInterval(zoomMinus, 10);
        })
        .on('mouseup mouseleave touchend', function () {
            clearInterval(zoomInterval);
            zoomEnd()
        });
}
function dragMoveListener(event, target) {
    var target = target
    // keep the dragged position in the data-x/data-y attributes
    var scale = (parseFloat(target.getAttribute('data-scale')) || 1);
    var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
    var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;
    // translate the element
    target.style.transform = 'translate(' + x + 'px, ' + y + 'px)  scale(' + scale + ')';
    // update the posiion attributes
    target.setAttribute('data-x', x)
    target.setAttribute('data-y', y)
    target.setAttribute('data-scale', scale)
   // event.target.setAttribute('data-scale', scale)
}
///////////// BIG MAP CONTROLS ///////////
/*function dragMoveListener(event, target) {
    //console.log("dragMoveListener")
    var target = $(target)[0],
        // keep the dragged position in the data-x/data-y attributes
        scale = (Number(target.getAttribute('data-scale')) || 1),
        x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx,
        y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

    // translate the element
    target.style.webkitTransform =
        target.style.transform =
        'translate(' + x + 'px, ' + y + 'px)  scale(' + scale + ')';

    // update the posiion attributes
    target.setAttribute('data-x', x);
    target.setAttribute('data-y', y);
    target.setAttribute('data-scale', scale);

}*/

function dragMoveListenerEnd(event, target) {
    //console.log("dragMoveListenerEnd")
    var target = $(target)[0],
        scale = (Number(target.getAttribute('data-scale')) || 1),
        targetWidth = target.offsetWidth * scale,
        targetHeight = target.offsetHeight * scale,
        parent = target.parentElement,
        parentWidth = parent.offsetWidth,
        parentHeight = parent.offsetHeight,
        updatedX = false,
        updatedY = false,
        x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx,
        y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;

    //positionement en largeur, selon si le plan est plus grand ou plus petit que son container
    if (targetWidth > parentWidth) {
        if (x > 0 - (target.offsetWidth - targetWidth) / 2) {
            updatedX = 0 - (target.offsetWidth - targetWidth) / 2 ;
        } else if (x < (targetWidth - target.offsetWidth) / 2 - (targetWidth - parentWidth)) {
            updatedX = (targetWidth - target.offsetWidth) / 2 - (targetWidth - parentWidth) ;
        } else {
            updatedX = x;
        }
    } else {
        if (x < 0 - (target.offsetWidth - targetWidth) / 2) {
            updatedX = 0 - (target.offsetWidth - targetWidth) / 2 ;
        } else if (x > (targetWidth - target.offsetWidth) / 2 - (targetWidth - parentWidth)) {
            updatedX = (targetWidth - target.offsetWidth) / 2 - (targetWidth - parentWidth) ;
        } else {
            updatedX = x;
        }
    }
    //positionement en hauteur, selon si le plan est plus grand ou plus petit que son container
    if (targetHeight > parentHeight) {
        if (y > 0 - (target.offsetHeight - targetHeight) / 2) {
            updatedY = 0 - (target.offsetHeight - targetHeight) / 2 
        } else if (y < (targetHeight - target.offsetHeight) / 2 - (targetHeight - parentHeight)) {
            updatedY = (targetHeight - target.offsetHeight) / 2 - (targetHeight - parentHeight) ;
        } else {
            updatedY = y;
        }
    } else {
        if (y < 0 - (target.offsetHeight - targetHeight) / 2) {
            updatedY = 0 - (target.offsetHeight - targetHeight) / 2 ;
        } else if (y > (targetHeight - target.offsetHeight) / 2 - (targetHeight - parentHeight)) {
            updatedY = (targetHeight - target.offsetHeight) / 2 - (targetHeight - parentHeight) ;
        } else {
            updatedY = y;
        }
    }
    // translate the element
    $({
        targetX: parseFloat(target.getAttribute('data-x')),
        targetY: parseFloat(target.getAttribute('data-y'))
    }).animate({
        targetX: updatedX,
        targetY: updatedY
    }, {
        duration: 150,
        step: function (now, fx) {
            //console.log(this.targetX)
            $(target).css('-webkit-transform', 'translate(' + this.targetX + 'px, ' + this.targetY + 'px) scale(' + scale + ')');
        }
    });

    // update the position attributes
    target.setAttribute('data-x', updatedX);
    target.setAttribute('data-y', updatedY);
    target.setAttribute('data-scale', scale);

    //fix svg text overlap glitch
    $('#svgPlanSalle').addClass('fixtextglitch')
    setTimeout(function () {
        $('#svgPlanSalle').removeClass('fixtextglitch');
    }, 100);
}
var moveInterval;
var zoomInterval;
var moveDecal = 4;
var scaleDecal = 0.04;
//move up
function moveUp() {
    interact(document.getElementById('eventMapInner')).fire({
        type: 'dragmove',
        target: $("#divPlanSalleWrapperAll")[0],
        dx: 0,
        dy: moveDecal
    });/*
    var target = $("#divPlanSalleWrapperAll")[0]
    var dataScale = Number(target.getAttribute('data-scale')) || 1
    var dataX = (parseFloat(target.getAttribute('data-x')) || 0);
    var dataY = (parseFloat(target.getAttribute('data-y')) || 0);

    $(target).css('-webkit-transform', 'translate(' + dataX + 'px, ' + (dataY + moveDecal) + 'px) scale(' + dataScale + ')');
    target.setAttribute('data-y', dataY + moveDecal);*/
}
//move down 
function moveDown() {
    interact(document.getElementById('eventMapInner')).fire({
        type: 'dragmove',
        target: $("#divPlanSalleWrapperAll")[0],
        dx: 0,
        dy: -moveDecal
    });
}
//move left
function moveLeft() {
    interact(document.getElementById('eventMapInner')).fire({
        type: 'dragmove',
        target: $("#divPlanSalleWrapperAll")[0],
        dx: moveDecal,
        dy: 0
    });
}
//move right
function moveRight() {
    interact(document.getElementById('eventMapInner')).fire({
        type: 'dragmove',
        target: $("#divPlanSalleWrapperAll")[0],
        dx: -moveDecal,
        dy: 0
    });
}
//move  end
function moveEnd() {
    //console.log("moveEnd")
    interact(document.getElementById('eventMapInner')).fire({
        type: 'dragend',
        target: $("#divPlanSalleWrapperAll")[0],
        dx: 0,
        dy: 0
    });
}
//zoom end
function zoomEnd() {
    interact(document.getElementById('eventMapInner')).fire({
        type: 'gestureend',
        target: $("#eventMapInner")[0],
        scale: 1,
        dx: 0,
        dy: 0
    });
}
// zoom plus
function zoomPlus(modifier) {
    modifier = (modifier != undefined) ? modifier : 1
   /* interact(document.getElementById('eventMapInner')).fire({
        type: 'gesturemove',
        target: $("#divPlanSalleWrapperAll")[0],
        scale: 1 + scaleDecal,
        dx: 0,
        dy: 0
    });*/
    var target = $("#divPlanSalleWrapperAll")[0]
    var dataScale = Number(target.getAttribute('data-scale')) || 1
    var dataX = (parseFloat(target.getAttribute('data-x')) || 0);
    var dataY = (parseFloat(target.getAttribute('data-y')) || 0);

    $({
        scale: dataScale
    }).animate({
        scale: Number(dataScale + (scaleDecal * modifier) )
    }, {
        duration: 9,
        step: function (now, fx) {
            if (now > 3) {
                now = 3
            }
            $(target).css('-webkit-transform', 'translate(' + dataX + 'px, ' + dataY + 'px) scale(' + now.toFixed(4) + ')');
            target.setAttribute('data-scale', now.toFixed(4));
        }
    });
}
//zoom moins
function zoomMinus(modifier) {
    modifier = (modifier != undefined) ? modifier : 1
    /*interact(document.getElementById('eventMapInner')).fire({
        type: 'gesturemove',
        target: $("#divPlanSalleWrapperAll")[0],
        scale: 1 - scaleDecal,
        dx: 0,
        dy: 0
    });*/
    var target = $("#divPlanSalleWrapperAll")[0]
    var dataScale = Number(target.getAttribute('data-scale')) || 1
    var dataX = (parseFloat(target.getAttribute('data-x')) || 0);
    var dataY = (parseFloat(target.getAttribute('data-y')) || 0);
    $({
        scale: dataScale
    }).animate({
        scale: Number(dataScale - (scaleDecal * modifier))
    }, {
        duration: 9,
        step: function (now, fx) {
            if (now < 0.2) {
                now = 0.2
            }
            $(target).css('-webkit-transform', 'translate(' + dataX + 'px, ' + dataY + 'px) scale(' + now.toFixed(4) + ')');
            target.setAttribute('data-scale', now.toFixed(4));
        }
    });
}

//lorsque l'utilisateur quitte le plan, on verifie s'il a une sélection avant de quitter
function shouldUnflagSeats(callback) {
    if (typeof arrSeatsFlagged !== 'undefined' && arrSeatsFlagged.length > 0) {
        $('#modalUnflagAllFromPlan').modal('show')

        $('#cancelUnflagMySeats').off('click').on('click', function () {
            $('#modalUnflagAllFromPlan').modal('hide')
        })

        $('#confirmUnflagMySeats').off('click').on('click', function () {
            var seatIds = [];
            $.each(arrSeatsFlagged, function (i, k) {
                seatIds.push(k.seatid)
            })
            /*var categIds = [];
            $.each(arrSeatsFlagged, function (i, k) {
                categIds.push(k.categid)
            })*/

            unflagSeat(seatIds, arrSeatsFlagged[0].eventid, arrSeatsFlagged[0].sessionid)
            $('#modalUnflagAllFromPlan').modal('hide')
            callback();
        })

    } else {
        callback();
    }

}
