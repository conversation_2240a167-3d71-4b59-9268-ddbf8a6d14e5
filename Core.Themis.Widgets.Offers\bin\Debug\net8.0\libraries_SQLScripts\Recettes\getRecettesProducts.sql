 /* 
 declare @peventid int = 23
 declare @pseance_id int = 23
 declare @pseatId int = 23
 declare @pdossierId int = 23
 */
 

--SELECT top 1 numbillet, externe , @psessionId as seance_id, produit_id , 1 as nbProduct, dos_prod_id
-- FROM recette_produit r  inner join dossier_produit d on d.dos_prod_id=r.produit_stock_id 
-- WHERE produit_id=@pproductId AND commande_id=@porderId and dos_prod_id = @pdossierId
-- AND type_operation='E'
 

-- AND (/* prod normal : dos_prod_numero =BasketLineId */ 
--	dos_prod_numero=@basketLineId	
--	OR /* carte adhesion : info comp dans dos_prod_numero */ 
--	dos_prod_numero in (
--	select id from identite_infos_comp iic 
--	inner join identite i on i.identite_id = iic.identite_id 
--	inner join commande c on i.identite_id = c.identite_id
--	where commande_id = @porderId
--	AND dos_prod_id not in ({listdosproddejatraites})
--	)
--	)


declare @seanceId int = @psessionId
declare @pNombre int = @pnbr
declare @OrderId int = @porderId
declare @ProductId int = @pproductId
declare @panierProduitId int = @pbasketLineId

if (@seanceId > 0)
begin
	SELECT top 1 numbillet, externe , dos_prod_numero, @seanceId as seance_id, produit_id ,@pNombre as nbProduct, dos_prod_id , recette_id 
	 FROM recette_produit r  inner join dossier_produit d on d.dos_prod_id=r.produit_stock_id 
	 WHERE produit_id=@ProductId AND commande_id=@OrderId
	 AND type_operation='E' and r.seance_id = @seanceId
	 AND
		(	dos_prod_numero=@panierProduitId /* prod normal : dos_prod_numero =BasketLineId */
		OR  (dos_prod_numero = 0 ) /* cmd faite dans Rodrigue : dos_prod_numero =0 */
		OR @panierProduitId = 0 /* @panierProduitId recu = 0 */
		OR   dos_prod_numero in (
			select id from identite_infos_comp iic  inner join identite i on i.identite_id = iic.identite_id  inner join commande c on i.identite_id = c.identite_id
			where commande_id = @OrderId			
			) /* carte adhesion : info comp dans dos_prod_numero */
		)
		AND dos_prod_id not in ({listdosproddejatraites})
		order by numbillet

end
else
begin
	SELECT top 1 numbillet, externe , dos_prod_numero, @seanceId as seance_id, produit_id ,@pNombre as nbProduct, dos_prod_id, recette_id 
	 FROM recette_produit r  inner join dossier_produit d on d.dos_prod_id=r.produit_stock_id 
	 WHERE produit_id=@ProductId AND commande_id=@OrderId
	 AND type_operation='E' 
	 AND
		(	dos_prod_numero=@panierProduitId /* prod normal : dos_prod_numero =BasketLineId */
		OR @panierProduitId = 0 /* @panierProduitId recu = 0 */
		OR  (dos_prod_numero = 0 ) /* cmd faite dans Rodrigue : dos_prod_numero =0 */
		OR   dos_prod_numero in (
			select id from identite_infos_comp iic  inner join identite i on i.identite_id = iic.identite_id  inner join commande c on i.identite_id = c.identite_id
			where commande_id = @OrderId
		) /* carte adhesion : info comp dans dos_prod_numero */
	)
	AND dos_prod_id not in ({listdosproddejatraites})
			order by numbillet
end