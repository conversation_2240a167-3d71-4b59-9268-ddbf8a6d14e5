/* big One : detail : zone, etage, section, dispo calculée sur les entree_xx */

/******** calcul d'histo par séance par rapport au panier  CAS-101847-C3Z1M6  */
 
/*
declare @peventid int = 60
declare @psessionid int = 123
declare @pidentityId int = 0
declare @pbuyerprofilId int = 0
declare @plangCode varchar(2) = 'fR'
declare @plisteZonesId varchar(max) = '' 
declare @plisteEtagesId varchar(max) = ''
declare @plisteSectionsId varchar(max) = ''
declare @plisteCategsId varchar(max) = ''
declare @pmyPanier varchar(max) = ''
*/
CREATE TABLE #mygp 
( 
    gestion_place_id INT,
    isforpa int
) 

DECLARE @gpAlreadyKnown INT
SELECT @gpAlreadyKnown = COUNT(*) FROM gestion_place gp WHERE gestion_place_id in ({gpAsked}) and isvalide =1

DECLARE @langue_Id int = 0
SELECT @langue_Id = langue_id FROM langue WHERE  upper(langue_code) = upper(@plangcode);
IF @langue_Id is null
	SET @langue_Id = 0

if (@gpAlreadyKnown > 0) -- gp demandées sont connues, on les obtient directement
BEGIN
    INSERT INTO #mygp
    SELECT gestion_place_id , isContrainteIdentite from gestion_place gp where gestion_place_id in ({gpAsked}) and isvalide =1
END
ELSE
BEGIN
    -- gp demandées vides => on les cherche	
DECLARE @isrevendeur int = 0
SELECT @isrevendeur = count(*) from profil_acheteur pa WHERE pa.id = @pbuyerprofilId AND pa.is_revendeur = 1	



DECLARE @addgeneralsrules int = 0
DECLARE @sqlColonne nvarchar(500)

SET @sqlColonne = 'SELECT @addgr = add_generals_rules FROM profil_acheteur pa WHERE pa.id = ' + convert(varchar(10),@pbuyerprofilId) + N' ' -- n = nbr de'offre sur lesquelles on n'ajoute pas l'offre g
BEGIN TRY 
    EXEC sp_executesql @sqlColonne, N'@addgr int out', @addgeneralsrules out
END TRY
BEGIN CATCH
    SET @addgeneralsrules = 1 -- on ajoute les regles de ventes tt public si la colonne n'existe pas
END CATCH


IF (@peventid = 0 AND @psessionid > 0)
	SELECT @peventid = manifestation_id FROM seance WHERE seance_id = @psessionid	


DECLARE @myBeneficiaire VARCHAR(6) 

	/******************************** commenter/decommenter la ligne ci dessous suivant ce que l'on	veut : */

      SET @myBeneficiaire ='PAYEUR' 
      -- le payeur de la commande (abo) est consideré comme le beneficiaire unique  => le nmax de le contrainte est multiplié % à l'histo DE CE QU'IL A PAYE
	  	  
      --set @myBeneficiaire ='CONSOM' 
	  -- le consommateur de la commande (abo) est consideré comme le beneficiaire unique  => le nmax de le contrainte est multiplié % à l'histo DES DOSSIERS QUI LUI SONT RELIÉS
      
	  
	  -- ************* renseigner gestion_place_multiplicateur_nbmax
	  -- tarif a multiplier | formule multiplicatrice | produit multiplicateur
	  
	  -- ************* renseigner gestion_place_correspondance_tarifs
      -- tarif de l'indiv | tarif de l'abonnement
	  
      SET nocount ON; 

      -- Insert statements for procedure here 
      CREATE TABLE #myoffres 
        ( 
           offre_id  INT, 
           offre_nom VARCHAR(50) 
        ) 



      EXEC [Sp_ws_getoffres_totable] 
        @pidentityId, 
        @pbuyerprofilId
		
      DECLARE @offrecount INT 

      SELECT @offrecount = Count(*) 
      FROM   #myoffres 

      DECLARE @myoffreid INT 

      SELECT TOP 1 @myoffreid = offre_id 
      FROM   #myoffres; 

      PRINT 'offreid=' + CONVERT(VARCHAR, @myoffreid); 

      IF ( @offrecount = 0 ) 
        BEGIN 
            PRINT 'pas d''Offres............' 

            IF (@isrevendeur =1)
            BEGIN
                print 'revendeur sans offre!'
            END
            ELSE
            BEGIN

                IF ( @peventId = 0 ) 
                  BEGIN 
                      INSERT INTO #mygp 
                      SELECT gp.gestion_place_id ,0
                      FROM   gestion_place gp 
                      WHERE  gp.isvalide = 1 
                             AND iscontrainteidentite = 0 
						      AND type_tarif_id IS NOT NULL
                  END 
                ELSE 
                  BEGIN 
                      INSERT INTO #mygp 
                      SELECT gp.gestion_place_id ,0
                      FROM   gestion_place gp 
                      WHERE  gp.isvalide = 1 
                             AND iscontrainteidentite = 0 
                             AND manif_id = @peventId 
                             AND type_tarif_id IS NOT NULL
                  END 
            END
        END 
     ELSE 
        BEGIN 
            PRINT 'Offres............' 

            DECLARE @listgp TABLE 
              ( 
                 manif_id         INT, 
                 gestion_place_id INT, 
                 isforpa          INT 
              ) 

            IF ( @peventId = 0 ) -- toutes les manifs 
              BEGIN 
                  --select offre_nom, o.offre_id, * from gestion_place gp, 
                  INSERT INTO @listgp 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         1 AS isForpa 
                  FROM   gestion_place gp, 
                         offre_gestion_place ogp, 
                         offre o, 
                         #myoffres myo 
                  WHERE  ogp.offre_id = o.offre_id 
                         AND o.offre_id = myo.offre_id 
                         AND ogp.gestion_place_id = gp.gestion_place_id 
                         AND gp.isvalide = 1 
        
                  UNION 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         0 AS isForpa 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 

                  INSERT INTO #mygp 
                  SELECT gptemp.gestion_place_id ,s.isForpa
                  FROM   (SELECT manif_id, 
                                 Max(isforpa) AS isForpa 
                          FROM   @listgp 
                          GROUP  BY manif_id) s 
                         INNER JOIN @listgp gptemp 
                                 ON gptemp.manif_id = s.manif_id 
                                    AND gptemp.isforpa = s.isforpa 
                  ORDER  BY s.manif_id 
              END 
            ELSE IF ( @psessionId = 0 ) -- toutes les seances d'une manif 
              BEGIN 
                  --select offre_nom, o.offre_id, * from gestion_place gp, 
                  INSERT INTO @listgp 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         1 AS isForpa 
                  FROM   gestion_place gp, 
                         offre_gestion_place ogp, 
                         offre o, 
                         #myoffres myo 
                  WHERE  ogp.offre_id = o.offre_id 
                         AND o.offre_id = myo.offre_id 
                         AND ogp.gestion_place_id = gp.gestion_place_id 
                         AND gp.isvalide = 1 
                         AND manif_id = @peventId 
                  UNION 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         0 AS isForpa 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 
                         AND manif_id = @peventId 

                  --select * from @listgp 
			        INSERT INTO #mygp 
                          SELECT gestion_place_id , isForpa
                          FROM   @listgp 
                          ORDER  BY manif_id 
              --select * from #mygp 
              END 
            ELSE 
              BEGIN -- une seance d'une manif 
                  --select offre_nom, o.offre_id, * from gestion_place gp, 
                  INSERT INTO @listgp 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         1 AS isForpa 
                  FROM   gestion_place gp, 
                         offre_gestion_place ogp, 
                         offre o, 
                         #myoffres myo 
                  WHERE  ogp.offre_id = o.offre_id 
                         AND o.offre_id = myo.offre_id 
                         AND ogp.gestion_place_id = gp.gestion_place_id 
                         AND gp.isvalide = 1 
                         AND manif_id = @peventId 
                         AND seance_id = @psessionId 
                  UNION 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         0 AS isForpa 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 
                         AND manif_id = @peventId 
                         AND seance_id = @psessionId 

			        INSERT INTO #mygp 
                          SELECT gestion_place_id , isForpa
                          FROM   @listgp 
                          ORDER  BY manif_id 
              END 
        END 
DROP TABLE #myoffres

IF @pbuyerprofilId = 0 AND @offrecount = 0  --pas profil acheter et pas d'offre ==*> cas general
	SET @addgeneralsrules = 1
	
-- offre sans profil acheteur
IF @pbuyerprofilId = 0 AND @offrecount > 0
BEGIN
	set @addgeneralsrules = 1 -- par défaut, on ajout l'offre générale
	IF EXISTS(SELECT 1 FROM sys.columns WHERE Name = N'add_generals_rules' AND Object_ID = Object_ID(N'dbo.offre'))
	BEGIN
	
		SET @sqlColonne = 'SELECT @addgr = add_generals_rules FROM offre o WHERE o.offre_id = ' + convert(varchar(10),@myoffreid) + N' ' -- n = nbr de'offre sur lesquelles on n'ajoute pas l'offre g
		
		BEGIN TRY 
			EXEC sp_executesql @sqlColonne, N'@addgr int out', @addgeneralsrules out
		END TRY
		BEGIN CATCH
			SET @addgeneralsrules = 1 -- en cas d'erreur, on ajout l'offre générale
		END CATCH
	
	END

END

IF (@isrevendeur = 1 or @addgeneralsrules=0)
BEGIN
	DELETE #mygp WHERE isforpa =0
END
ELSE
BEGIN

	IF (@peventId > 0)
	BEGIN

			INSERT INTO #mygp /* add les gestion places des offres d'adhesion mises en avant */
							SELECT gp.gestion_place_id ,0
							FROM offre o
							INNER JOIN offre_gestion_place ogp on ogp.offre_id = o.offre_id
							INNER JOIN gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
							INNER JOIN Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
							INNER JOIN Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID
							INNER JOIN Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1
                            LEFT JOIN offre_contrainte oc on oc.offre_id = o.offre_id
						WHERE isvalide =1 and manif_id = @peventId 	
                        AND oc.contrainte_id is null /* offres adh sans contraintes */
	
	END
	ELSE		
	BEGIN
	
		INSERT INTO #mygp /* add les gestion places des offres d'adhesion mises en avant */
				SELECT gp.gestion_place_id ,0
							FROM offre o
							INNER JOIN offre_gestion_place ogp on ogp.offre_id = o.offre_id
							INNER JOIN gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
							INNER JOIN Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
							INNER JOIN Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID
							INNER JOIN Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1
                            LEFT JOIN offre_contrainte oc on oc.offre_id = o.offre_id
						WHERE isvalide =1		
                        AND oc.contrainte_id is null /* offres adh sans contraintes */
					
	END
END

INSERT INTO #mygp
      SELECT DISTINCT gestion_place_parent_id, isContrainteIdentite FROM gestion_place
     WHERE gestion_place_id in (select gestion_place_id from #mygp)
 
CREATE TABLE #Gpres (Gestion_Place_id int ,reserve_id int)
INSERT INTO #GPres 
SELECT gpin.gestion_place_id, reserve_id  from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id
		 INNER JOIN gestion_place_reserve gpr on gpr.gestion_place_id=my.gestion_place_id
		 WHERE gpin.isvalide = 1		 
		 UNION		 
		 SELECT gpin.gestion_place_id, 0 as  reserve_id from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id		 
		 WHERE gpin.isvalide = 1 AND gpin.aucune_reserve=1 
CREATE CLUSTERED INDEX IX_Gestion_place_id ON #GPres (gestion_place_id)
CREATE INDEX IX_reserve_id ON #GPres (reserve_id)

CREATE TABLE #GpLight (Gestion_Place_id int ,reserve_id int,Manif_id int ,Seance_id int, categ_id int,Type_tarif_id int,Nb_Min int,Nb_Max int,Prise_Place int, Aucune_Reserve int)
INSERT INTO #GpLight 
SELECT gp.Gestion_place_id,reserve_id,manif_id ,Seance_id,Categ_id ,type_tarif_id,gp.nb_min,gp.nb_max,gp.prise_place,gp.Aucune_Reserve   from gestion_place gp inner join #Gpres gpr on gpr.Gestion_Place_id = gp.gestion_place_id
	  
      DECLARE @sqlreq VARCHAR(max) 
      DECLARE @const_placementlibre INT; 
      DECLARE @const_priseauto INT;
      DECLARE @const_vueplacement INT ;
      DECLARE @const_choixsurplan INT; 

      SET @const_placementlibre =32; 
      SET @const_priseauto =16; 
      SET @const_vueplacement =8; 
      SET @const_choixsurplan =1; 

      CREATE TABLE #tableresultgrilletarif 
        ( 
           event_Id         INT, 
           session_id       INT, 
           railingpriceid  INT, 
           amountexcepttax DECIMAL(18, 10), 
           charge          DECIMAL(18, 10), 
           tax             DECIMAL(18, 10), 
           discount        DECIMAL(18, 10), 
           commission      DECIMAL(18, 10), 
           totaltax        DECIMAL(18, 10), 
           totalamount     DECIMAL(18, 10), 
           ticketamount    DECIMAL(18, 10), 
           zoneid          INT, zonedispSequence int,
           zonename        VARCHAR(50), 
           sectionid       INT, sectiondispSequence int,
           sectionname     VARCHAR(50), 
           floorname       VARCHAR(50), 
           floorid         INT, floordispSequence int,
           categ_Id      INT, 
           categoryname    VARCHAR(50), 
           categorycode    VARCHAR(50), 
		   categorycolor    INT, 
		   categdispSequence int,
           priceid         INT, 
           pricename       VARCHAR(100), 
           nbseats          INT, 
           nbseatsmin       INT, 
           nbseatsmax       INT, 
		   nbhisto INT,
           bookingtype     INT, 
           notnumbered     INT, 
           priseauto   INT,
           surplan INT,
           voirplace INT,
           gpid            INT, 
           PriceDispSequence INT,
           zapper_ZES INT,
           zapper_ZE INT 
        ); 

      CREATE TABLE #tableresultsession 
        ( 
           gestion_place_id INT, 
           event_Id          INT, 
           session_id        INT, 
           nbseatmin        INT, 
           nbseatmax        INT 
        ); 

      /* multiplicateur : multiplie le nombre de places max grÃ¢ce aux formules et ou cartes de l'historique */
      CREATE TABLE #mymultiplicator 
        ( 
           type_tarif_id INT, 
           mult          INT 
        ) 

      /* insert le nombre de formules prises */ 
      DECLARE @peventIdHisto_encours INT 

      CREATE TABLE #myhisto 
        ( 
           manifid     INT, 
           seanceid    INT, 
           categid     INT, 
           typetarifid INT, 
           nbr         INT 
        )
        IF (@isrevendeur != 1) -- si revendeur, pas besoin de calculer l'histo, panier, etc
        BEGIN
        
          IF (@myBeneficiaire = 'CONSOM' ) -- ************* 
            BEGIN 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(DISTINCT abo_id) AS mult 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       commande_ligne_comp clc, 
                       commande_ligne cl 
                WHERE  cl.commande_ligne_id = clc.commande_ligne_id 
                       AND identite_id = @pidentityId 
                       AND mult.formule_id = cl.formule_id   AND etat IN ( 'R', 'P', 'B' )
                -- les commande_ligne abo à mon nom 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /* insert le nombre de cartes d'abo prises */ 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Max(nb_produit) 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       dossier_produit dp 
                WHERE  identite_id = @pidentityId 
                       AND dp.produit_id = mult.produit_id 
                -- les dossiers produit à mon nom 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /******************************************** calcul de l'histo */ 
                DECLARE eventcursorhisto CURSOR scroll FOR 
                  SELECT DISTINCT( cl.manifestation_id ) 
                  FROM   commande_ligne cl 
                         INNER JOIN commande_ligne_comp clc 
                                 ON clc.commande_ligne_id = cl.commande_ligne_id 
                  WHERE  identite_id = @pidentityId 
                         AND type_ligne = 'DOS' 
                         AND etat IN ( 'R', 'P', 'B' ) and cl.manifestation_id = @peventid

                OPEN eventcursorhisto 

                FETCH next FROM eventcursorhisto INTO @peventIdHisto_encours 

                WHILE @@FETCH_STATUS = 0 
                  BEGIN 
                      SET @sqlreq = 'INSERT INTO #myhisto SELECT ' + CONVERT(VARCHAR, @peventIdHisto_encours) + Char(13) 
                                    + ', e.seance_id, e.categorie_id, e.type_tarif_id, count(*) as nbr ' 
                          + Char(13) + ' from dossier_' + CONVERT(VARCHAR, @peventIdHisto_encours) + ' d '
					      + 'INNER JOIN entree_' + CONVERT(VARCHAR, @peventIdHisto_encours) + ' e on e.dossier_id=d.dossier_id ' 
                          + 'WHERE identite_id=' + CONVERT(VARCHAR, @pidentityId) 
                          + 'GROUP by e.seance_id,  e.categorie_id, e.type_tarif_id, e.seance_id'; 

			    PRINT @sqlreq 

			    EXEC (@sqlreq) 

			    FETCH next FROM eventcursorhisto INTO @peventIdHisto_encours 
                  END 

                CLOSE eventcursorhisto 

                DEALLOCATE eventcursorhisto 
            END 

          IF ( @myBeneficiaire = 'PAYEUR' ) -- ************* 
            BEGIN 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(DISTINCT abo_id) AS mult 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       commande_ligne_comp clc, 
                       commande_ligne cl 
                       INNER JOIN commande c  ON c.commande_id = cl.commande_id 
                WHERE  cl.commande_ligne_id = clc.commande_ligne_id 
                       AND abo_id <> 0 
                       AND c.identite_id = @pidentityId 
                       AND mult.formule_id = cl.formule_id  AND etat IN ( 'R', 'P', 'B' )
                -- les abos que j'ai acheté 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /* insert le nombre de cartes d'abo prises */ 
                INSERT #mymultiplicator 
                       (type_tarif_id, 
                        mult) 
                SELECT mult.type_tarif_id, 
                       Count(*) 
                FROM   [gestion_place_multiplicateur_nbmax] mult, 
                       dossier_produit dp 
                       INNER JOIN commande c 
                               ON c.commande_id = dp.commande_id 
                WHERE  c.identite_id = @pidentityId 
                       AND dp.produit_id = mult.produit_id 
                -- les cartes que j'ai acheté 
                GROUP  BY mult.type_tarif_id, 
                          mult.produit_id, 
                          mult.formule_id 

                /******************************************** calcul de l'histo */ 
                DECLARE eventcursorhisto CURSOR SCROLL FOR 
                  SELECT DISTINCT( cl.manifestation_id ) 
                  FROM   commande_ligne cl 
                         INNER JOIN commande_ligne_comp clc 
                                 ON clc.commande_ligne_id = cl.commande_ligne_id 
                  WHERE  identite_id = @pidentityId 
                         AND type_ligne = 'DOS' 
                         AND etat IN ( 'R', 'P', 'B' ) and cl.manifestation_id = @peventid

                OPEN eventcursorhisto 

                FETCH NEXT FROM eventcursorhisto INTO @peventIdHisto_encours 

                WHILE @@FETCH_STATUS = 0 
                  BEGIN 
                      SET @sqlreq = 'INSERT INTO #myhisto SELECT ' + CONVERT(VARCHAR, @peventIdHisto_encours) + Char(13) 
                                    + ', e.seance_id,  e.categorie_id, e.type_tarif_id,  count(*) as nbr '  + Char(13) 
								    + ' from dossier_'  + CONVERT(VARCHAR, @peventIdHisto_encours) + ' d ' 
					      + 'INNER JOIN entree_' + CONVERT(VARCHAR, @peventIdHisto_encours) + ' e on e.dossier_id=d.dossier_id ' 
                          + 'WHERE identite_id=' + CONVERT(VARCHAR, @pidentityId) 
                          + 'GROUP by e.seance_id,  e.categorie_id, e.type_tarif_id, e.seance_id'; 

			    PRINT @sqlreq 

			    EXEC (@sqlreq) 

			    FETCH next FROM eventcursorhisto INTO @peventIdHisto_encours 
                  END 

                CLOSE eventcursorhisto 

                DEALLOCATE eventcursorhisto 
            END 

          -- select * from #myhisto 

          IF (@pmyPanier <> '' ) 
            BEGIN 
                DECLARE @gpInBasket TABLE (gpid INT ) 
                DECLARE @Counter INT 

                SET @Counter = 1 

                WHILE ( Charindex(',', @pmyPanier) > 0 ) 
                  BEGIN 
                      INSERT INTO @gpInBasket (gpid) 
                      SELECT gpId = Ltrim(Rtrim(Substring(@pmyPanier, 1, 
                                                Charindex(',', @pmyPanier)-1)  )) 

                      SET @pmyPanier = Substring(@pmyPanier, Charindex(',', @pmyPanier) + 1, Len( @pmyPanier)) 
                      SET @Counter = @Counter + 1 
                  END 

                INSERT INTO @gpInBasket 
                            (gpid) 
                SELECT gpId = Ltrim(Rtrim(@pmyPanier)) 
            END 

          INSERT INTO #myhisto 
          SELECT manif_id, 
                 seance_id, 
                 categ_id, 
                 type_tarif_id, 
                 1 
          FROM   gestion_place gp 
                 INNER JOIN @gpInBasket bask 
                         ON bask.gpid = gp.gestion_place_id 

              --select * from #myhisto

          UPDATE #myhisto 
          SET    typetarifid = (SELECT  type_tarif_id_maitre 
                                FROM   gestion_place_correspondance_tarifs 
                                WHERE  #myhisto.typetarifid = type_tarif_id_eleve
                  and type_tarif_id_maitre in (select type_tarif_id from @listgp gp inner join gestion_place gp2 on gp2.gestion_place_id = gp.gestion_place_id)
              
                  ) WHERE typetarifid in (select type_tarif_id_eleve FROM gestion_place_correspondance_tarifs)

           /******************************************** FIN calcul de l'histo */ 
      END 
      END
      --select * from #mygp 
      DECLARE eventcursor CURSOR scroll FOR 
        SELECT DISTINCT ( manif_id ) , seance_id
        FROM   #mygp, 
               gestion_place gp 
        WHERE  gp.gestion_place_id = #mygp.gestion_place_id 

      SET @sqlreq =''; 

      DECLARE @strTraduction VARCHAR(50) 

      SET @strTraduction=''; 

      IF ( @langue_id != 0 ) 
      BEGIN 
         SET @strTraduction='traduction_'; 
      END 

      DECLARE @peventId_encours INT 
        DECLARE @psessionId_encours INT 

      OPEN eventcursor 

      FETCH next FROM eventcursor INTO @peventId_encours, @psessionId_encours

    WHILE @@FETCH_STATUS = 0 


        BEGIN 
            PRINT @peventId_encours 
			print @psessionId_encours

            DECLARE @zapperEtage INT 

            SELECT @zapperEtage = Count(*) 
            FROM   proprietes_of_manifs pm, 
                   proprietes_references_of_manifs pref 
            WHERE  pref.propriete_ref_id = pm.propriete_ref_id 
                   AND valeur = '1' 
                   AND pm.manifestation_id = @peventId_encours 
                   AND pref.code = 'ZapperEtag' 

            DECLARE @zapperZEC INT 

            SELECT @zapperZEC = Count(*) 
            FROM   proprietes_of_manifs pm, 
                   proprietes_references_of_manifs pref 
            WHERE  pref.propriete_ref_id = pm.propriete_ref_id 
                   AND valeur = '1 '
                   AND pm.manifestation_id = @peventId_encours 
                   AND pref.code = 'ZapperZES' 

            PRINT 'zapperZEC=' + CONVERT(VARCHAR, @zapperZEC) 

            PRINT 'zapperEtage=' + CONVERT(VARCHAR, @zapperEtage) 

            SET @sqlreq = 'INSERT INTO #tableResultGrilleTarif SELECT ' + Char(13) + 
		    'event_Id, session_id, RailingPriceID, AmountExceptTax, Charge, Tax, discount, Commission, TotalTax, TotalAmount, TicketAmount, ZoneId, zonedispsequence,ZoneName, SectionId, sectiondispSequence, SectionName, FloorName,
		    floorId, floordispSequence,  categ_Id, CategoryName, CategoryCode, categColor, CategdispSequence, PriceID, PriceName,   sum(nbSeats) as nbSeats, NbSeatMin, NbSeatMax, NbHisto, BookingType,
		    NotNumbered,priseauto,surplan,voirplace,GpId,PriceDispSequence, '+CONVERT(VARCHAR, @zapperZEC) +' as zapper_ZES ,' 
				+ CONVERT(VARCHAR, @zapperEtage) +' as zapper_ze ' 
              + Char(13) + 'FROM (SELECT ' 
              + 'vgp.manif_id as event_Id' 
              + ',vgp.seance_id as session_id' + Char(13) 
              + ',vts.vts_id as RailingPriceID' + Char(13) 
              + ',vts.vts_grille1 as AmountExceptTax' 
              + Char(13) + ',vts.vts_grille2 as Charge ' 
              + Char(13) 
              + 	',Tax= case when modecol4=''TAXE'' then vts.vts_grille4 else 0 END + case  when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''TAXE'' then vts.vts_grille10 else 0 END'
              + Char(13) 
              + ',Discount= case when modecol4=''REMISE'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then vts.vts_grille10 else 0 END'
              + Char(13) 
              + ',Commission= case when modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END'
              + Char(13) 
              + ',TotalTax= case  when modecol4=''REMISE''  then - vts.vts_grille4 when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  vts.vts_grille10 else 0 END '
              + Char(13) 
              + ',TotalAmount= vts.vts_grille1+vts.vts_grille2+case when modecol4=''REMISE'' then - vts.vts_grille4  when modecol4=''TAXE''  then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' then vts.vts_grille10 else 0 END  '
              + Char(13) + ',TicketAmount=vts.vts_grille3' 
              + Char(13) 

    IF ( @zapperZEC = 0 ) /* ZEC = non */
	BEGIN 
			SET @sqlreq = @sqlreq + ',vgp.zone_id as ZoneID, z.pref_affichage as zonedispSequence' 
                        + ',isnull(ztrad.zone_nom, z.zone_nom) as ZoneName, vgp.section_id as Sectionid, sc.pref_affichage as sectiondispSequence, isnull(sctrad.section_nom, sc.section_nom) as SectionName' + Char(13); 

			IF ( @zapperEtage = 0 ) 	/* Zapper Etage = non */
			BEGIN 
				SET @sqlreq = @sqlreq 
                        + ',isnull(ettrad.etage_nom, et.etage_nom) as FloorName, et.etage_id as FloorId, et.pref_affichage as floordispSequence ' 
                        + Char(13); 
			END 
			ELSE 
			BEGIN 	/* Zapper Etage = oui */
				  SET @sqlreq = @sqlreq 
								+ ',''Uni'' as FloorName, 0 as FloorId, 0 as floordispSequence ' 
								+ Char(13); 			  
			END 
	END 
	ELSE 
	BEGIN /* ZEC = oui */
		SET @sqlreq = @sqlreq 
			+ ',0 as ZoneID, 0 as zonedispSequence, ''Uni'' as ZoneName, 0 as Sectionid, 0 as sectiondispSequence, ''Uni'' as SectionName,''Uni'' as FloorName,0 as FloorId, 0 as floordispSequence' + Char(13); 	
	END 

	SET @sqlreq = @sqlreq + ',vts.categ_id as categ_Id,' 
				  + Char(13) + 'isnull(ctrad.[categ_nom], c.categ_nom)  as CategoryName	'				  
                  + Char(13) + ',c.categ_code as CategoryCode, c.categ_couleur_id as categColor, c.pref_affichage as categdispSequence' 
                  + Char(13) + ',vts.type_tarif_id as PriceID' 
                  + Char(13) + ',isnull(tttrad.type_tarif_nom, tt.type_tarif_nom) as PriceName' 
				  
                  + Char(13) + ',NbSeats as NbSeats ' 
                  + ',vgp.nb_min as NbSeatMin,' + Char(13); 
    SET @sqlreq = @sqlreq + Char(9) 
                  + ' vgp.nb_max  * isnull((select sum(mult) from #mymultiplicator mym where mym.type_tarif_id = tt.type_tarif_id),1) as NbSeatMax' + Char(13); 
	
    /********** nbr de places deja  prises */     
    SET @sqlreq = @sqlreq + Char(9) + ',isnull(' 
                  + '(select sum(nbr) FROM #myhisto h WHERE h.typetarifid =vts.type_tarif_id and h.manifid=' 
              + CONVERT(VARCHAR, @peventId_encours) + ' and h.seanceid = ' + CONVERT(VARCHAR, @psessionId_encours)
              + '),0) as NbHisto' + Char(13); 
    SET @sqlreq = @sqlreq 
                  + ', BookingType=CASE WHEN vgp.prise_place is null THEN 0 ELSE vgp.prise_place END ' 
              + ',NotNumbered = CASE WHEN (vgp.prise_place & ' 
              + CONVERT(VARCHAR, @const_placementlibre) 
              + ')=0 THEN 0 ELSE 1 END ' 
			  
               + ',priseauto = CASE WHEN (vgp.prise_place & ' 
              + CONVERT(VARCHAR, @const_priseauto) 
              + ')=0 THEN 0 ELSE 1 END ' 

               + ',voirplace = CASE WHEN (vgp.prise_place & ' 
              + CONVERT(VARCHAR, @const_vueplacement) 
              + ')=0 THEN 0 ELSE 1 END ' 

               + ',surplan = CASE WHEN (vgp.prise_place & ' 
              + CONVERT(VARCHAR, @const_choixsurplan) 
              + ')=0 THEN 0 ELSE 1 END ' 

              + ',vgp.gestion_place_id as GpId ' 
              + ',tt.pref_affichage as PriceDispSequence  ' 
              + Char(13) + 'FROM valeur_tarif_stock' 
              + CONVERT(VARCHAR, @peventId_encours) 
              + ' vts ' + 'INNER JOIN (' + Char(13) 
              + ' SELECT gp.manif_id,gp.gestion_place_id, gp.seance_id, gp.type_tarif_id, gp.categ_id ,e.reserve_id,gp.nb_min,gp.nb_max,gp.prise_place '
              + Char(13) 
                 + ' ,rlp.zone_id,rlp.section_id,rlp.etage_id,isnull(sum(case when entree_etat = ''L'' then 
                  case when gp.aucune_reserve =1 then 
                  case when e.reserve_id > 0 then 
                  case when gpreserve.reserve_id > 0 then 1 else 0 end
                            else 1 end                  
              when entree_etat <> ''L'' then 0 
              else 1 end
              end               
                ),0)  as NbSeats'
              + Char(13) + 'FROM entree_' 
              + CONVERT(VARCHAR, @peventId_encours) + ' e ' 
              + Char(13) 
              + ' INNER JOIN #GpLight gp  ON gp.seance_id=e.seance_id and gp.categ_id = e.categorie_id and gp.reserve_id = e.reserve_id' 
              + Char(13) + 'INNER JOIN valeur_tarif_stock' 
              + CONVERT(VARCHAR, @peventId_encours) 
              + ' vts ON vts.type_tarif_id = gp.type_tarif_id AND vts.categ_id = gp.categ_id AND vts.seance_id=e.seance_id'
              + Char(13) 
              + 'INNER JOIN #mygp ON #mygp.gestion_place_id = gp.gestion_place_id ' 
              + Char(13) 
				+' INNER JOIN #Gpres gpreserve ON gpreserve.gestion_place_id=gp.gestion_place_id AND e.reserve_id=gpreserve.reserve_id '
              + 'INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id  '
              + Char(9) + 'INNER JOIN ' + @strTraduction 
              + 'categorie c on c.categ_id = vts.categ_id ' 
              + Char(9) + 'INNER JOIN ' + @strTraduction 
              + 'zone z on z.zone_id = rlp.zone_id ' 
              + Char(13) + Char(9) + 'INNER JOIN ' 
              + @strTraduction 
              + 'section sc on sc.section_id = rlp.section_id ' 
              + Char(13) + Char(9) + 'INNER JOIN ' 
              + @strTraduction 
              + 'etage et on et.etage_id = rlp.etage_id ' 
              + Char(13) + Char(9) 
              + 'WHERE e.entree_etat in (''L'',''P'',''B'',''R'') AND (flag_selection='''' OR flag_selection IS NULL) AND alotissement_id=0 AND contingent_id=0 '
              + Char(13) 

    IF ( @langue_id != 0 ) 
      BEGIN 
          SET @sqlreq = @sqlreq + ' AND et.langue_id=' + CONVERT(VARCHAR, @langue_id); 
          SET @sqlreq = @sqlreq + ' AND z.langue_id='  + CONVERT(VARCHAR, @langue_id); 
          SET @sqlreq = @sqlreq + ' AND sc.langue_id=' + CONVERT(VARCHAR, @langue_id); 
          SET @sqlreq = @sqlreq + ' AND c.langue_id='  + CONVERT(VARCHAR, @langue_id); 
      END 

    IF ( @plisteZonesId <> 0 ) 
      SET @sqlreq = @sqlreq + ' AND z.zone_id in ('  + @plisteZonesId + ') '; 

    IF ( @plisteEtagesId <> 0 ) 
      SET @sqlreq = @sqlreq + ' AND et.etage_id in (' + @plisteEtagesId + ') '; 

    IF ( @plisteSectionsId <> 0 ) 
      SET @sqlreq = @sqlreq + ' AND sc.section_id in (' + @plisteSectionsId + ') '; 

    IF ( @plisteCategsId <> 0 ) 
      SET @sqlreq = @sqlreq + ' AND c.categ_id in (' + @plisteCategsId + ') '; 

    SET @sqlreq = @sqlreq + Char(9) 
                  + 'AND vts_v =(SELECT MAX(vts_v) FROM valeur_tarif_stock' 
                  + CONVERT(VARCHAR, @peventId_encours) 
                  + ' vts2' + Char(13) 
    SET @sqlreq = @sqlreq + Char(9) 
                  + 'WHERE vts2.tarif_logique_id=vts.tarif_logique_id' 
                  + Char(13) 
    SET @sqlreq = @sqlreq + Char(9) 
                  + 'and vts2.seance_id=vts.seance_id' 
                  + Char(13) 
    SET @sqlreq = @sqlreq + Char(9) 
                  + 'and vts2.categ_id= vts.categ_id' 
                  + Char(13) 
    SET @sqlreq = @sqlreq + Char(9) 
                  + 
    'and vts2.type_tarif_id= vts.type_tarif_id) and vts.vts_grille1>=0' 
                  + Char(13) 
    SET @sqlreq = @sqlreq + Char(13) + Char(9)                  
				+ 'GROUP BY gp.manif_id,gp.gestion_place_id,gp.seance_id,gp.type_tarif_id,gp.categ_id,gp.nb_min,gp.nb_max,gp.prise_place,e.reserve_id '
              + ' ,rlp.zone_id,rlp.section_id,rlp.etage_id' 
              + Char(13) + ') vgp' + Char(13)  + 
				' ON vts.type_tarif_id =  vgp.type_tarif_id and vts.categ_id = vgp.categ_id AND vts.seance_id = vgp.seance_id  '
              + 'INNER JOIN structure st ON st.structure_id >0' 
              + Char(13) + 'INNER JOIN categorie c ON c.categ_id=vts.categ_id' 
			  + Char(13) + 'LEFT OUTER JOIN traduction_categorie ctrad ON ctrad.categ_id=c.categ_id and ctrad.langue_id=' +  CONVERT(VARCHAR, @langue_id)
			  
              + Char(13) + 'INNER JOIN type_tarif tt ON tt.type_tarif_id=vts.type_tarif_id' 
			  + Char(13) + 'LEFT OUTER JOIN traduction_type_tarif tttrad ON tttrad.type_tarif_id=tt.type_tarif_id and tttrad.langue_id='+ CONVERT(VARCHAR, @langue_id)
			  
              + Char(13) + 'INNER JOIN zone z on z.zone_id = vgp.zone_id' 
			  + Char(13) + 'LEFT OUTER JOIN traduction_zone ztrad on ztrad.zone_id = z.zone_id and ztrad.langue_Id=' + CONVERT(VARCHAR, @langue_id)
			  
              + Char(13) + 'INNER JOIN section sc on sc.section_id = vgp.section_id' 
			  + Char(13) + 'LEFT OUTER JOIN traduction_section sctrad on sctrad.section_id = sc.section_id and sctrad.langue_Id='  + CONVERT(VARCHAR, @langue_id)
			  
              + Char(13); 

    IF ( @zapperEtage = 0 ) 
      BEGIN 
          SET @sqlreq = @sqlreq + 'INNER JOIN etage et on et.etage_id = vgp.etage_id ' 
		  SET @sqlreq = @sqlreq + 'LEFT OUTER JOIN traduction_etage ettrad on ettrad.etage_id = et.etage_id and ettrad.langue_Id='  + CONVERT(VARCHAR, @langue_id)
		  
                        + Char(13); 
      END 
    ELSE 
      BEGIN 
          PRINT 'zapperetage' 
      --SET @sqlreq = @sqlreq + 'INNER JOIN ' + @strTraduction + 'etage et on et.etage_id = vgp.etage_id ' +  char(13);
      END 

    SET @sqlreq = @sqlreq 
                  + 'WHERE vts_v =(SELECT MAX(vts_v) FROM valeur_tarif_stock' 
                  + CONVERT(VARCHAR, @peventId_encours) 
                  + ' vts2' + Char(13) + Char(9) 
                  + 'WHERE vts2.tarif_logique_id=vts.tarif_logique_id' 
                  + Char(13) + Char(9) 
                  + 'and vts2.seance_id=vts.seance_id' 
                  + Char(13) + Char(9) 
                  + 'and vts2.categ_id= vts.categ_id' 
                  + Char(13) + Char(9) 
                  + 'and vts2.type_tarif_id= vts.type_tarif_id)' 
                  + ' AND vts.vts_grille1>=0 ' + Char(13) 

    SET @sqlreq = @sqlreq + ') ssr WHERE 1=1 ' + Char(13); 


	          SET @sqlreq = @sqlreq + ' AND session_id=' 
                        + CONVERT(VARCHAR, @psessionId_encours) + Char(13) 

    IF ( @psessionId > 0 ) 
    BEGIN 
          SET @sqlreq = @sqlreq + ' AND session_id=' 
                        + CONVERT(VARCHAR, @psessionId) + Char(13) 
    END 

    SET @sqlreq = @sqlreq + 
				' GROUP BY event_Id, session_id, RailingPriceID, AmountExceptTax, Charge, Tax, discount, Commission, TotalTax, 
				TotalAmount, TicketAmount, ZoneId, zonedispSequence, ZoneName, SectionId, sectiondispSequence, SectionName, floorId, 
				FloorName, floordispSequence, categ_Id, CategoryName, CategoryCode, CategColor, CategdispSequence, PriceID, PriceName, PriceDispSequence, 
				nbSeatMin, nbSeatMax, nbhisto, bookingType, NotNumbered, priseauto, surplan, voirplace, GpId, PriceDispSequence'; 
				
    SET @sqlreq = @sqlreq 
                  + ' ORDER BY CategoryName,PriceDispSequence;' 
                  + Char(13) + Char(13) 
    SET @sqlreq = @sqlreq 
                  + '/* regle generale sur la seance : *********** */' 
                  + Char(13) 
    /* regle generale sur la seance : */ 
    /* regle generale sur la seance : */ 
    declare @sqlReqGen varchar(max) ='INSERT INTO #tableResultSession SELECT gp.gestion_place_id,gp.manif_id as event_Id, gp.seance_id as session_id, gp.nb_min as NbSeatMin, '
			+ '(SELECT MIN(Col) FROM (VALUES (gp.nb_max '
				+ '- 	isnull((select sum(nbr) from #myhisto where manifid=' + CONVERT(VARCHAR, @peventId_encours) + ' and seanceid=' + CONVERT(VARCHAR, @psessionid_encours) + ' ),  0)' 
              /* + Char(13) + '- isnull((select ' + Char(13) 
              + 'COUNT(*) as nbr from dossier_' 
              + CONVERT(VARCHAR, @peventId_encours) + ' d' 
              + Char(13) + ' inner join entreesvg_' 
              + CONVERT(VARCHAR, @peventId_encours) 
              + ' e on e.dossier_id=d.dossier_id' 
              + Char(13) + ' WHERE identite_id=' 
              + CONVERT(VARCHAR, @pidentityId) + Char(13) 
              + ' and e.dossier_v = (select MAX(dossier_v) from dossier_' 
              + CONVERT(VARCHAR, @peventId_encours) 
              + ' d2 where d2.dossier_id=d.dossier_id and d2.identite_id=d.identite_id)' 
              + Char(13) 
              + 'and dossier_etat in (''B'',''P'',''R'')' 
              + Char(13) + 'and e.seance_id = gp.seance_id' 
              + Char(13) + 'group by e.seance_id' + Char(13) 
              + '),0)' */
			  + '), (gp.dispo)) AS X(Col)) AS NbSeatMax'+ CHAR(13) +
			 			

			 			
              + ' FROM gestion_place gp ' + Char(13) 
              + ' INNER JOIN #mygp ON #mygp.gestion_place_id =gp.gestion_place_id ' 
              + Char(13) + ' WHERE manif_id=' 
              + CONVERT(VARCHAR, @peventId_encours) + ' and seance_id=' + CONVERT(VARCHAR, @psessionid_encours)
              + ' AND type_tarif_id is null AND categ_id is null AND isvalide=1 ' ; 

		IF ( @psessionId > 0 ) 
		BEGIN 
          SET @sqlReqGen = @sqlReqGen + ' and seance_id=' + CONVERT(VARCHAR, @psessionId); 
		END 

		declare @sqlreqTotal varchar(max) = @sqlreq + @sqlReqGen

   		print '@sqlreq'+ @sqlreq
		print @sqlReqGen

    EXEC (@sqlreqTotal) 

     FETCH next FROM eventcursor INTO @peventId_encours , @psessionId_encours
END 

CLOSE eventcursor 
DEALLOCATE eventcursor 

DROP TABLE #mymultiplicator 


SELECT DISTINCT event_Id, 
					m.manifestation_nom as event_name,
					m.manifestation_code as event_code,	
					l.lieu_id as place_id, l.lieu_nom as place_name,								
                    session_id, 
					sea.seance_date_deb as date_start, 
					sea.seance_date_fin as date_end,
                    railingpriceid, 
                    convert(int, 100 * amountexcepttax) as amountexcepttax , 
                    convert(int, 100 * charge) as charge, 
                    convert(int, 100 * tax) as tax, 
                    convert(int, 100 * discount) as discount, 
                    convert(int, 100 * commission) as commission, 
                    convert(int, 100 * totaltax) as totaltax, 
                    convert(int, 100 * totalamount) as totalamount, 
                    convert(int, 100 * ticketamount) as ticketamount, 
                    zoneid, 
                    zonename, 
					zonedispSequence,
                    sectionid, 
                    sectionname, 
					sectiondispSequence,
                    floorname, 
                    floorid, 
					floordispSequence,
                    gt.categ_Id, 
                    rtrim(ltrim(categoryname)) as categoryname, 
                    categorycode, 
					categorycolor,
					categdispSequence,
                    priceid, 
                    rtrim(ltrim(pricename)) as pricename, 
                    nbseats, 
                    nbseatsmin, 
                    case when adh.Adhesion_Catalog_ID is null then nbseatsmax - nbhisto else nbseatsmax end as nbseatsmax, 
                    bookingtype, 
                    notnumbered,
                    priseauto,
                    surplan,
                    voirplace,
					case when nepasafficherdate like 'o' then 0 else 1 end as isshowsessiondate,
					case when nepasafficherdate like 'h' then 0 when nepasafficherdate like 'o' then 0 else 1 end as isshowsessionhour,
                    Max(gpid) AS GpId,
					gp.gestion_place_parent_id as GpParentId,
					o.offre_id as offerId,  o.offre_nom as offerName,
					adh.Adhesion_Catalog_ID as adhCatalogId,
					adh.Catalog_Libelle as adhCatalogName,
					adh.Mise_En_Avant as miseenavant,
                    PriceDispSequence,
                    ISNULL(adh.Pref_Affichage,  sum( 9999 +gt.PriceDispSequence)) as prefAffichageAdhesion, -- permet de mettre les tarifs hors adhésion à la fin
                    ISNULL(adh.Pref_Affichage, 99999) as prefAffichageAdhesion2, -- permet de mettre les tarifs hors adhésion à la fin
                    case when (select  Propriete_Valeur_Int1 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_MAITRE') IS NULL then 0 else 1 end as isTarifMaitre,

                    case when (select  Propriete_Valeur_Int1 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_ELEVE') IS NULL then 0 else 1 end as isTarifEleve,

                    --Min du tarif maitre de l'adhésion sur Rodrigue
                    (select  Propriete_Valeur_Int2 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_MAITRE')  as rodMinMaitre,

                    --Max du tarif maitre de l'adhésion sur Rodrigue
                    (select  Propriete_Valeur_Int3 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_MAITRE')  as rodMaxMaitre,

                    --Min du tarif elève de l'adhésion sur Rodrigue
                     (select  Propriete_Valeur_Int2 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_ELEVE')  as rodMinEleve,

                    --Max du tarif elève de l'adhésion sur Rodrigue
                     (select  Propriete_Valeur_Int3 from Adhesion_Catalog_Propriete  acprop 
                    where Adhesion_Catalog_ID =adh.Adhesion_Catalog_ID and acprop.Propriete_Valeur_Int1 = gt.priceid and acprop.Propriete_Code = 'TARIF_ELEVE')  as rodMaxEleve
                    ,zapper_ZES, zapper_ze,
                    (select islock from GP_MANIFESTATION gpm where gpm.manifestation_id = event_Id) as is_lock
INTO #tmpTableGrilleTarif

FROM #tableresultgrilletarif gt
INNER JOIN manifestation m on m.manifestation_id = gt.event_Id
INNER JOIN seance sea on sea.seance_Id = gt.session_id
INNER JOIN lieu_configuration liconf on liconf.lieu_config_id = sea.lieu_config_id
INNER JOIN lieu l on l.lieu_id = liconf.lieu_ID

INNER JOIN gestion_place gp on gp.gestion_place_id = gt.gpid

LEFT JOIN offre_gestion_place ogp on ogp.gestion_place_id = gt.gpid
LEFT JOIN offre o on o.offre_id = ogp.offre_id
LEFT JOIN adhesion_catalog_offresliees aco on aco.offre_id = o.offre_id
LEFT JOIN Adhesion_Catalog adh on adh.Adhesion_Catalog_ID = aco.adhesion_catalog_id

WHERE event_Id not in(select manifestation_id from GP_manifestation where islock =1)
AND session_id not in(select seance_id from GP_seance where islock =1)

GROUP BY event_Id, m.manifestation_code, m.manifestation_nom,
		nepasafficherdate,
		l.lieu_id, l.lieu_nom,
		session_id, 
		sea.seance_date_deb, sea.seance_date_fin,		
		railingpriceid, 
		amountexcepttax, 
		charge, 
		tax, 
		discount, 
		commission, 
		totaltax,  
		totalamount, 
		ticketamount , 
		zoneid, 
		zonename, 
		sectionid, 
		sectionname, 
		floorname, 
		floorid, 
		gt.categ_Id, 
		categoryname, 
		categorycode, categorycolor, categdispSequence,
		priceid, 
		pricename, 
		nbseats, 
		nbseatsmin, 
		nbseatsmax, 
		nbhisto,
		bookingtype, 
		notnumbered, 
		priseauto,
		surplan,
		voirplace,
		adh.Mise_En_Avant,
		adh.Pref_Affichage,
		PriceDispSequence,
		o.offre_id, o.offre_nom,
		adh.Adhesion_Catalog_ID,
		adh.Catalog_Libelle,
		zapper_ZES, zapper_ze	,
		zonedispSequence, floordispSequence,
		sectiondispSequence,
		gestion_place_parent_id

    ORDER  BY event_Id, 
              session_id, 
			  categdispSequence,
              categoryname, 
			  zonedispSequence,
			  floordispSequence, 
			  sectiondispSequence,
              prefAffichageAdhesion,
              priceDispSequence,
              isTarifMaitre,
              isTarifEleve

SELECT * FROM #tmpTableGrilleTarif WHERE nbseats > 0
ORDER  BY event_Id, 
              session_id, 
			  categdispSequence,
              categoryname, 
			  zonedispSequence,
			  floordispSequence, 
			  sectiondispSequence,
              prefAffichageAdhesion,
              PriceDispSequence,
              isTarifMaitre,
              isTarifEleve,
              pricename,
			  offerId 
              
SELECT distinct * 
FROM #tableresultsession 
ORDER by nbseatmax asc

DROP TABLE #myhisto 
DROP TABLE #Gpres
DROP TABLE #GpLight
DROP TABLE #mygp
DROP TABLE #tableresultgrilletarif 
DROP TABLE #tableresultsession 
DROP TABLE #tmpTableGrilleTarif 
