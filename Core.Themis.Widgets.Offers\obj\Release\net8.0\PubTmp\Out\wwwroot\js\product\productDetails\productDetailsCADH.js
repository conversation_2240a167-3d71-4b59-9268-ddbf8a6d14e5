﻿$(document).ready(function () {
    initChangeModalAndCollapse()
    //bind click bouton ajouter au panier
    $('#addProductToBasket').off('click').on('click', function () {
        console.log('addProductToBasket')
        $('#cardsContentWrapper').removeClass('was-validated')
        haserror = false
        $.each($('#cardsContentWrapper .oneRowCustomerAdhesion'), function (i, k) {
            if ($(k).attr('data-identityid') == "") {
                haserror = true
                $(k).find('.selectConsumersForAdh')[0].setCustomValidity('error');
                $(k).find('.inputNumberAdherent')[0].setCustomValidity('error');
            } 
        })
        if (haserror) {
            event.preventDefault();
            event.stopPropagation();
            $('#cardsContentWrapper').addClass('was-validated')
        } else {
            loadingButtonBootstrapOn($("#addProductToBasket"))
            submitCardsContent()
        }
    })
    $('#addMoreCards').off('click').on('click', function () {
        if (identityId != 0) {
            loadingButtonBootstrapOn($(this))
            
            //si l'utilisateur est loggé, on ajoute une carte
            if ($('#hidden-template-adhesionproductconsumer').html() != "") {
                loadingButtonBootstrapOff($('#addMoreCards'))
                addAdhCards()
                
            } else {
                initSelectProductConsumers()
            }
           
            
            
        } else {
            //sinon, on ouvre une modal pour lui demander de se logger / signup
            $('#modalAskLogin').modal({
                backdrop: 'static',
                keyboard: false
            })
            $('#modalAskLoginBtnCreateAccount, #modalAskLoginBtnLogin').off('click').on('click', function () {
                window.parent.postMessage({
                    "action": "openCustomerArea",
                }, "*")
            })
        }

    })
    

    formatDevise()

    sendIframeSize()
});

// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
// (custom pour la page "Events")
function lessReady() {
    sendIframeSize()
    console.log('productDetailsCADH.js lessReady READY')

}

//formatage des devises
function formatDevise() {
    $.each($('[data-pricetoformat]'), function (i, k) {
        $(k).html(SetDeviseCode(parseInt($(k).attr('data-pricetoformat'))))
    })
}

//charge le template du select + input adherent
function initSelectProductConsumers() {
    $.ajax({
        type: "GET",
        //GetAdhesionProductConsumerAjax/{structureId}/{identiteId}/{langCode}/{webUserId}/{buyerProfilId}/{productId}
        url: widgetCustomerUrl + "GetAdhesionProductConsumerAjax/" + structureId + "/" + identityId + "/" + langCode + "/" + webUserId + "/" + buyerProfilId + "/" + productAdhId,
        data: {
            token: partnerToken
        }, success: function (data) {
            $('#hidden-template-adhesionproductconsumer').html(data)
            addAdhCards()
            
        },
        error: function (a, b, c) {
            console.log("GetAdhesionProductConsumerAjax -> Error")
            console.log(a.responseText)
        },
        complete: function () {
            loadingButtonBootstrapOff($('#addMoreCards'))
        }
    });
}

//ajoute des cartes adhesion
function addAdhCards() {
    
    $('#cardsContentWrapper').removeClass('was-validated')
    $('#addProductToBasket').prop("disabled", false)
    var template = $('#hidden-template-adhesionproductconsumer').html()
    var lastRowId = 0
    if ($('#cardsContentWrapper .oneRowCustomerAdhesion').length > 0) {
        lastRowId = parseInt($('#cardsContentWrapper .oneRowCustomerAdhesion').last().attr('data-rowid')) + 1
    }
        
    template = template.replaceAll('[loop]', lastRowId).replaceAll('[rowid]', lastRowId).replaceAll('[adhesioncatalogid]', productAdhCatalogId)
    //console.log(template)
    $('#cardsContentWrapper').append(template)
   
    calculAmountCards()

    $('#cardsContentWrapper .deleteConsumersForProduct').off('click').on('click', function () {
        var thisRowid = $(this).closest('.oneRowCustomerAdhesion').attr('data-rowid')
        if ($(this).closest('.oneRowCustomerAdhesion').attr('data-identityid') != null) {
            var identityId = $(this).closest('.oneRowCustomerAdhesion').attr('data-identityid')
            lockUnlockIdentityOnSamePricesOffer(false, identityId, thisRowid)
        }
        $(this).closest('.oneRowCustomerAdhesion').remove()
        calculAmountCards()
        if ($('#cardsContentWrapper .oneRowCustomerAdhesion').length == 0) {
            $('#addProductToBasket').prop("disabled", true)
        }
    })
    //bouton check adherent
    $('#cardsContentWrapper  .btnCheckIsAdherent').off('click').on('click', function (e) {
        e.preventDefault()
        e.stopPropagation();
        loadingButtonBootstrapOn(this)

        var adherentId = 0;
        var thisRowid = parseInt($(this).closest('.oneRowCustomerAdhesion').attr('data-rowid'));
        var consumerId = parseInt($('#selectConsumersForAdh_' + thisRowid).val());
        var targetCollapse = $('#productsCollapse_' + thisRowid);

        $('#selectConsumersForAdh_' + thisRowid).removeClass('error')
        $('#inputNumberAdherent_' + thisRowid).removeClass('error')

        $(targetCollapse).removeClass('show')
        /*if ($('#inputNumberAdherent_' + thisRowid).val().trim() != "") {
            adherentId = parseInt($('#inputNumberAdherent_' + thisRowid).val())
        }*/
        if (adherentId != 0) {
            //check si le consommateur est un adhérant via un numéro d'adhérent
            checkConsumerIsAdherent(structureId, adherentId, 0, thisRowid, productAdhId);
        } else if (consumerId != 0) {
            //check si le consommateur est un adhérant via le pool de consommateur
            checkConsumerIsAdherent(structureId, 0, consumerId, thisRowid, productAdhId);
        } else {
            //aucun des deux n'est rempli
            loadingButtonBootstrapOff(this)
        }
    })

    $('#cardsContentWrapper .selectConsumersForAdh').off('change').on('change', function () {
        $('#cardsContentWrapper').removeClass('was-validated')
        var thisRowId = parseInt($(this).closest('.oneRowCustomerAdhesion').attr('data-rowid'));
        var thisLoop = parseInt($(this).closest('.oneRowCustomerAdhesion').attr('data-loop'));
        var thisAdhesionCatalogId = parseInt($(this).closest('.oneRowCustomerAdhesion').attr('data-adhesioncatalogid'));
        var targetCollapse = $(this).closest('.oneRowCustomerAdhesion').find('#productsCollapse_' + thisRowId);
        var targetHtml = $(this).closest('.oneRowCustomerAdhesion').find('#productsCollapse_' + thisRowId);

        //reset l'apparition du bouton valider, éditer, inprogresss
        $('.btnCheckIsAdherent[data-rowid="' + thisRowId + '"]').show()
        $('.adherentProgressWrapper[data-rowid="' + thisRowId + '"]').hide()
        $('.adherentEditWrapper[data-rowid="' + thisRowId + '"]').hide()

        $(targetCollapse).removeClass('show')
        $(targetCollapse).html('')
        if ($(this).val() == "create") {
            $.ajax({
                type: "GET",
                url: widgetCustomerUrl + "Consumers/CreateConsumerFormAjax/" + structureId + "/" + langCode,
                data: {
                    buyerProfilId: buyerProfilId,
                    token: partnerToken
                },
                success: function (data) {
                    drawCreateConsumerForm(data, targetHtml, targetCollapse, thisLoop, thisAdhesionCatalogId)
                },
                error: function (a, b, c) {
                    console.log("Consumers/CreateConsumerFormAjax -> Error")
                }
            });
        }

        if ($(this).val() == "search") {
            $.ajax({
                type: "GET",
                url: widgetCustomerUrl + "Consumers/SearchConsumerFormAjax/" + structureId + "/" + langCode,
                data: {
                    buyerProfilId: buyerProfilId,
                    token: partnerToken
                },
                success: function (data) {
                    drawSearchConsumerForm(data, targetHtml, targetCollapse, thisLoop, thisAdhesionCatalogId)
                },
                error: function (a, b, c) {
                    console.log("Consumers/SearchConsumerFormAjax -> Error")
                }
            });
        }
    })
    initChangeModalAndCollapse()
    sendIframeSize()
}

function calculAmountCards() {
    var nbmax = parseInt($('#bel_productdetail').attr('data-nbmax'))
    var nbcards = $('#cardsContentWrapper .oneRowCustomerAdhesion ').length
    if (nbcards < nbmax) {
        $('#addMoreCards').prop('disabled', false).removeClass('disabled')
    } else {
        $('#addMoreCards').prop('disabled', true).addClass('disabled')
    }
    var totalAmount = 0
    var unitAmount = parseInt($('#bel_productdetail').attr('data-amount'))
    totalAmount = (unitAmount * $('#cardsContentWrapper .oneRowCustomerAdhesion').length)
    $('#addProductToBasket .totalAmount').html(SetDeviseCode(totalAmount))
}
//verifie si le consommateur est éligible à ce tarif adhesion
function checkConsumerIsAdherent(structureId, adherentId, identityId, thisRowid, productId) {
    $.ajax({
        type: "POST",
        url: widgetCustomerUrl + "CheckConsumerIsAdherentAjax/" + structureId + "/" + identityId + "/" + adherentId + "/0/" + productId + "/" + langCode,
        data: {
            token: partnerToken
        },
        success: function (data) {
            /*
             200 => pas adherent (ni open ni panier)
             403 => pas adherent (open) mais carte dans panier (adhesion en cours)
             401 => adherent (déja adherent bdd)
             */
            //if ( data.identity != null && data.identity.identiteId != 0) {
            var identityId = 0
            if (data.identity != undefined) {
                identityId = data.identity.identiteId;
            }
            if (data.identity == undefined) {
                ConsumerIsAdherentNotOk(thisRowid, data, "Generic")
            }else if (data.identity != undefined && !checkIfConsumersAlreadyAttached(identityId, thisRowid)) {
                switch (data.statusAdh) {
                    case 200:
                        ConsumerIsAdherentOk(thisRowid, data);
                        break;
                    case 403:
                        ConsumerIsAdherentNotOk(thisRowid, data, "NotAllowed")
                        break;
                    case 401:
                        ConsumerIsAdherentNotOk(thisRowid, data, "NotAllowed")
                        break;
                    default:
                        // code block
                        ConsumerIsAdherentNotOk(thisRowid, data, "Generic")
                }
            } else {
                ConsumerIsAdherentNotOk(thisRowid, data, "AlreadyAttached")
            }
            //} 
        },
        error: function (a, b, c) {
            console.log("CheckConsumerIsAdherentAjax -> Error")
            console.log(a.responseText)
            ConsumerIsAdherentNotOk(thisRowid, a.responseText, "Generic")
        },
        complete: function () {
            loadingButtonBootstrapOff('.btnCheckIsAdherent[data-rowid="' + thisRowid + '"]')
        }
    });
}

//verifie si une identité a déja été attaché a un tarif maitre sur cette même sélection (utile quand l'utilisateur passe par le numéro d'adhérent)
function checkIfConsumersAlreadyAttached(identityId, thisRowid) {
    var alreadyExist = false
    if ($('.oneRowCustomerAdhesion[data-rowid="' + thisRowid + '"][data-identity="' + identityId + '"]').length > 0) {
        alreadyExist = true
    }
    return alreadyExist;
}

//bloque ou débloque une identité (sur les champs du pool) sur le même tarif + offer
function lockUnlockIdentityOnSamePricesOffer(lock, identityId, thisRowid) {
    //console.log('lockUnlockIdentityOnSamePricesOffer')
    //console.log('lock : ' + lock + ' identity : ' + identityId + ' price : ' + priceId + ' offer : ' + offerId)
    var template = $('#hidden-template-adhesionproductconsumer');
    var clone = $(template.html()).clone();
    if (lock) {
        $(clone).find('.selectConsumersForAdh option[value="' + identityId + '"]').prop('disabled', true)
        //si on appelle la fonction pour BLOQUER le consommateur sur les autres champs
        $.each($('.oneRowCustomerAdhesion'), function (i, k) {
            if (parseInt($(k).attr('data-identityid')) != identityId) {
                $(k).find('.selectConsumersForAdh:not(:disabled) option[value="' + identityId + '"]').prop('disabled', true)
                
            }
            if ($(k).find('.selectConsumersForAdh:not(:disabled) option[value="' + identityId + '"]').is(':selected')) {
                if ($(k).attr('data-rowid') != thisRowid) {
                    $(k).find('.selectConsumersForAdh option').first().prop('selected', true);
                }
            }
        })
    } else {
        $(clone).find('.selectConsumersForAdh option[value="' + identityId + '"]').prop('disabled', false)
        //si on appelle la fonction pour DEBLOQUER le consommateur sur tous les champs
        $.each($('.oneRowCustomerAdhesion'), function (i, k) {
            $(k).find('.selectConsumersForAdh option[value="' + identityId + '"]').prop('disabled', false)
        })
    }
    
    $('#hidden-template-adhesionproductconsumer').html(clone);
}

//initialisation du bouton "Modifier" le consommateur attaché
function initBtnEditIsAdherent(targetCollapse, thisRowid) {
    $('.btnEditIsAdherent[data-rowid="' + thisRowid + '"]').off('click').on('click', function (e) {
        e.preventDefault()
        e.stopPropagation();
        var identityId = parseInt($(targetCollapse).closest('.oneRowCustomerAdhesion').attr("data-identityid"))
        lockUnlockIdentityOnSamePricesOffer(false, identityId, thisRowid)
        $(targetCollapse).collapse('hide')
        $('#selectConsumersForAdh_' + thisRowid).prop('disabled', false)
        $('#inputNumberAdherent_' + thisRowid).prop('readonly', false)
        $('.btnCheckIsAdherent[data-rowid="' + thisRowid + '"]').show()
        $('.adherentProgressWrapper[data-rowid="' + thisRowid + '"]').hide()
        $('.adherentEditWrapper[data-rowid="' + thisRowid + '"]').hide()
        $(targetCollapse).closest('.oneRowCustomerAdhesion').attr("data-identityid", "")
        $(targetCollapse).closest('.oneRowCustomerAdhesion').attr("data-complete", false);
        sendIframeSize()
    })
}

//l'utilisateur est adhérent ou possède une carte adhérent à son nom dans le panier, il peut prendre le tarif adhesion
function ConsumerIsAdherentOk(thisRowid, data) {
    var identityId = data.identity.identiteId;
    var targetCollapse = $('#productsCollapse_' + thisRowid);
    var thisRowid = parseInt($(targetCollapse).closest('.oneRowCustomerAdhesion').attr('data-rowid'));
    $(targetCollapse).closest('.oneRowCustomerAdhesion').attr("data-identityid", identityId);
    $(targetCollapse).closest('.oneRowCustomerAdhesion').attr("data-complete", true);


    lockUnlockIdentityOnSamePricesOffer(true, identityId, thisRowid);
    $('#selectConsumersForAdh_' + thisRowid).prop('disabled', true)
    $('#inputNumberAdherent_' + thisRowid).prop('readonly', true)
    $('.btnCheckIsAdherent[data-rowid="' + thisRowid + '"]').hide()
    $('.adherentProgressWrapper[data-rowid="' + thisRowid + '"]').hide()
    $('.adherentEditWrapper[data-rowid="' + thisRowid + '"]').show()
    sendIframeSize()
    //btn edit consommateur lié
    initBtnEditIsAdherent(targetCollapse, thisRowid)
}

//l'utilisateur n'a pas le droit de prendre ce produit adhesion
function ConsumerIsAdherentNotOk(thisRowid, data, ErrorType) {
    /* ******* error type 
    Generic, // erreur de l'ajax
    NotAllowed, // deja adherent, place deja prise
    AlreadyAttached, // adherent deja attaché
    */
    var templateAlertProductAdhesionError = ""
    switch (ErrorType) {
        case "NotAllowed":
            templateAlertProductAdhesionError = $('#hidden-template-alert-linkproductadh-error-notallowed').html();
            break;
        case "PurchasePeriodExceeded":
            templateAlertProductAdhesionError = $('#hidden-template-alert-linkproductadh-error-purchaseperiodexceeded').html();
            break;
        case "AlreadyAttached":
            templateAlertProductAdhesionError = $('#hidden-template-alert-linkproductadh-error-alreadyattached').html();
            break;

        default:
            templateAlertProductAdhesionError = $('#hidden-template-alert-linkproductadh-error-generic').html();
            break;
    }

    var targetCollapse = $('#productsCollapse_' + thisRowid);
    var targetHtml = $('#productsCollapse_' + thisRowid);

    //
    var identityName = ""
    if (data.identity != undefined) {
        identityName = data.identity.firstName + " " + data.identity.surName;
    }
    //
    $(targetHtml).html(templateAlertProductAdhesionError
        .replaceAll("{IdentityName}", identityName)
    )


    $(targetCollapse).collapse('show')
}

//écrit le formulaire de recherche de consommateur
function drawSearchConsumerForm(data, targetHtml, targetCollapse, thisLoop, thisAdhesionCatalogId) {
    var thisRowid = parseInt($(targetCollapse).closest('.oneRowCustomerAdhesion').attr('data-rowid'));
    $(targetHtml).html(data.replaceAll('[targetCollapse]', "#"+targetCollapse.attr('id'))
        .replaceAll('[loop]', thisLoop)
        .replaceAll('[adhesioncatalogid]', thisAdhesionCatalogId))

    $('.btnCheckIsAdherent[data-rowid="' + thisRowid + '"]').hide()
    $('.adherentProgressWrapper[data-rowid="' + thisRowid + '"]').show()
    $('.adherentEditWrapper[data-rowid="' + thisRowid + '"]').hide()
   // $('#selectConsumersForAdh_' + thisRowid).prop('disabled', true)
    $('#inputNumberAdherent_' + thisRowid).prop('readonly', true)
    $(targetCollapse).collapse('show')
    sendIframeSize();
}

//écrit le formulaire de création de consommateur
function drawCreateConsumerForm(data, targetHtml, targetCollapse, thisLoop, thisAdhesionCatalogId) {
    var thisRowid = parseInt($(targetCollapse).closest('.oneRowCustomerAdhesion').attr('data-rowid'));
    $(targetHtml).html(data.replaceAll('[targetCollapse]', "#" +targetCollapse.attr('id'))
        .replaceAll('[loop]', thisLoop)
        .replaceAll('[adhesioncatalogid]', thisAdhesionCatalogId))

    $('.btnCheckIsAdherent[data-rowid="' + thisRowid + '"]').hide()
    $('.adherentProgressWrapper[data-rowid="' + thisRowid + '"]').show()
    $('.adherentEditWrapper[data-rowid="' + thisRowid + '"]').hide()
    //$('#selectConsumersForAdh_' + thisRowid).prop('disabled', true)
    $('#inputNumberAdherent_' + thisRowid).prop('readonly', true)
    $(targetCollapse).collapse('show')
    sendIframeSize();
}

//ajoute les cartes au panier
function submitCardsContent() {

    var CardsContentArr = []
    
    $.each($('#cardsContentWrapper .oneRowCustomerAdhesion '), function (i, k) {
        var CardsContentObj = {}
        var identityId = $(k).attr('data-identityid')
        var productId = $('#bel_productdetail').attr('data-productid')

        CardsContentObj.ProductId = productId
        CardsContentObj.ConsumerId = identityId
        CardsContentObj.Count = 1
        CardsContentArr.push(CardsContentObj)
    })

    console.log(CardsContentArr)
    $.ajax({
        type: "POST",
        //"{structureId}/Product/AddBasketAjax/{identityId}/{webUserId}/{buyerProfilId}/{langCode}"
        url: widgetOfferUrl + structureId + "/Product/AddBasketAjax/" + identityId + "/" + webUserId + "/" + buyerProfilId + "/"+ langCode,
        data: {
            listProducts: CardsContentArr,
            token: partnerToken
        },
        success: function (data) {

            $('#modalAskContinueShoppingProduct').modal()
            $('#BtnContinueShoppingProducts').off('click').on('click', function () {
                var msg = {
                    "action": "continueShoppingProduct",
                }
                window.parent.postMessage(msg, '*')
            })
            $('#BtnContinueShoppingEvents').off('click').on('click', function () {
                var msg = {
                    "action": "continueShoppingEvents",
                }
                window.parent.postMessage(msg, '*')
            })
            $('#BtnGoToBasket').off('click').on('click', function () {
                var msg = {
                    "action": "addBasket",
                    "basketId": data.basketId,
                    "hash": data.hash
                }
                window.parent.postMessage(msg, '*')
            })

        },
        error: function (a, b, c) {
            console.log("Product/AddBasketAjax -> Error")
        },
        complete: function () {
            loadingButtonBootstrapOff($("#addProductToBasket"))
            sendIframeSize()
        }
    });


}

//lorsque la liste des consommateurs a changé, on remplace tous les selects par cette nouvelle liste, et on reinitialise le select
function replaceOptionsselectConsumersForAdh(data, thisRowid) {
    $.each($('.oneRowCustomerAdhesion'), function (i, k) {
        var selectConsumers = $(k).find('.selectConsumersForAdh')
        var inputAdherentNumber = $(k).find('.inputNumberAdherent')
        oldval = ''
        if ($(selectConsumers).val() != "0" && $(selectConsumers).val() != "create" && $(selectConsumers).val() != "search") {
            oldval = $(selectConsumers).val()
        }
        $(selectConsumers).html(data)

        if ($(selectConsumers).attr('id') == "selectConsumersForAdh_" + thisRowid) {
            //celui qui a déclencher le replace
            $(selectConsumers).val($(selectConsumers).find("option[selected]").attr("value"))
            $(selectConsumers).trigger('change');
            $(inputAdherentNumber).prop('readonly', false);
            $(selectConsumers).prop('disabled', false);
        } else if (oldval != "") {
            //si un select avait deja une value avant le remplacement, alors on redéfini
            $(selectConsumers).val(oldval).trigger('change');
        }
    })
}