﻿/*

  declare @pTxtFR varchar(50) = ''
  declare @pTxtEN varchar(50) = ''
  declare @pTxtDE varchar(50) = ''
  declare @pTxtNL varchar(50) = ''

  declare @pTxtIT varchar(50) = ''
  declare @pTxtES varchar(50) = ''
  declare @pTxtPT varchar(50) = ''
  declare @pfieldCode varchar(50) = ''
*/


declare @checkIsFieldExist int 
  set @checkIsFieldExist = (select count(*) from translate_fieldsGlobalTranslation where fieldCode = @pfieldCode )


  if @checkIsFieldExist = 0
  BEGIN
        --if @pfieldCode <> '' and @pTxtFR <> '' and @pTxtEN <> '' and @pTxtDE <> '' and @pTxtNL <> '' 
        if @pfieldCode <> '' 
          insert into  translate_fieldsGlobalTranslation values(@pfieldCode, @pTxtEN, @pTxtFR, @pTxtDE, @pTxtNL, @pTxtIT, @pTxtES, @pTxtPT)


    SELECT CAST(scope_identity() AS int)

 END

 select -1  as fieldCodeId