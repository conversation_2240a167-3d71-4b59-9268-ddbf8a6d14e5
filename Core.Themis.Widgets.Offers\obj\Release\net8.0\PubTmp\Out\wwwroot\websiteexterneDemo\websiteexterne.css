/* resets */
body { margin:0px; padding:0px; }

/* main */
header {
    height: 360px;
    z-index: 10;
}
.header-banner {
    background-color: #333;
    background-image: url('https://37.media.tumblr.com/8b4969985e84b2aa1ac8d3449475f1af/tumblr_n3iftvUesn1snvqtdo1_1280.jpg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    height: 300px;
}

header h1 {
    background-color: rgba(18,72,120, 0.8);
    color: #fff;
    padding: 0 1rem;
    position: absolute;
    top: 2rem; 
    left: 2rem;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%; 
}

nav {
    width: 100%;
    height: 60px;
    background: #292f36;
    postion: fixed;
    z-index: 10;
}

nav div {
    color: white;
    font-size: 2rem;
    line-height: 60px;
    position: absolute;
    top: 0;
    left: 2%;
    visibility: hidden;
}
.visible-title {
    visibility: visible;
}

nav ul { 
    list-style-type: none;
    margin: 0 2% auto 0;
    padding-left: 0;
    text-align: right;
    max-width: 100%;
}
nav ul li { 
    display: inline-block; 
    line-height: 60px;
    margin-left: 10px;
}
nav ul li a {
    text-decoration: none; 
    color: #a9abae;
}

/* demo content */
body { 
    color: #292f36;
    font-family: helvetica;
    line-height: 1.6;
}
.content{ 
    margin: 0 auto;
    padding: 4rem 0;
    width: 960px;
    max-width: 100%;
}
article {
    float: left;
    width: 720px;
}
article p:first-of-type {
    margin-top: 0;
}
aside {
    float: right;
    width: 120px;
}
p img {
    max-width: 100%;
}

@media only screen and (max-width: 960px) {
    .content{ 
        padding: 2rem 0;
    }
    article {
        float: none;
        margin: 0 auto;
        width: 96%;
    }
    article:last-of-type {  
        margin-bottom: 3rem;
    }
    aside {  
        float: none;
        text-align: center;
        width: 100%;
    }
}
#RodrigueWidgetsPlace {
    background-color: #FFFFFF;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(88, 0, 0, 0.1);
    padding: 20px;
    margin: 20px;
    position: relative;
    transition: all 0.3s ease;
}

    #RodrigueWidgetsPlace::before {
        content: '';
        position: absolute;
        top: 20px;
        left: -10px;
        width: 20px;
        height: 20px;
        background-color: #6083b1;
        border-radius: 50%;
        box-shadow: 0 0 0 5px #FFFFFF;
    }

    #RodrigueWidgetsPlace::after {
        content: '';
        position: absolute;
        top: 30px;
        left: 0;
        width: 3px;
        height: calc(100% - 50px);
        background-color: #6083b1;
    }
     