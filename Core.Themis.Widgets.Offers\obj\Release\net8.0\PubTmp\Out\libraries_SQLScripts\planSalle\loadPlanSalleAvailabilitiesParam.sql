﻿
/*
declare @peventid int = 332
declare @psessionid int = 3378
declare @pidentityId int = 0
declare @pbuyerprofilId int = 152
declare @puserId int
*/
	
DECLARE @isrevendeur int = 0
SELECT @isrevendeur = count(*) from profil_acheteur pa WHERE pa.id = @pbuyerprofilId AND pa.is_revendeur = 1		


      SET nocount ON; 

      -- Insert statements for procedure here 
      CREATE TABLE #myoffres 
        ( 
           offre_id  INT, 
           offre_nom VARCHAR(50) 
        ) 

      CREATE TABLE #mygp 
        ( 
           gestion_place_id INT 
        ) 

      EXEC [Sp_ws_getoffres_totable] 
        @pidentityId, 
        @pbuyerprofilId
		
      DECLARE @offrecount INT 

      SELECT @offrecount = Count(*) 
      FROM   #myoffres 

      DECLARE @myoffreid INT 

      SELECT TOP 1 @myoffreid = offre_id 
      FROM   #myoffres; 

      PRINT 'offreid=' + CONVERT(VARCHAR, @myoffreid); 

      IF ( @offrecount = 0 ) 
        BEGIN 
            PRINT 'pas d''Offres............' 

            IF ( @peventId = 0 ) 
              BEGIN 
                  INSERT INTO #mygp 
                  SELECT gp.gestion_place_id 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 
						  AND type_tarif_id IS NOT NULL
              END 
            ELSE 
              BEGIN 
                  INSERT INTO #mygp 
                  SELECT gp.gestion_place_id 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 
                         AND manif_id = @peventId 
                         AND type_tarif_id IS NOT NULL
              END 
        END 
     ELSE 
        BEGIN 
            PRINT 'Offres............' 

            DECLARE @listgp TABLE 
              ( 
                 manif_id         INT, 
                 gestion_place_id INT, 
                 isforpa          INT 
              ) 

                  --select offre_nom, o.offre_id, * from gestion_place gp, 
                  INSERT INTO @listgp 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         1 AS isForpa 
                  FROM   gestion_place gp, 
                         offre_gestion_place ogp, 
                         offre o, 
                         #myoffres myo 
                  WHERE  ogp.offre_id = o.offre_id 
                         AND o.offre_id = myo.offre_id 
                         AND ogp.gestion_place_id = gp.gestion_place_id 
                         AND gp.isvalide = 1 
                         AND manif_id = @peventId 
                         AND seance_id = @psessionId 
                  UNION 
                  SELECT manif_id, 
                         gp.gestion_place_id, 
                         0 AS isForpa 
                  FROM   gestion_place gp 
                  WHERE  gp.isvalide = 1 
                         AND iscontrainteidentite = 0 
                         AND manif_id = @peventId 
                         AND seance_id = @psessionId 

                  INSERT INTO #mygp 
                  SELECT gptemp.gestion_place_id 
                  FROM   (SELECT manif_id, 
                                 Max(isforpa) AS isForpa 
                          FROM   @listgp 
                          GROUP  BY manif_id) s 
                         INNER JOIN @listgp gptemp 
                                 ON gptemp.manif_id = s.manif_id 
                                    AND gptemp.isforpa = s.isforpa 
                  ORDER  BY s.manif_id 
              END 
   

IF (@peventId > 0)
BEGIN
	if (@isrevendeur =0)
	begin
		INSERT INTO #mygp /* add les gestion places des offres d'adhesion mises en avant */
						SELECT gp.gestion_place_id 
						FROM offre o
						INNER JOIN offre_gestion_place ogp on ogp.offre_id = o.offre_id
						INNER JOIN gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
						INNER JOIN Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
						INNER JOIN Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID
                        INNER JOIN Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1
					WHERE --acat.Mise_En_Avant =1 and
					isvalide =1  and manif_id = @peventId 	
	end
END

DECLARE @const_choixsurplan int = 1

CREATE TABLE #Gpres (Gestion_Place_id int ,reserve_id int, categorie_id int,  seance_id int)
INSERT INTO #GPres 
SELECT gpin.gestion_place_id, reserve_id, categ_id, seance_id  from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id
		 INNER JOIN gestion_place_reserve gpr on gpr.gestion_place_id=my.gestion_place_id
		 WHERE gpin.isvalide = 1 and (prise_place & @const_choixsurplan) =1               
		 UNION		 
		 SELECT gpin.gestion_place_id, 0 as  reserve_id, categ_id, seance_id from #mygp my 
		 INNER JOIN gestion_place gpin on gpin.gestion_place_id = my.gestion_place_id		 
		 WHERE gpin.isvalide = 1 AND gpin.aucune_reserve=1 and (prise_place & @const_choixsurplan) =1 


IF (@psessionid >0)
BEGIN
	DELETE #Gpres WHERE seance_id <> @psessionid
END


CREATE CLUSTERED INDEX IX_Gestion_place_id ON #GPres (gestion_place_id)
CREATE INDEX IX_reserve_id ON #GPres (reserve_id)


SELECT distinct entree_id, e.seance_id, e.categorie_id, e.reserve_id, e.iindex, e.entree_etat,
rlp.zone_id, rlp.etage_id, rlp.section_id,
rlp.rang as row,
rlp.siege as seat,
rlp.orientation as orientation,rlp.type_siege, pos_x, pos_y,decal_x, decal_y,
e.alotissement_id, e.contingent_id,  rlp.tribune as tribune_id,  rlp.porte as porte_id, rlp.acces as acces_id, 
rlp.denomination_id, d.denom_nom, rlp.bordure, e.entree_etat, e.alotissement_id, e.contingent_id, rlp.tribune as tribune_id,  rlp.porte as porte_id, rlp.acces as acces_id,
z.zone_nom, et.etage_nom, sect.section_nom , cat.categ_nom, res.reserve_nom, alot.alot_nom, co.conting_nom,  pptrib.nom as tribune, ppacces.nom as acces, ppporte.nom as gate, e.lieu_configuration_id as lieu_id,
CASE when e.flag_selection = 'T' + convert(varchar,@puserId) then 1 else 0 end as is_mine

FROM entree_[eventID] e 
inner join reference_lieu_physique rlp on rlp.iindex = e.iindex and rlp.ref_uniq_phy_id = e.reference_unique_physique_id

INNER JOIN denomination d on d.denom_id = rlp.denomination_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id 
INNER JOIN etage et ON et.etage_id = rlp.etage_id 
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN categorie cat ON cat.categ_id=e.categorie_id
LEFT OUTER JOIN reserve res ON res.reserve_id =e.reserve_id
LEFT OUTER JOIN alotissement alot ON alot.alot_id = e.alotissement_id
LEFT OUTER JOIN contingent co ON co.conting_id = e.contingent_id
LEFT OUTER JOIN Propriete_physique pptrib ON pptrib.id = rlp.tribune 
LEFT OUTER JOIN Propriete_physique ppacces ON ppacces.id = rlp.acces 
LEFT OUTER JOIN Propriete_physique ppporte ON ppporte.id = rlp.porte 

INNER JOIN #Gpres gp on e.reserve_id = gp.reserve_id AND e.categorie_id = gp.categorie_id and e.seance_id = gp.seance_id

WHERE e.entree_etat ='L' and (e.flag_selection ='' or e.flag_selection is null or e.flag_selection = 'T' + convert(varchar,@puserId)) AND e.alotissement_id = 0 AND e.contingent_id =0

DROP TABLE #Gpres
DROP TABLE #myoffres
DROP TABLE #mygp
    
