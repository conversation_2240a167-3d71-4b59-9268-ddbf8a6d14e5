﻿/**
 * Init window.onmessage event function
 * @param {EventListener} e 
 */
window.onmessage = async function (e) {
    if (e.data.action == 'callDotNetMethod') {
        await useDotNetMethod.apply(this, e.data.args.slice(1));
    }
};

/**
 * Set bootstrap modal state (hide | show)
 * Only for bootstrap 4
 * @param {string} selector 
 * @param {string} action 
 */
function setModalBootstrap4State(selector, action){
    $(selector).modal(action);
}

/**
 * Download file from byte array buffer
 * @param {byte[]} buffer 
 * @param {string} fileName 
 * @param {string} mimeType 
 */
function downloadFileFromBuffer(buffer, fileName, mimeType) {

    var blob = new Blob([buffer], { type: mimeType });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

/**
 * send message to iframe parent for use confirmation modal
 * @param {object} modalInfos 
 */
async function useConfirmationModal(modalInfos) {

    var message = {
        "action": "showCustomModal",
        "modalInfos": modalInfos
    };

    sendMessageToIframeParent(message);

    return new Promise(function (resolve, reject) {
        window.addEventListener("message", function handleGetConfirmationResponse (e) {
            if (e.data.action == 'confirmation') {
                resolve(e.data.response);
                window.removeEventListener("click", handleGetConfirmationResponse);
            }
        });
    });
}

/**
 * send message to iframe parent for use confirmation modal
 * @param {object} modalInfos 
 */
async function useCustomModal(modalInfos) {

    var message = {
        "action": "showCustomModal",
        "modalInfos": modalInfos,
    };

    sendMessageToIframeParent(message);

    return new Promise(function (resolve, reject) {
        window.addEventListener("message", function handleGetFormResponse (e) {
            if (e.data.action == 'customResponse') {
                resolve(e.data.response);
                window.removeEventListener("click", handleGetFormResponse);
            }
        });
    });
}

/**
 * send message to iframe parent for use confirmation modal
 * @param {string} html 
 */
async function useToast(html) {

    var message = {
        "action": "showToast",
        "toastHtml": html
    };

    sendMessageToIframeParent(message);
}

/**
 * send message to iframe parent for redirect to an url
 * @param {string} goToUrl 
 */
function sendIframeGoToUrl(goToUrl) {

    var message = {
        "action": "urltogo",
        "url": goToUrl
    }

    sendMessageToIframeParent(message)
}

/**
 * Send message to iframe parent
 * @param {object} message 
 */
function sendMessageToIframeParent(message) {
    window.top.postMessage(message, '*');
}

/**
 * Scroll to the first html element matching with the error selector
 * @param {string} errorSelector default: '.is-invalid'
 */
function scrollToFirstError(errorSelector = '.is-invalid') {
    let errors = document.querySelectorAll(errorSelector);

    if (errors.length > 0) {
        let offsetTop = errors[0].offsetTop;

        var message = {
            "action": "scrollTo",
            "offsetTop": offsetTop
        };

        sendMessageToIframeParent(message)
    }
}

/**
 * use js invokable method from blazor component
 * @param {object} dotNetHelper
 * @param {string} method
 */
async function useDotNetMethod(dotNetHelper, method)
{
    let args = Array.prototype.slice.call(arguments, 2);
    let response = await window[dotNetHelper].invokeMethodAsync(method, ...args);

    var message = {
        "action": "callDotNetMethodResponse",
        "response": response
    };

    sendMessageToIframeParent(message)
}


