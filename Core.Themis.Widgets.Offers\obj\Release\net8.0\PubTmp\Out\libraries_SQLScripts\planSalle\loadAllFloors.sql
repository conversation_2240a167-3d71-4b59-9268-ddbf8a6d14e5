
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT e.lieu_id, e.etage_id,
	case when t.etage_nom is null then e.etage_nom else t.etage_nom end as etage_nom,
	e.etage_code, e.pref_affichage, e.etage_couleur_id 
FROM etage e
LEFT OUTER JOIN traduction_etage t on t.etage_id = e.etage_id and t.langue_id = @LgId
order by e.pref_affichage, e.lieu_id

