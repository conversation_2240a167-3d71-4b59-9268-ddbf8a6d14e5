﻿
UPDATE entree_[eventID] 
 SET [dossier_id] = @pdossierid,
 dateoperation=getdate() 
,[entree_etat]='R'
,[type_tarif_id] = @ppriceid
,[valeur_tarif_stock_id] = @poperateurId
,[ValeurTarifStockVersion] = 0
,[numero_billet]= @pnumerobillet
,[flag_selection] = ''
,[montant1]= @pmontant1/100
,[montant2]= @pmontant2/100
,[montant3]= @pmontant3/100
,[montant4]= @pmontant4/100
,[montant5]= @pmontant5/100
,[montant6]= @pmontant6/100
,[montant7]= @pmontant7/100
,[montant8]= @pmontant8/100
,[montant9]= @pmontant9/100
,[montant10]= @pmontant10/100
WHERE entree_etat='L' and entree_id = @pentreeId 
AND (
	flag_selection= @ptypeTag + convert(varchar(10),@pidentiteId) 
	OR flag_selection like (@ptypeTag +  convert(varchar(10),@pidentiteId)  + '_%') 
	OR flag_selection = @ptypeTag + convert(varchar(10),@pcurrentuserid)
	OR flag_selection = @ptypeTagVerouille + convert(varchar(10),@pidentiteId)
	OR flag_selection like ('_' + convert(varchar(10),@pidentiteId) + '_%')
	OR flag_selection like('%-' + convert(varchar(10),@pidentiteId) )  
	OR flag_selection like ('_' + convert(varchar(10),@pcurrentuserid))
	OR flag_selection=''
	)
		
INSERT into entreesvg_[eventID]
 ([entree_id] 
,[seance_id] 
,[dossier_id] 
,[entree_etat] 
,[categorie_id] 
,[type_tarif_id] 
,[numero_billet] 
,[alotissement_id] 
,[reserve_id] 
,[contingent_id] 
,[montant1]
,[montant2]
,[montant3]
,[montant4]
,[montant5]
,[montant6]
,[montant7]
,[montant8]
,[montant9]
,[montant10]
,[dateoperation] 
,[dossier_v] 
,[valeur_tarif_stock_id] 
,[ValeurTarifStockVersion] )
SELECT 
entree_id,
seance_id,
dossier_id,
entree_etat,
categorie_id,
type_tarif_id,
numero_billet,
alotissement_id,
@preserveid ,
contingent_id,
montant1,
montant2,
montant3,
montant4,
montant5,
montant6,
montant7,
montant8,
montant9,
montant10,
getdate(),
@pentreedossierv,
valeur_tarif_stock_id,
valeurtarifstockversion
FROM entree_[eventID] WHERE entree_id = @pentreeId 
