﻿/*
DECLARE @pKeyword varchar(300)
DECLARE @pLangCode varchar(300)

set @pKeyword = 'Widget_Basket_MsgErrorNotFoundSponsorPanierEntrees'
set @pLangCode = 'fr'
*/



IF (not EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE  TABLE_NAME = 'translate_fieldsSpecificTranslation'))
BEGIN

CREATE TABLE [dbo].[translate_fieldsSpecificTranslation](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[fieldCode] [varchar](max) NULL,
	[val] [varchar](200) NULL,
	[lang_id] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]


ALTER TABLE [dbo].[translate_fieldsSpecificTranslation]  WITH CHECK ADD  CONSTRAINT [FK_translate_fieldsSpecificLocalTranslation_langue] FOREIGN KEY([lang_id])
REFERENCES [dbo].[langue] ([langue_id])



END





DECLARE @nb_row_language int
select @nb_row_language = COUNT(*) from langue where langue_code = @pLangCode

 IF(@nb_row_language > 0)
 BEGIN
	
	DECLARE @langue_id int
	select @langue_id = langue_id from langue where langue_code = @pLangCode

	select * from translate_fieldsSpecificTranslation where fieldCode= @pKeyword and lang_id = @langue_id

END
	
