﻿--nouvel id qui vient d'être inséré
/*declare @evals_response_form_id int

insert into evals_response_form (command_id, response_date, comment)
values (@order_id, getdate(), @comment)


SELECT @evals_response_form_id = CAST(scope_identity() AS int)


insert into evals_response (form_id, question_id, score)
values (@evals_response_form_id, @question_id, @score)
*/



insert into OpinionOrderFormsResponses (form_id, question_id, score)
values (@form_id, @question_id, @score)

SELECT CAST(scope_identity() AS int)
