// Script simple pour corriger l'affichage des messages de non-disponibilité

// Fonction globale pour corriger les éléments .noevent
function fixNoeventElements() {
        $('#alleventhours .noevent').each(function() {
            var $el = $(this);
            console.log('Fix nodisponible: Élément .noevent trouvé', $el.text());

            // Transformer en texte simple (supprimer l'apparence de bouton)
            $el.css({
                'background': 'none',
                'border': 'none',
                'border-radius': '0',
                'box-shadow': 'none',
                'height': 'auto',
                'line-height': '1.4',
                'white-space': 'normal',
                'word-wrap': 'break-word',
                'padding': '5px 0',
                'min-height': 'auto',
                'color': '#666',
                'font-size': '14px',
                'text-align': 'center',
                'display': 'block',
                'cursor': 'default'
            });
        });
}

$(document).ready(function() {
    console.log('Fix nodisponible: Script chargé');

    // Appliquer la correction immédiatement
    fixNoeventElements();

    // Réappliquer après un délai pour les éléments chargés dynamiquement
    setTimeout(fixNoeventElements, 500);
    setTimeout(fixNoeventElements, 1000);

    // Observer les changements DOM pour les nouveaux éléments
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldReapply = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    $(mutation.addedNodes).each(function() {
                        if ($(this).hasClass && $(this).hasClass('noevent')) {
                            shouldReapply = true;
                        }
                    });
                }
            });

            if (shouldReapply) {
                setTimeout(fixNoeventElements, 100);
            }
        });

        var target = document.getElementById('alleventhours');
        if (target) {
            observer.observe(target, { childList: true, subtree: true });
        }
    }
});
