﻿/* Url de la font globale sur le site */
@globalFontUrl: "https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900";

/* Nom de la font globale sur le site */
@globalFontName: "Roboto";

/* couleur de textes */
@globalFontColor: #333333;

/* couleur des lien */
@globalLinkColor: #000000;

/* couleur de background */
@globalBackgroundColor: f8f8f8;

/* couleur principale */
@primaryColor: #000000;

/* couleur de texte des boutons primaire */
@btnPrimaryTextColor: #ffffff;

/* couleur secondaire */
@secondaryColor: #5F5F5F;

/* couleur de texte des boutons secondaire */
@btnSecondaryTextColor: #ffffff;
/*Couleurs des catégorie*/
@seatSelected: #088eb2;

@categ1: #308abe;
@categ2: #b55fc5;
@categ3: #ffa733;
@categ4: #d2b25e;
@categ5: #56d3d8;
@categ6: #308abe;
@categ7: #f7d664;
@categ8: #f2bc00;
@categ9: #d4a61a;
@categ10: #cc99ff;
@categ11: #cc33ff;
@categ12: #9900cc;
@categ13: #ddc097;
@categ14: #ddc097;
@categ15: #ddc097;
@categ16: #ddc097;
@categ17: #ddc097;
@categ18: #ddc097;
@categ19: #ddc097;
@categ20: #ddc097;
@categ21: #ddc097;
@categ22: #ddc097;
@categ23: #ddc097;
@categ24: #ddc097;
@categ25: #ddc097;
@categ26: #ddc097;
@categ27: #ddc097;
@categ28: #ddc097;
@categ29: #ddc097;
@categ30: #ddc097;
@categ31: #ddc097;
@categ32: #ddc097;
@categ33: #ddc097;
@categ34: #ddc097;
@categ35: #ddc097;
@categ36: #ddc097;
@categ37: #ddc097;
@categ38: #ddc097;
@categ39: #ddc097;
@categ40: #ddc097;
@categ41: #ddc097;
@categ42: #ddc097;
@categ43: #ddc097;
@categ44: #ddc097;
@categ45: #ddc097;
@categ46: #ddc097;
@categ47: #ddc097;
@categ48: #ddc097;
@categ49: #ddc097;
@categ50: #ddc097;

@panoratio:"5/4";

@homemodularblockscolor: #000000;

/* import font custom  */
& when not(@globalFontUrl ="") {
    @import (less) "@{globalFontUrl}";
}

/********* GENERAL ********/
body {
    color: @globalFontColor;
    font-family: "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

body when not (@globalFontName ="") {
    font-family: "@{globalFontName}", "Helvetica Neue", Helvetica, Verdana, Trebuchet MS;
}

:focus, :focus-within {
    outline: none !important;
    -webkit-box-shadow: 0 0 0 0  !important;
            box-shadow: 0 0 0 0  !important;
}

a {
    color: @globalLinkColor;
    cursor: pointer;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}

a:hover, a:focus {
    color: darken(@globalLinkColor, 20%)
}
.tooltip-lg > .tooltip-inner {
    max-width: 300px;
}
.tooltip-light.bs-tooltip-auto[x-placement^=top] .arrow::before, .tooltip-light.bs-tooltip-top .arrow::before {
    border-top-color: #fefefe;
}
.tooltip-light.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .tooltip-light.bs-tooltip-bottom .arrow::before {
    border-bottom-color: #fefefe;
}
.tooltip-light.bs-tooltip-auto[x-placement^=left] .arrow::before, .tooltip-light.bs-tooltip-left .arrow::before {
    border-left-color: #fefefe;
}

.tooltip-light.bs-tooltip-auto[x-placement^=right] .arrow::before, .tooltip-light.bs-tooltip-right .arrow::before {
    border-right-color: #fefefe;
}
.tooltip-light .tooltip-inner {
    color: #000;
    background-color: #fefefe;
}
.tooltip-nopadding .tooltip-inner {
    padding:0
}
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.btn {
    border-radius : 100px;
    padding-left:30px;
    padding-right:30px;
}
.btn.initialpadding {
    padding: 0.375rem 0.75rem;
}
.btn-decrement, .btn-increment {
    padding-left: 0px;
    padding-right: 0px;
}
.nav-link.btn {
    display: block;
}

.nav-pills .nav-link.btn {
    border-radius: 100px;
}
.btn-with-arrow-above:after, .btn-with-arrow-below:after {
    font-family: "Font Awesome 6 Free";
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-weight: 900;
    margin-left: 7px;
}

.btn-with-arrow-below:not(.collapsed):after {
    content: "\f077";
}

.btn-with-arrow-below:after {
    content: "\f078";
}

.btn-with-arrow-above:not(.collapsed):after {
    content: "\f078";
}

.btn-with-arrow-above:after {
    content: "\f077";
}

.btn:not([disabled]):hover, .btn:not([disabled]):active, .btn:not([disabled]):focus {
    text-decoration: none;
    cursor: pointer
}

.btn-primary {
    color: @btnPrimaryTextColor;
    background-color: @primaryColor;
    border-color: @primaryColor;
}

.btn-primary:not([disabled]):hover, .btn-primary:not([disabled]):active, .btn-primary:not([disabled]):focus {
    background-color: darken(@primaryColor, 20%);
    border-color: darken(@primaryColor, 20%);
}

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle {
    background-color: darken(@primaryColor, 20%)
}

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus {
}

.btn-primary .badge {
    color: @primaryColor;
    background-color: @btnPrimaryTextColor;
}

.btn-secondary {
    color: @btnSecondaryTextColor;
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}

.btn-secondary:not([disabled]):hover, .btn-secondary:not([disabled]):active, .btn-secondary:not([disabled]):focus,
.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show>.btn-secondary.dropdown-toggle {
    background-color: darken(@secondaryColor, 20%);
    border-color: darken(@secondaryColor, 20%)
}

.btn-outline-secondary {
    color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-outline-secondary:hover, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle {
    color: contrast(@secondaryColor);
    background-color: @secondaryColor;
    border-color: @secondaryColor;
}
.btn-link {
    color: @primaryColor;
    background-color: #f0f0f0;
    border-color: #d9d9d9
}

.btn-link:not([disabled]):hover, .btn-link:not([disabled]):active, .btn-link:not([disabled]):focus {
    background-color: #aaaaaa;
    border-color: #d9d9d9
}

.btn[disabled], .btn.disabled, .btn.disabled:hover, .btn-primary.disabled, .btn-primary[disabled], fieldset[disabled] .btn-primary, .btn-primary.disabled:hover, .btn-primary[disabled]:hover, fieldset[disabled] .btn-primary:hover, .btn-primary.disabled:focus, .btn-primary[disabled]:focus, fieldset[disabled] .btn-primary:focus, .btn-primary.disabled:active, .btn-primary[disabled]:active, fieldset[disabled] .btn-primary:active, .btn-primary.disabled.active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary.active {
    color: #fff;
    background-color: #aaaaaa;
    border-color: #878787
}

.table-primary, .table-primary > td, .table-primary > th {
    background-color: fade(@primaryColor, 10%);
}

.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-primary thead th {
    border-color: fade(@primaryColor, 10%);
}

.custom-control-input:checked ~ .custom-control-label::before {
    color: @btnPrimaryTextColor;
    border-color: @primaryColor;
    background-color: @primaryColor;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 50%);
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
    border-color: fade(@primaryColor, 30%);
}

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: fade(@primaryColor, 30%);
}

@media (min-width: 576px) {
    .modal-xl-wide {
        max-width: 95%;
    }
}

@media (min-width: 992px) {
    .modal-xl-wide {
        max-width: 800px;
    }
}

@media (min-width: 1200px) {
    .modal-xl-wide {
        max-width: 1140px;
    }
}
/*********** EMPLACEMENTS BLOCKS ***********/
//emplacements
.emplacementBlock {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 1.5%;
}
.emplacementBlock .oneblocktype {
     margin-bottom: 1.5%;
}

.emplacementBlock .oneblocktype_carrousel {
    margin-bottom: 3%;
}
.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) {
    color: contrast(@homemodularblockscolor);
    }
/**
.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) {
    height: auto;
    background-color: @homemodularblockscolor;
    color: contrast(@homemodularblockscolor);
    padding-left: 0;
    gap: 1.5%;
    border: 0;
    padding: 1.2rem 1rem;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    width: 100%;
    overflow: hidden;
   
}

.oneblocktype_background {
    background-size: cover;
    background-position: center;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.4;
    -webkit-filter: contrast(1.2) grayscale(1);
            filter: contrast(1.2) grayscale(1);
    mix-blend-mode: multiply;
    z-index: 1;
    -webkit-transition: -webkit-transform 0.2s;
    transition: -webkit-transform 0.2s;
    -o-transition: transform 0.2s;
    transition: transform 0.2s;
    transition: transform 0.2s, -webkit-transform 0.2s;
}*/
.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) {
    position:relative;
    overflow: hidden;
       display: flex;
    flex-direction: column;
}
.oneblocktype_background_wrapper {
    width: 100%;
    height: auto;
    position:relative;
    display:block;
    overflow:hidden;
    background-color: @homemodularblockscolor;
    display:flex;
    padding: 1.2rem 1rem;
 }
.oneblocktype_background {
    
    color: contrast(@homemodularblockscolor);
    background-size: cover;
    background-position: center;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.4;
    -webkit-filter: contrast(1.2) grayscale(1);
            filter: contrast(1.2) grayscale(1);
    mix-blend-mode: multiply;
    z-index: 1;
    -webkit-transition: -webkit-transform 0.2s;
    transition: -webkit-transform 0.2s;
    -o-transition: transform 0.2s;
    transition: transform 0.2s;
    transition: transform 0.2s, -webkit-transform 0.2s;
}

.emplacementBlock .oneblocktype > * {
    -ms-flex-item-align: auto;
        -ms-grid-row-align: auto;
        align-self: auto;
}

.emplacementBlock .oneblocktype .item-icon-wrapper {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -ms-flex-item-align: center;
        -ms-grid-row-align: center;
        align-self: center;
    position: relative;
    z-index: 2;
    width: 100%;
}

.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) :not([data-href='']):hover {
    text-decoration: none;
    cursor: pointer;
}

.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel):hover  .oneblocktype_background {
    -webkit-transform: scale(1.1);
        -ms-transform: scale(1.1);
            transform: scale(1.1);
}
.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel)  .item-name-inner-background {
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
    color: contrast(@homemodularblockscolor);
    position: absolute;
    text-align: center;
    font-weight: bold;
}
.emplacementBlock .oneblocktype .item-icon {
    width: 100%;
    text-align: center;
    opacity: 0.5;
}

.emplacementBlock .oneblocktype .item-icon .fa-ticket {
    -webkit-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
            transform: rotate(-45deg);
}
.emplacementBlock .oneblocktype .item-name-wrapper {
    padding-left: 0;
    margin-top: auto;
    position: relative;
    z-index: 2;
    padding: 0.4rem 0.6rem;    
    border-radius: 0px 0px .25rem .25rem ;
    flex-grow:1
}

.emplacementBlock .oneblocktype .item-showunavailable {
    margin-top: auto;
    background-color : #c41111;
    color:#fff;
    position: relative;
    z-index: 2;
    padding: 0.2rem 0.6rem;
    font-size: 0.75em;
    width:fit-content;
    border-radius:5px;
}
.emplacementBlock .oneblocktype .item-showunavailable + .item-name-wrapper {
    margin-top: 0.25rem;
}
.emplacementBlock .oneblocktype .item-name-wrapper .item-name, .emplacementBlock .oneblocktype .item-value-wrapper:not(:empty) {
    line-height: 1.3em;
    overflow-wrap: anywhere;
}
.emplacementBlock .oneblocktype .item-name-wrapper .item-value {
    display: block;
    font-weight: bold;
    margin-bottom: 0;
}
/**** carousel ****/
.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-inner {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-left: 0;
    margin-right: 0;
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    /*gap: 30px;*/
    padding: 15px 0;
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item {
    margin-right: 0;
    display: block;
    -webkit-transition: margin 0.6s ease-in-out;
    -o-transition: margin 0.6s ease-in-out;
    transition: margin 0.6s ease-in-out;
    margin: 0 10px;
}



.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item:not([data-href=""]):hover {
    cursor: pointer;
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item:hover .eventImg, .emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item:hover .productImg {
    -webkit-transform: scale(1.1);
        -ms-transform: scale(1.1);
            transform: scale(1.1)
}
//uniquement carrousel events
.emplacementBlock .oneblocktype_events_featured .carousel[data-cols] .carousel-item:before {
    background: url(../../img/ticket-shape-bottom.svg) no-repeat;
    background-position: center bottom;
    background-size: cover;
    content: '';
    position: absolute;
    -webkit-filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    height: 100%;
    //background-size: calc(100% + 3px);
    width: 100%;
    right: 0;
    top: 50%;
    bottom: 0;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
}
//uniquement carrousel products
.emplacementBlock .oneblocktype_products_featured .carousel[data-cols] .carousel-item {
    -webkit-filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    background-color:#fff;
    border-radius:7px;
    overflow:hidden;
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-inner .carousel-item-next,
.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-inner .carousel-item-prev,
.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-inner .carousel-item-left,
.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-inner .carousel-item-right {
    position: relative;
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item.translateleft {
    -webkit-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
            transform: translateX(-100%);
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-item.translateright {
    -webkit-transform: translateX(100%);
        -ms-transform: translateX(100%);
            transform: translateX(100%);
}

.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-control-next,
.emplacementBlock .oneblocktype .carousel[data-cols] .carousel-control-prev {
    width: 7%;
    color: @globalFontColor;
    background-color: contrast(@globalFontColor);
}

.emplacementBlock .oneblocktype .carousel[data-cols] .eventImgWrapper, .emplacementBlock .oneblocktype .carousel[data-cols] .productImgWrapper {
    aspect-ratio: 1/1;
    width: 100%;
    overflow: hidden;
}

.emplacementBlock .oneblocktype .carousel[data-cols] .eventImg, .emplacementBlock .oneblocktype .carousel[data-cols] .productImg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    position: relative;
    -webkit-transition: -webkit-transform 0.5s ease-in-out;
    transition: -webkit-transform 0.5s ease-in-out;
    -o-transition: transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out;
    transition: transform 0.5s ease-in-out, -webkit-transform 0.5s ease-in-out;
}

.emplacementBlock .oneblocktype .carousel[data-cols] .eventName, .emplacementBlock .oneblocktype .carousel[data-cols] .productName {
    z-index: 1;
    position: relative;
    text-align: center;
    padding: 10px 15px 7% 15px;
    color: @globalFontColor;
    word-break: break-word;
}

.emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) .item-icon {
    font-size: 3.5rem;
}

// Small devices (landscape phones, 576px and up)
@media (min-width: 576px) {
   
    /* block 1/3 */
    .emplacementBlock .oneblocktypesize_1:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper {
        font-size: 0.8rem;
        aspect-ratio: 1/1;
    }
    /* block 1/2 */
    .emplacementBlock .oneblocktypesize_1-5:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper {
        font-size: 1rem;
        aspect-ratio: 2/1.33;
    }
    /* block 2/3  */
    .emplacementBlock .oneblocktypesize_2:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper {
        font-size: 1.2rem;
           aspect-ratio: 2.046/1;

    }
    /* block 3/3 */
    .emplacementBlock .oneblocktypesize_3:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.4rem;
        aspect-ratio: 3/1;
    }
    /* icon */
    .emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) .item-icon {
        font-size: 2.5rem;
        aspect-ratio: 3/1;
    }
}
// Medium devices (tablets, 768px and up)
@media (min-width: 768px) {
    /* .emplacementBlock .oneblocktype .item-name-wrapper {
        padding: 1rem;
    }*/
    /* block 1/3 */
    .emplacementBlock .oneblocktypesize_1:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1rem;
    }
    /* block 1/2 */
    .emplacementBlock .oneblocktypesize_1-5:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.2rem;
    }
    /* block 2/3  */
    .emplacementBlock .oneblocktypesize_2:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.4rem;
    }
    /* block 3/3 */
    .emplacementBlock .oneblocktypesize_3:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.6rem;
    }
    /* icon */
    .emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) .item-icon {
        font-size: 3rem;
    }
}
// Large devices (desktops, 992px and up)
@media (min-width: 992px) {
    /* block 1/3 */
    .emplacementBlock .oneblocktypesize_1:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.2rem;
    }
    /* block 1/2 */
    .emplacementBlock .oneblocktypesize_1-5:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.4rem;
    }
    /* block 2/3  */
    .emplacementBlock .oneblocktypesize_2:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.6rem;
    }
    /* block 3/3 */
    .emplacementBlock .oneblocktypesize_3:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.8rem;
    }
    /* icon */
    .emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) .item-icon {
        font-size: 4rem;
    }
}
// Extra large devices (large desktops, 1200px and up)
@media (min-width: 1200px) {
    /* block 1/3 */
    .emplacementBlock .oneblocktypesize_1:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.4rem;
    }
    /* block 1/2 */
    .emplacementBlock .oneblocktypesize_1-5:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.6rem;
    }
    /* block 2/3  */
    .emplacementBlock .oneblocktypesize_2:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 1.8rem;
    }
    /* block 3/3 */
    .emplacementBlock .oneblocktypesize_3:not(.oneblocktype_carrousel) .oneblocktype_background_wrapper{
        font-size: 2rem;
    }
    /* icon */
    .emplacementBlock .oneblocktype:not(.oneblocktype_carrousel) .item-icon {
        font-size: 5rem;
    }
}


@media (max-width: 575.98px) {
    .emplacementBlock {
        display: block;
    }

    .emplacementBlock .oneblocktype .oneblocktype_background_wrapper{
        aspect-ratio: 3/2;
        font-size: 1.3rem;
    }
}