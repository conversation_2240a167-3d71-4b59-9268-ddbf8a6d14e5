﻿/* maj date_envoi_done */

declare @thisDateEnvoi datetime

SELECT @thisDateEnvoi = date_envoi FROM OrdersReservationReminders WHERE id=@schedule_id AND orderReservation_id in 
	(SELECT id FROM OrdersReservation ord WHERE structure_id = @structure_id AND commande_id=@order_id)

UPDATE OrdersReservationReminders
SET date_envoi_done=getdate() WHERE id=@schedule_id AND orderReservation_id in 
	(SELECT id FROM OrdersReservation ord WHERE structure_id = @structure_id AND commande_id=@order_id)

/* update les OrdersReservationReminders qu'on a eventuellement loupé : ne pas renvoyer un autre mail */
UPDATE OrdersReservationReminders 
SET date_envoi_done=getdate() WHERE date_envoi < @thisDateEnvoi AND orderReservation_id in 
	(SELECT id FROM OrdersReservation ord WHERE structure_id = @structure_id AND commande_id=@order_id)



