﻿
//$(window).resize(function () {
//	if ($(window).outerWidth(true).toString() != $('body').attr('oldwidth')) {
//		$('body').attr('oldwidth', $(window).outerWidth(true).toString())
//		initResizeEventsOverlay()
//	}
//});

function initGridList(showImage) {
	$('[data-viewmode]').off('click').on('click', function () {
		var bigwrapper = $(this).closest("[data-eventareaconfigcount]")
		var mode = $(this).attr("data-viewmode")
		$(bigwrapper).find('[data-viewmode]').removeClass('active')
		$(this).addClass('active')
		$.each($(bigwrapper).find('[data-viewmode]'), function (i, k) {
			$(bigwrapper).find('.eventscatalog_all').removeClass($(k).attr("data-viewmode"))
		})
		$(bigwrapper).find('.eventscatalog_all').addClass(mode)
		initResizeEventsOverlay(showImage)
	})

	$('[data-viewmode].active').trigger('click')
}

function initResizeEventsOverlay(showImage) {
	$('.event_one_front').css({
		"height": ""
	})
	const currentBreakpoint = bootstrapDetectBreakpoint()
	if (currentBreakpoint.index >= 2) {
		
		$.each($('.eventscatalog_all.grid'), function (i, k) {
			//
			maxheight = 0;
			
			/*if (showImage == false) {
				//si l'image des évènements est masquée
				$(k).find('.event_one .event_one_back').css({ "opacity": 1, "position": "relative", "height": "100%" })
				$(k).find('.event_one .event_one_front').css({ "display": "none" })
				$.each($(k).find('.event_one .event_one_back'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.event_one .event_one_back').css({
					"height": maxheight
				})
			} else {
				//si l'image des évènements est affichée
				$.each($(k).find('.event_one .event_one_front'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.event_one .event_one_front').css({
					"height": maxheight
				})
			}*/

			if (showImage == true) {
				//si l'image des évènements est affichée
				$.each($(k).find('.event_one .event_one_front'), function (eventi, eventk) {
					if ($(eventk).height() > maxheight) {
						maxheight = $(eventk).outerHeight()
					}

				})
				$(k).find('.event_one .event_one_front').css({
					"height": maxheight
				})
			}
			
			$(k).attr('initial-cards-height', maxheight)
			$(k).attr('initial-height', $(k).outerHeight())
			
		})

		//si l'image des évènements est affichée
		if (showImage == true) {
			$('.eventscatalog_all.grid .event_one').off("mouseenter").on("mouseenter", function () {
				var front = $(this).find('.event_one_front')
				var back = $(this).find('.event_one_back')

				var backMaxHeight = $(back).outerHeight() + parseInt($(back).css('border-top-width')) + parseInt($(back).css('border-bottom-width'))

				var dif = 0
				if (backMaxHeight > $(front).outerHeight()) {
					dif = backMaxHeight - parseFloat($(front).css("height"))
					if (($(this).position().top + backMaxHeight > ($(this).closest('.eventscatalog_all').position().top + parseFloat($(this).closest('.eventscatalog_all').attr('initial-height'))))) {
						var eventscatalog_allHeight = $(this).closest('.eventscatalog_all').outerHeight() + dif
						$(front).css({
							"height": backMaxHeight
						})
						sendIframeSize(dif)
					}
				}

				if (backMaxHeight < $(front).outerHeight()) {
					$(back).css({
						"height": $(front).outerHeight()
					})
				}

			}).off("mouseleave").on("mouseleave", function () {
				var front = $(this).find('.event_one_front')
				var back = $(this).find('.event_one_back')
				$(back).css({
					"height": ""
				})
				$(front).css({
					"height": $(this).closest('.eventscatalog_all').attr('initial-cards-height')
				})
				sendIframeSize()
			});
		} 
		sendIframeSize()
	} else {
		$('.event_one').off("mouseenter").off("mouseleave")
		sendIframeSize()
	}
}

function bindClickDataHref() {
	$("[data-href]:not([data-href='']").off('click').on('click', function () {
		var msg = {}
		switch ($(this).attr('data-action')) {
			case "openwaitinglist":
				msg = {
					"action": $(this).attr('data-action'),
					"url": $(this).attr('data-href')
				}
				break;
			default:
				msg = {
					"action": "urltogo",
					"url": $(this).attr('data-href')
				}
				break;
		}
		window.parent.postMessage(msg, '*')
	})
}