﻿// Fichier: wwwroot/js/phoneInput.js

// Configuration par défaut
const defaultConfig = {
    version: '17.0.17',
    preferredCountries: ['fr', 'be', 'ch'],
    initialCountry: 'fr'
};

// Variable pour stocker l'instance
let phoneInputInstance = null;

// Fonction pour charger une ressource CSS
function loadCSS(url) {
    return new Promise((resolve, reject) => {
        if (document.querySelector(`link[href="${url}"]`)) {
            resolve();
            return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        link.onload = () => resolve();
        link.onerror = () => reject(new Error(`Échec de chargement CSS: ${url}`));
        document.head.appendChild(link);
    });
}

// Fonction pour charger un script JS
function loadScript(url) {
    return new Promise((resolve, reject) => {
        if (document.querySelector(`script[src="${url}"]`)) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = url;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Échec de chargement script: ${url}`));
        document.head.appendChild(script);
    });
}

// Fonction pour initialiser intlTelInput
export async function initPhoneInput(elementId, options = {}) {
    try {
        // Fusionner les options avec les valeurs par défaut
        const config = { ...defaultConfig, ...options };

        // Charger les ressources nécessaires
        await loadCSS(`https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/${config.version}/css/intlTelInput.min.css`);
        await loadScript(`https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/${config.version}/js/intlTelInput.min.js`);
        await loadScript(`https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/${config.version}/js/utils.js`);

        // Vérifier que intlTelInput est disponible
        if (typeof window.intlTelInput === 'undefined') {
            throw new Error('intlTelInput n\'est pas disponible après le chargement des scripts');
        }

        // Obtenir l'élément
        const element = document.getElementById(elementId);
        if (!element) {
            throw new Error(`Élément avec ID '${elementId}' non trouvé`);
        }

        // Initialiser intlTelInput
        phoneInputInstance = window.intlTelInput(element, {
            preferredCountries: config.preferredCountries,
            initialCountry: config.initialCountry,
            utilsScript: `https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/${config.version}/js/utils.js`
        });

        // Ajouter des événements
        element.addEventListener('input', () => {
            const number = getPhoneNumber();
            console.log('Numéro saisi:', number);
        });

        // Retourner true pour indiquer le succès
        return true;
    } catch (error) {
        console.error('Erreur lors de l\'initialisation du téléphone:', error);
        return false;
    }
}

// Fonction pour récupérer le numéro de téléphone
export function getPhoneNumber() {
    if (phoneInputInstance) {
        try {
            return phoneInputInstance.getNumber() || '';
        } catch (error) {
            console.error('Erreur lors de la récupération du numéro:', error);
            return '';
        }
    }
    return '';
}

// Fonction pour détruire l'instance
export function destroyPhoneInput() {
    if (phoneInputInstance) {
        try {
            phoneInputInstance.destroy();
            phoneInputInstance = null;
            return true;
        } catch (error) {
            console.error('Erreur lors de la destruction de l\'instance:', error);
            return false;
        }
    }
    return false;
}