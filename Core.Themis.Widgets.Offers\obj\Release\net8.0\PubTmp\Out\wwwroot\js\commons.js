// lorsque le less est pret, il cherche a declencher la function lessReady si elle existe sur un autre js sinon celle du commons
/*less.pageLoadFinished.then(function() {
  if ($.isFunction(window.lessReady)) {
    lessReady()
  } else {
    lessReadyfunc.apply()
  }
});*/
var xmlVueFromSeat;
// LESSREADY se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
var lessReadyfunc = function lessReady() {
  console.log( htmlSelector+' lessReady commons.js')
  sendIframeSize()
}

$(document).ready(function () {
    initChangeModalAndCollapse()
    sendIframeSize()   
    
})
$(window).resize(function () {
    //fixe si une modal plus longue que le contenu est ouverte dans l'iframe 
    /*if ($('body.modal-open').length > 0) {
        var modal = $('.modal.show')
        pushAttrModalHeightToBody(modal)
    }*/
    sendIframeSize()
})
/**** detect breakpoints BS in js ****/
const bootstrapBreakpointNames = function () {
    return ["xxl", "xl", "lg", "md", "sm", "xs"];
}
const bootstrapDetectBreakpoint = function () {
    // cache some values on first call
    if (!this.breakpointValues) {
        this.breakpointNames = bootstrapBreakpointNames()
        this.breakpointValues = []
        const isPriorBS5 = !!window.getComputedStyle(document.documentElement).getPropertyValue('--breakpoint-sm')
        const prefix = isPriorBS5 ? "--breakpoint-" : "--bs-breakpoint-"
        for (const breakpointName of this.breakpointNames) {
            const value = window.getComputedStyle(document.documentElement).getPropertyValue(prefix + breakpointName)
            if (value) {
                this.breakpointValues[breakpointName] = value
            }
        }
    }
    let i = this.breakpointNames.length
    for (const breakpointName of this.breakpointNames) {
        i--
        if (window.matchMedia("(min-width: " + this.breakpointValues[breakpointName] + ")").matches) {
            return { name: breakpointName, index: i }
        }
    }
    return null
}
function sendIframeSize(modifier) {
    
    var modifier = modifier || 0
    var bodyHeight = 0
    if ($('body').attr('data-sizewanted-modelopen') != undefined) {
        bodyHeight = parseInt($('body').attr('data-sizewanted-modelopen'))
    } else if ($('body').attr('data-sizewanted') != undefined) {
        bodyHeight = parseInt($('body').attr('data-sizewanted'))
    } else {
        bodyHeight = ($('body').outerHeight(true) + modifier)
    }
    //console.log("sendIframeSize")
    //console.log(bodyHeight)
    if (bodyHeight > 0) {
        window.parent.postMessage({
            "selector": htmlSelector,
            "action": "resize",
            "height": bodyHeight
        }, "*")
    }
}
function initChangeModalAndCollapse() {
    $('.collapse').off('show.bs.collapse').on('show.bs.collapse', function (e) {
        var sizeWanted = 0
        if ($(this).closest('.modal').length > 0) {
            //si la collapse est dans une modal
            var modalContainer = $(this).closest('.modal')
            pushAttrModalHeightToBody(modalContainer, $(e.target).height())
        } else {
            //si la collapse est dans le body
            sizeWanted = $('body').outerHeight(true) + $(e.target).height()
            $('body').attr('data-sizewanted', sizeWanted)
        }
        sendIframeSize()
    })
    $('.collapse').off('shown.bs.collapse').on('shown.bs.collapse', function (e) {
        $('body').removeAttr('data-sizewanted')
        sendIframeSize()
    })
    $('.collapse').off('hidden.bs.collapse').on('hidden.bs.collapse', function (e) {
        if ($(this).closest('.modal').length > 0) {
            //si la collapse était dans une modal
            var modalContainer = $(this).closest('.modal')
            pushAttrModalHeightToBody(modalContainer, 0, true)

        }
        sendIframeSize()
    })
    $('.modal').off('shown.bs.modal').on('shown.bs.modal', function (e) {
        sendIframeSize()
    })
    $('.modal').off('show.bs.modal').on('show.bs.modal', function (e) {
        $(this).css({ 'display': 'block' })
        pushAttrModalHeightToBody(this)
        $(this).css({ 'display': 'none' })
        sendIframeSize()
    })
    $('.modal').off('hide.bs.modal').on('hide.bs.modal', function (e) {
        $('body').removeAttr('data-sizewanted-modelopen')
    })
    $('.modal').off('hidden.bs.modal').on('hidden.bs.modal', function (e) {
        sendIframeSize()
    })

    $('#modalCGV').off('show.bs.modal').on('show.bs.modal', function (e) {
        var urltoload = e.relatedTarget.attr('data-url');
        var thismodal = this;
        $(this).find('.modal-body').html('<iframe src="' + urltoload + '" id="iframeSeeCGV" width="100%" height="100%" frameborder="0"></iframe>')
        $('#iframeSeeCGV')[0].removeEventListener('load', function () {})
        $('#iframeSeeCGV')[0].addEventListener('load', function () {
            if (this.contentDocument != null && this.contentDocument.body.scrollHeight > 0) {
                //si on est sur le mm domain
                this.style.height = this.contentDocument.body.scrollHeight + 'px';
            } else {
                //si on est crossdomain
                this.style.height = '600px';
            }
            /* meme contenu que le show.bd.modal generic plus haut */
            if (!$(thismodal).is(':visible')) {
                $(thismodal).css({ 'display': 'block' })
                pushAttrModalHeightToBody(thismodal)
                $(thismodal).css({ 'display': 'none' })
                sendIframeSize()
            } else {
                pushAttrModalHeightToBody(thismodal)
                sendIframeSize()
            }
           
        });
        $(this).css({ 'display': 'block' })
        pushAttrModalHeightToBody(this)
        $(this).css({ 'display': 'none' })
        sendIframeSize()
    })
}

//calcule la taille d'une modal 
function pushAttrModalHeightToBody(modal, modifier, forced) {
    var forced = (forced == true) ? true : false;
    var modifier = modifier || 0
    var modalMarginTopBottom = parseFloat($(modal).find('.modal-dialog').css("margin-top")) + parseFloat($(modal).find('.modal-dialog').css("margin-bottom"))
    var modalBorderTopBottom = parseFloat($(modal).find('.modal-content').css('border-top-width')) + parseFloat($(modal).find('.modal-content').css('border-bottom-width'))
    var modalSizeWanted = ($(modal).find('.modal-content').height() + modalMarginTopBottom + modalBorderTopBottom) + modifier
    if (modalSizeWanted > window.document.body.scrollHeight) {
        $('body').attr('data-sizewanted-modelopen', Math.ceil(modalSizeWanted))
    } else if (forced) {
        $('body').attr('data-sizewanted-modelopen', window.document.body.scrollHeight)
    }
}
//serialize foorm obj
$.fn.serializeObject = function() {
  var o = {};
  var a = this.serializeArray();
  $.each(a, function() {
    if (o[this.name]) {
      if (!o[this.name].push) {
        o[this.name] = [o[this.name]];
      }
      o[this.name].push(this.value || '');
    } else {
      o[this.name] = this.value || '';
    }
  });
  return o;
};

function overlayLoadingOn() {
  $('#overlayLoading').show()
}

function overlayLoadingOff() {
  $('#overlayLoading').hide()
}


function loadingButtonOn(target) {
    var loadinghtml = $("#overlayLoading").html()
    if ($(target).find('#loadingcircle').length == 0) {
        $(target).addClass("disabled").prop('disabled', true)
        $(target).prepend(loadinghtml)
    }
}

function loadingButtonOff(target) {
    $(target).removeClass("disabled").prop('disabled', false)
    $(target).find('#loadingcircle').remove()
}

function loadingButtonBootstrapOn(target) {
    var loadinghtml = $("#hidden-template-overlayLoadingBootstrapBtn").html()
    if ($(target).find('.spinner-border').length == 0) {
        $(target).addClass("disabled").prop('disabled', true)
        $(target).prepend(loadinghtml)
    }
}

function loadingButtonBootstrapOff(target) {
    $(target).removeClass("disabled").prop('disabled', false)
    $(target).find('.spinner-border').remove()
}

function loadingBootstrapOff(target) {
    $(target).find('.spinner-border-wrapper').remove()
}

function loadingBootstrapOn(target) {
    var loadinghtml = $("#hidden-template-overlayLoadingBootstrap").html()
    if ($(target).find('.spinner-border-wrapper').length == 0) {
        $(target).prepend(loadinghtml)
    }
}

function GetTranslationTerm(translations, term) {
  var translations = (translations != null) ? translations : []
  arr = jQuery.grep(translations, function(x) {
    return (x.translationKey == term);
  });
  if (arr.length > 0) {
    return arr[0].translationValue;
  } else {
    return ""
  }

}

//sort by
function predicate() {
    var fields = [],
        n_fields = arguments.length,
        field, name, reverse, cmp;

    var default_cmp = function (a, b) {
        if (a === b) return 0;
        return a < b ? -1 : 1;
    },
        getCmpFunc = function (primer, reverse) {
            var dfc = default_cmp,
                // closer in scope
                cmp = default_cmp;
            if (primer) {
                cmp = function (a, b) {
                    return dfc(primer(a), primer(b));
                };
            }
            if (reverse) {
                return function (a, b) {
                    return -1 * cmp(a, b);
                };
            }
            return cmp;
        };

    // preprocess sorting options
    for (var i = 0; i < n_fields; i++) {
        field = arguments[i];
        if (typeof field === 'string') {
            name = field;
            cmp = default_cmp;
        } else {
            name = field.name;
            cmp = getCmpFunc(field.primer, field.reverse);
        }
        fields.push({
            name: name,
            cmp: cmp
        });
    }

    // final comparison function
    return function (A, B) {
        var a, b, name, result;
        for (var i = 0; i < n_fields; i++) {
            result = 0;
            field = fields[i];
            name = field.name;

            result = field.cmp(A[name], B[name]);
            if (result !== 0) break;
        }
        return result;
    };
}

// unique array
function unique(list) {
    var result = [];
    $.each(list, function (i, e) {
        if ($.inArray(e, result) == -1) result.push(e);
    });
    return result;
}

//formate les cents en devise
function SetDeviseCode(montant) {
    montant = montant.toString().replace('&nbsp;', '')
    if (montant != "") {
        if (deviseCode != "") {
            if (deviseCode.before) {
                return deviseCode.code + " " + formatMoney(montant.replace(',', '.'), 2, deviseCode.separator, " ");

            } else {
                return formatMoney(montant.replace(',', '.'), 2, deviseCode.separator, " ") + " " + deviseCode.code;
            }
        } else {
            return montant;
        }
    }
}
//formate des cents (0000) vers un montant devisé (00,00 €)
function formatMoney(amount, decimalCount, decimal, thousands) {

    var decimalCount = decimalCount || 2
    var decimal = decimal || "."
    var thousands = thousands || ","

    if (!isNaN(parseFloat(amount))) {
        try {
            amount = (Number(amount / 100) || 0).toString()
            decimalCount = Math.abs(decimalCount);
            decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

            const negativeSign = amount < 0 ? "-" : "";

            let i = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
            let j = (i.length > 3) ? i.length % 3 : 0;

            return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousands) + (decimalCount ? decimal + Math.abs(amount - i).toFixed(decimalCount).slice(2) : "");
        } catch (e) {
            //console.log(e)
        }
    } else {
        return amount
    }
}

//permet de formater un integer sur autant de chiffres que l'on souhaite
//utiliser pour les structureid 0175
function numToNDigitStr(num, n) {
    if (num >= Math.pow(10, n - 1)) {
        return num;
    }
    return "0" + numToNDigitStr(num, n - 1);
}

/***************** BIG MAP *******************/
//initialise le rescale au chargement de la big map 
function initScaleAndReplaceBigMap(container, isScaled) {
    var target = $(container).find("#divPlanSalleWrapperAll")
    var parent = $(target).parent()
    var targetWidth = $(target).width()
    var parentWidth = $(parent).width()
    var targetHeight = $(target).height()
    var parentHeight = $(parent).height()
    var scaleWidth = parentWidth / targetWidth
    var scaleHeight = parentHeight / targetHeight
    var scale = scaleWidth
    if (scaleHeight < scaleWidth) {
        scale = scaleHeight
    }

    if (scale > 1) {
        scale = 1
    }

    if (isScaled) {
        translateX = (0 - (targetWidth - (targetWidth * scale)) / 2) - (((targetWidth * scale) / 2) - (parentWidth / 2))
        translateY = (0 - (targetHeight - (targetHeight * scale)) / 2) - (((targetHeight * scale) / 2) - (parentHeight / 2))
    } else {
        translateX = (0 - (targetWidth - (targetWidth * scale)) / 2) - (((targetWidth * scale) / 2) - (parentWidth / 2)) 
        translateY = (0 - (targetHeight - (targetHeight * scale)) / 2) - (((targetHeight * scale) / 2) - (parentHeight / 2))
    }

    $(target).attr('data-scale', scale).attr('data-x', translateX).attr('data-y', translateY).css('transform', 'translate(' + translateX + 'px, ' + translateY + 'px) scale(' + scale + ')')

}

//recupère le xml de la vue des sièges
function getSeatingPlans(structureId, lieuId) {
    $.ajax({
        type: "GET",
        url: BaseImagesUrlPath.replace('{structureId}', numToNDigitStr(structureId, 4))+"seatingplans/iindexToImageName" + lieuId + ".xml",
        dataType: "xml",
        success: function (xml) {
            xmlVueFromSeat = xml
        }
    });
}

function getZESCIdsFromAreaId(data, area, id) {
    var obj = {
        "zones": [],
        "zonesdispo": 0,
        "floors": [],
        "floorsdispo": 0,
        "sections": [],
        "sectionsdispo": 0,
        "categs": [],
        "categsdispo": 0,
    }
    $.each(data.listZones, function (zi, zk) {
        var zonefound = false;
        if (area == "zone") {
            if (zk.zoneId == id) {
                zonefound = true;
            }
        } else {
            $.each(zk.listFloors, function (fi, fk) {
                var floorfound = false;
                if (area == "floor") {
                    if (fk.floorId == id) {
                        zonefound = true;
                        floorfound = true;
                    }
                } else {
                    $.each(fk.listSections, function (si, sk) {
                        var sectionfound = false;
                        if (area == "section") {
                            if (sk.sectionId == id) {
                                zonefound = true;
                                floorfound = true;
                                sectionfound = true;
                            }
                        } else {
                            $.each(sk.listCategories, function (ci, ck) {
                                var categfound = false;
                                if (area == "categ") {
                                    if (ck.categId == id) {
                                        zonefound = true;
                                        floorfound = true;
                                        sectionfound = true;
                                        categfound = true;
                                    }
                                } 
                                if (categfound) {
                                    if ($.inArray(ck.categId, obj["categs"]) == -1) {
                                        obj["categs"].push(ck.categId);
                                        obj["categsdispo"] += ck.dispo
                                    }
                                    
                                }
                            })
                        }
                        if (sectionfound) {
                            if ($.inArray(sk.sectionId, obj["sections"]) == -1) {
                                obj["sections"].push(sk.sectionId);
                                obj["sectionsdispo"] += sk.dispo
                            }
                        }
                    })
                }
                if (floorfound) {
                    if ($.inArray(fk.floorId, obj["floors"]) == -1) {
                        obj["floors"].push(fk.floorId);
                        obj["floorsdispo"] += fk.dispo
                    }
                }

            })
        }
        if (zonefound) {
            if ($.inArray(zk.zoneId, obj["zones"]) == -1) {
                obj["zones"].push(zk.zoneId);
                obj["zonesdispo"] += zk.dispo
            }
        }
    })
    
    return obj;
}
//recupère tout le chemin de l'objet jusqu'au noeud sélectionné  zone / étage / section
//area : "zone", "floor", "section", "category"
//id : id de l'area sélectioné
function getSessionObjectFromAreaId(data, area, id) {
    var found = false
    var obj = $.extend(true, {}, data);
    $.each(data.session.listZones, function (zi, zk) {
        if (!found) {
            if (area == "zone") {
                if (sk.zoneId == parseInt(id)) {
                    obj.session.listZones = $.extend(true, {}, zk);
                    found = true
                    return false;
                } else {
                    obj.session.listZones = null
                }
            } else {
                obj.session.listZones = $.extend(true, {}, zk);
                $.each(zk.listFloors, function (fi, fk) {
                    if (!found) {
                        if (area == "floor") {
                            if (fk.floorId == parseInt(id)) {
                                obj.session.listZones.listFloors = $.extend(true, {}, fk);
                                found = true
                                return false;
                            } else {
                                obj.session.listZones.listFloors = null
                            }
                        } else {
                            obj.session.listZones.listFloors = $.extend(true, {}, fk);
                            $.each(fk.listSections, function (si, sk) {
                                if (!found) {
                                    if (area == "section") {
                                        if (sk.sectionId == parseInt(id)) {
                                            obj.session.listZones.listFloors.listSections = $.extend(true, {}, sk);
                                            found = true
                                            return false;
                                        } else {
                                            obj.session.listZones.listFloors.listSections = null
                                        }
                                    } else {
                                        obj.session.listZones.listFloors.listSections = $.extend(true, {}, sk);
                                        $.each(sk.listCategories, function (ci, ck) {
                                            if (!found) {
                                                if (area == "category") {
                                                    if (ck.categId == parseInt(id)) {
                                                        obj.session.listZones.listFloors.listSections.listCategories = $.extend(true, {}, ck);
                                                        found = true
                                                        return false;
                                                    } else {
                                                        obj.session.listZones.listFloors.listSections.listCategories[ci] = null
                                                    }
                                                } else {
                                                    obj.session.listZones.listFloors.listSections.listCategories = $.extend(true, {}, ck);
                                                }
                                            }
                                        })
                                    }
                                }
                            })
                        }
                    }
                })
            }
        }
    })
    return obj;
}

function findNestedObj(entireObj, keyToFind, valToFind) {
    let foundObj = [];
    JSON.stringify(entireObj, (_, nestedValue) => {
        if (nestedValue && nestedValue[keyToFind] === valToFind) {
            foundObj.push(nestedValue);
        }
        return nestedValue;
    });
    return foundObj;
};
