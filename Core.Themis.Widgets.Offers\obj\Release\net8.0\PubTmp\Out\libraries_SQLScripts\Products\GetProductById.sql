/*
DECLARE @pLangCode VARCHAR(2)
set @pLangCode = 'fr'
DECLARE @pProductId int
set @pProductId = 5650001
*/

DECLARE @langue_id int
SELECT @langue_id = langue_id FROM langue WHERE langue_code = @pLangCode

IF @langue_id IS NULL
	SET @langue_id = 0


select * FROM produit p
LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @langue_id
where p.internet = 1 and p.produit_id = @pProductId 
