﻿// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
function lessReady() {
    $(window).resize()
    console.log('homemodular.js lessReady READY')
}

$(window).resize(function () {
    sendIframeSize()
    currentBreakpoint = bootstrapDetectBreakpoint()
    updateHomeModularCarouselHeight()
});

$(document).ready(function () {
    console.log('homemodular.js READY')
    bindClickDataHref()
    $(window).resize()
});

function getCarouselMinDataCols(carousel, breakpoint) {
    var breakpointIdx = breakpoint.index;
    var breakpointsnames = bootstrapBreakpointNames().reverse()
    var breakpointsnamesLength = breakpointsnames.length
    var output = null;
    for (i = breakpoint.index; i >= 0; i--) {
        if ($(carousel).attr('data-cols-' + breakpointsnames[i]) != undefined) {
            output = "-" + breakpointsnames[i]
            return output
        }
    }
    return "";
}

function updateHomeModularCarouselHeight() {
    $.each($('.carousel'), function (i, k) {
        var cols;
        datacolsuffix = getCarouselMinDataCols($(k), currentBreakpoint);
        if ($(k).attr('data-cols' + datacolsuffix) != undefined) {
            cols = parseInt($(k).attr('data-cols' + datacolsuffix))
        }
        if ($(k).find('.carousel-item').length <= cols) {
            $(k).find('.carousel-inner').css({ "justify-content": "center" })
            $(k).carousel('dispose')
            $(k).find('.carousel-control-prev , .carousel-control-next').hide()
        } else {
            $(k).find('.carousel-inner').css({ "justify-content": "" })
            $(k).carousel({ keyboard: false, interval: 4000, pause: false })
            $(k).carousel('cycle')
            $(k).find('.carousel-control-prev , .carousel-control-next').show()
        }
        var itemmargin = ((parseInt($(k).find('.carousel-item').last().css('marginLeft')) + parseInt($(k).find('.carousel-item').last().css('marginRight')))) || 0;
        var flexdecal = itemmargin || 0;
        $(k).find('.carousel-item').css({ flex: "0 0  calc(" + (100 / cols) + "% - " + flexdecal + "px )" })
    })

    $('.EventFeaturedCarousel, .ProductFeaturedCarousel').off('slide.bs.carousel').on('slide.bs.carousel', function (e) {
        sendIframeSize()
        var $e = $(e.relatedTarget);
        var $carousel = $(e.currentTarget)
        var cols;
        var itemsPerSlide;
        datacolsuffix = getCarouselMinDataCols($carousel, currentBreakpoint);
        if ($carousel.attr('data-cols' + datacolsuffix) != undefined) {
            cols = parseInt($carousel.attr('data-cols' + datacolsuffix))
            itemsPerSlide = parseInt($carousel.attr('data-cols' + datacolsuffix));
        }
        if (cols != undefined) {

            if (e.direction == "left") {
                var itemmargin = parseInt($carousel.find('.carousel-item').first().css('marginLeft')) || 0;
                $cloned = $carousel.find('.carousel-item').first().clone()
                $carousel.find('.carousel-inner').append($cloned)
                $carousel.find('.carousel-item').first().css({ "margin-left": "calc(" + -(100 / itemsPerSlide) + "% + " + itemmargin + "px)" })
            } else {
                var itemmargin = parseInt($carousel.find('.carousel-item').last().css('marginLeft')) || 0;
                $cloned = $carousel.find('.carousel-item').last().clone()
                $cloned.css({ "margin-left": "calc(" + -(100 / itemsPerSlide) + "% + " + itemmargin + "px)" })
                $carousel.find('.carousel-inner').prepend($cloned)
                setTimeout(function () { $carousel.find('.carousel-item').first().css({ "margin-left": "" }) }, 1);
            }
            bindClickDataHref()
        }
    })

    $('.EventFeaturedCarousel, .ProductFeaturedCarousel').off('slid.bs.carousel').on('slid.bs.carousel', function (e) {
        var $carousel = $(e.currentTarget)
        sendIframeSize()
        var cols;
        datacolsuffix = getCarouselMinDataCols($carousel, currentBreakpoint);
        if ($carousel.attr('data-cols' + datacolsuffix) != undefined) {
            cols = parseInt($carousel.attr('data-cols' + datacolsuffix))
        }
        if (cols != undefined) {
            if (e.direction == "left") {
                $carousel.find('.carousel-item').first().remove()
            } else {
                $carousel.find('.carousel-item').last().remove()
            }
            $carousel.find('.carousel-item:first').addClass('active')
            $carousel.find('.carousel-item:not(:first)').removeClass('active')
        }
    })
}


function bindClickDataHref() {
    $("[data-href]:not([data-href='']").off('click').on('click', function () {
        var goToUrl = $(this).attr('data-href')
        var msg = {
            "action": "urltogo",
            "url": goToUrl
        }
        window.parent.postMessage(msg, '*')
    })
}