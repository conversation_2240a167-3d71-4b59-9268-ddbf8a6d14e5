
<body>
	<hr>
	<div id="testInsertSeatSelection">insert seat selection</div>	
	<hr>

	<script src="https://code.jquery.com/jquery-3.5.0.js"></script>	
	<!--- CS : -->
	<!--<script src='https://localhost:44310/widget-js'></script> 
	-->
	<script src='https://localhost:44310/widget-js'></script> 
	<script>
		$(document).ready(function () {
			var queryString = window.location.search;
			var urlParams = new URLSearchParams(queryString);
			var identityid = urlParams.get('identityid')
			var buyerprofilid = urlParams.get('buyerprofilid')
			var structureid = urlParams.get('structureid')
			var webUserId = urlParams.get('webuserid')
			var eventid = urlParams.get('eventid')
			var ForceDate = urlParams.get('yymm')
			var ForceSession = urlParams.get('sessionid')
			var langCode = urlParams.get('lang')
			initSeatsSelection(structureid, identityid, buyerprofilid, webUserId, eventid, ForceDate, ForceSession, langCode);
		});
		function initSeatsSelection(structureid, identityid, buyerprofilid, webUserId, eventid, ForceDate, ForceSession, langCode) {
			var langCode = langCode || "fr"
			var webUserId = webUserId || 0;
			var ForceDate = ForceDate || '0';
			var ForceSession = ForceSession || 491;
			var structureid = structureid || 994; //si vous voulez changer de structure id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?structureid=991 
			var eventid = eventid || 32; //si vous voulez changer de event id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?eventid=60
			var identityid = identityid || 0; //si vous voulez changer de identityid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?identityid=473 
			var buyerprofilid = buyerprofilid || 0; //si vous voulez changer de buyerprofilid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?buyerprofilid=789 
		// call widget offre core
		SphereOffers.Session({
			selector: '#testInsertSeatSelection',
			partnerName: "WEBSITEEXTERNE" ,
			structureId: structureid,
			langCode: langCode,
			eventId:eventid,
			identityId: identityid,
			webUserId: webUserId,
			forceDate : ForceDate,
			forceSession : ForceSession,
			buyerProfilId: buyerprofilid,
			//signature: "CIZIT64et7R67PZGLvyxpgAruU8cttRh3M8kOZkroP0="
			signature: "+BsNFj2XMpx9erQNetGPU+i9TERvzW2c1jGLNFSF6rI="
		})      
	}
</script>
</body>
