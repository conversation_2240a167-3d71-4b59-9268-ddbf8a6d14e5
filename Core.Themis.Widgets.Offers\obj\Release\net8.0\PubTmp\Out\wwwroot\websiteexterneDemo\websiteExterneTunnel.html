﻿<head>
    <link rel="stylesheet" href="websiteexterne.css">


    <script src="websiteexterne.js"></script>

</head>

<body>

    <header>
        <div class="header-banner">
            <h1>Visit src='https://dev2.themisweb.fr/widgets/offers/sandbox_pga/widget-js'</script> </h1>
        </div>
        <div class="clear"></div>
        <nav>
            <div class="site-title">Finland</div>
            <ul>
                <li><a href="/archive">Archive</a></li>
                <li><a href="/events">Events</a></li>
                <li><a href="/contact">Contact</a></li>
                <ul>
        </nav>
    </header>





    <section class="content">
        <article>
            <p>Wolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn iPhone Helvetica kitsch Godard, XOXO blog aesthetic beard quinoa. Fixie kale chips PBR&B twee, YOLO raw denim before they sold out photo booth bespoke seitan food truck. Ethical chia before they sold out, trust fund viral ennui you probably haven't heard of them cred bitters mixtape semiotics deep v jean shorts pork belly occupy shabby chic sriracha ethnic normcore bicycle rights single-origin coffee slow-carb jean shorts. Twee ethnic mumblecore, Carles banh mi slow-carb pour-over organic. Ethical tofu narwhal, hoodie viral ennui tousled paleo. Butcher chia cray iPhone keytar, sustainable Cosby sweater literally try-hard put a bird on it photo booth ethical street art literally semiotics.</p>
            <p>	<div id="RodrigueWidgetsPlace">insert seat selection</div>	</p>
            <p>Twee 8-bit Blue Bottle, wolf tattooed distillery retro dreamcatcher put a bird on it letterpress asymmetrical actually Austin crucifix cred. Selfies ethical butcher vegan, umami bitters literally wolf seitan +1 Intelligentsia Pitchfork. Godard mixtape pork belly Pitchfork, fap food truck wolf banh mi post-ironic. Narwhal letterpress Etsy direct trade irony pour-over. Cosby sweater literally Vice DIY butcher selvage shabby chic, XOXO hashtag letterpress single-origin coffee. Artisan Portland disrupt swag small batch chambray, Neutra bespoke Wes Anderson tofu VHS ennui. Cosby sweater letterpress 90's shabby chic cornhole, literally YOLO sartorial bitters Blue Bottle cardigan chambray asymmetrical.</p>
            <p>Fixie gluten-free sriracha flannel, selfies chambray direct trade. Authentic mixtape semiotics deep v jean shorts pork belly occupy shabby chic sriracha. Tousled Williamsburg fanny pack High Life shabby chic Cosby sweater, gastropub organic cornhole post-ironic hella drinking vinegar cliche wolf pork belly. Wolf selvage cornhole church-key, ethnic raw denim chillwave authentic asymmetrical Austin synth. Single-origin coffee iPhone Carles, Godard synth chia photo booth fap mustache authentic pickled direct trade. Literally Vice Echo Park, trust fund viral ennui you probably haven't heard of them cred biodiesel lomo scenester gastropub chia keffiyeh Intelligentsia. Blue Bottle blog asymmetrical, pickled XOXO sustainable twee seitan cornhole 90's.</p>
            <p>Flexitarian bitters cardigan tofu tattooed, Kickstarter Thundercats umami hella raw denim mumblecore stumptown Godard trust fund. Pop-up organic iPhone, Godard actually vegan fanny pack Intelligentsia sartorial. Chambray Echo Park you probably haven't heard of them, food truck Wes Anderson Brooklyn brunch. Scenester Austin street art, sartorial sriracha letterpress hashtag. Wayfarers vinyl single-origin coffee, fashion axe pork belly hoodie polaroid mumblecore street art Truffaut. Readymade ethical Carles American Apparel fanny pack twee. Normcore Etsy squid, farm-to-table XOXO forage selfies distillery photo booth Tonx.</p>
            <p>Mlkshk flexitarian ethical, narwhal forage bespoke artisan hella plaid vinyl try-hard Tonx actually keytar. McSweeney's chambray forage next level, mixtape Austin Portland Odd Future ethical seitan. Gluten-free roof party locavore, pop-up cornhole chambray mlkshk bitters McSweeney's Kickstarter bespoke. YOLO cred pop-up chambray PBR&B. Kitsch mixtape DIY skateboard leggings, YOLO ennui chillwave dreamcatcher put a bird on it. Pug artisan PBR&B PBR dreamcatcher, Marfa Etsy mixtape mustache messenger bag Vice. Plaid lomo pour-over post-ironic, synth disrupt beard Truffaut church-key Tumblr narwhal Bushwick cred slow-carb tofu.</p>
            <p>Pitchfork disrupt Vice fixie sriracha Blue Bottle. Carles gastropub skateboard Schlitz, Truffaut semiotics PBR fanny pack. Truffaut biodiesel aesthetic, skateboard Etsy distillery blog. Craft beer mumblecore bitters, Tonx fixie viral fingerstache Bushwick occupy. DIY tofu pug lo-fi, street art flexitarian Truffaut. 90's banjo lo-fi master cleanse selvage Tonx, narwhal tote bag. Helvetica before they sold out put a bird on it slow-carb normcore lo-fi.</p>
        </article>

        <aside>
            Wid Signature :<p style="font-family:'Courier New', monospace;" id="Widgetsignat"></p>
            Api Signature :<p style="font-family:'Courier New', monospace;" id="APIsignat"></p>
            Api Token :<p style="font-family:'Courier New', monospace;" id="APItoken"></p>
            Web User :<p style="font-family:'Courier New', monospace;" id="WebUserId"></p>

            Basket Id :<p style="font-family:'Courier New', monospace;" id="BasketId"></p>
            Basket Hash :<p style="font-family:'Courier New', monospace;" id="BasketHash"></p>

            Identite Id :<p style="font-family:'Courier New', monospace;" id="IdentiteId"></p>
            Identite Hash :<p style="font-family:'Courier New', monospace;" id="IdentiteHash"></p>


            <img width="200" src="https://www.nordic.be/wp-content/uploads/2020/04/Finland-Samaai-meren-%C2%A9-Fotolia-2.jpg">
            <img width="200" src="https://storage-prtl-co.imgix.net/endor/articles/2959/images/1722254319_shutterstock_1680187504.jpg?max-w=660&max-h=532&fit=crop&auto=format,compress&q=40">
        </aside>
    </section>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js">
    </script>
    <script>

        // Key is UTF8 encoded 64 bytes -> no padding / no hashing required
        const key = ",@8gaG:=4H4ps858k._ADBR9d"
        //console.log(window.location.origin)
        const mess = window.location.origin + "/$POST";
        const partnerName = "WEBSITEEXTERNE"
        const structureId = 994


        const sha256digest = CryptoJS.HmacSHA256(mess, key);
        var base64sha256 = CryptoJS.enc.Base64.stringify(sha256digest);
        var myCalculatedWidgetSignature = base64sha256
        document.getElementById("Widgetsignat").innerHTML = myCalculatedWidgetSignature;

        const apiauthDmon = "https://development.rodrigue-ws.com/v1/authentication/api/"

        //const apiauthDmon = "https://localhost:7022/api/"
        const apiauthUrl = apiauthDmon + structureId + "/token/" + partnerName

        //const apiExternalDmon = "https://localhost:7265/api/"
        const apiExternalDmon = "https://development.rodrigue-ws.com/v1/external/api/"


        const apiExternalUrl = apiExternalDmon + structureId + "/WebUser"

        var toEncode = "GET " + apiauthUrl
        const sha256digestapi = CryptoJS.HmacSHA256(toEncode, key);
        var base64sha256api = CryptoJS.enc.Base64.stringify(sha256digestapi);
        var myCalculatedApiSignature = base64sha256api
        document.getElementById("APIsignat").innerHTML = myCalculatedApiSignature;

        var tokenApi = "";

        //

        var getApiToken = function (endpointAuthent) {
            var settings = {
                "url": endpointAuthent,
                "method": "GET",
                "timeout": 0,
                "headers": {
                    "Signature": myCalculatedApiSignature
                },
            };
            return $.ajax(settings);
        }

        function createWebUser(resultToken) {


            document.getElementById("APItoken").innerHTML = resultToken.authenticationToken;

            var settings = {
                "url": apiExternalUrl,
                "method": "POST",
                "timeout": 0,
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + resultToken.authenticationToken
                },
                "data": JSON.stringify({
                    "referer": "myreferer",
                    "addressIp": "myaddressIp",
                    "browser": "postMan"
                }),
            };

            var ret = $.ajax(settings);

            return ret;

        }

        function callTunnel(resultCreateUser) {
            //alert(resultCreateUser.id);
            document.getElementById("WebUserId").innerHTML = resultCreateUser.id;

            var langCode = "fr"
            var webUserId = resultCreateUser.id
            var ForceDate = '0';
            var ForceSession = 491;
            var structureid = 994; //si vous voulez changer de structure id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?structureid=991
            var eventid = 32; //si vous voulez changer de event id vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?eventid=60
            var identityid = 0; //si vous voulez changer de identityid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?identityid=473
            var buyerprofilid = 0; //si vous voulez changer de buyerprofilid vous pouvez le faire a la volée !!! https://dev.themisweb.fr/indiv/testingCalloffersLocal.html?buyerprofilid=789
            var mysettings = '{"global":{"globalFontUrl":"https://fonts.googleapis.com/css?family=Iceberg:100,300,400,700,900", "globalFontName": "Iceberg", "globalBackgroundColor":"#DAF7A6", "secondaryColor": "#F1DB4B", "primaryColor": "#FF5733" }}';


            // call widget offre core


            SphereOffers.iTunnel({
                selector: '#RodrigueWidgetsPlace',
                partnerName: partnerName,
                structureId: structureid,
                langCode: langCode,
                eventId: eventid,
                identityId: identityid,
                webUserId: resultCreateUser.id,
                forceDate: ForceDate,
                forceSession: ForceSession,
                buyerProfilId: buyerprofilid,
                mySettings: mysettings,
                signature: myCalculatedWidgetSignature
            })
        }






    </script>


    <script src="https://code.jquery.com/jquery-3.5.0.js"></script>
    <!--- CS : -->
    <!--<script src='https://localhost:44310/widget-js'></script> 		<add key="widgetOfferUrl" value="https://dev2.themisweb.fr/widgets/offers/v1/widget-js"/>
    -->
    <script src='https://dev2.themisweb.fr/widgets/offers/sandbox_pga/widget-js'></script>


    <script>
        $(document).ready(function () {

            // obtenir un token
            getApiToken(apiauthUrl)
                .then(createWebUser)
                .then(callTunnel);


            //initSeatsSelection(structureid, identityid, buyerprofilid, webUserId, eventid, ForceDate, ForceSession, langCode);

        });





    </script>

    <script>

        $(document).ready(function () {
            window.addEventListener('message', iframeListener);
        })

        /******** Écoute des iframes ******/
        function iframeListener(event) {
            switch (event.data.action) {
                //redirection via url
                case ("urltogo"):
                    alert('redirect!');
                    window.location = event.data.url;
                    break;
                //ajout au panier
                case ("addBasket"):

                    ///// call basket widget
                    //var myurl = UrlBasket.replace("[BasketId]", event.data.basketId);
                    var basketId = event.data.basketId;
                    var basketHash = event.data.hash;

                    document.getElementById("BasketId").innerHTML = basketId;
                    document.getElementById("BasketHash").innerHTML = basketHash;

                    break;

                case "loginOk":

                    var identiteId = event.data.identiteId;
                    var identiteHash = event.data.hashKey;

                    document.getElementById("IdentiteId").innerHTML = identiteId;
                    document.getElementById("IdentiteHash").innerHTML = identiteHash;

                    break;



            }
        }


    </script>


</body>