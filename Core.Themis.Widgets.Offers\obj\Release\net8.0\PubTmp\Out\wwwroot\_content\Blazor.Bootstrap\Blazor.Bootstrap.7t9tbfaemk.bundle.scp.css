/* _content/Blazor.Bootstrap/Components/Callout/Callout.razor.rz.scp.css */
.bb-callout[b-5p9xdwo7e3] {
    --bb-callout-color: var(--bs-secondary-text-emphasis);
    --bb-callout-bg: var(--bs-secondary-bg-subtle);
    --bb-callout-border: var(--bs-secondary-border-subtle);
    --bs-code-color: var(--bb-callout-code-color);
    --bs-link-color-rgb: var(--bb-callout-link);
    padding: 1.25rem;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    color: var(--bb-callout-color, inherit);
    background-color: var(--bb-callout-bg, var(--bs-gray-100));
    border-left: 0.25rem solid var(--bb-callout-border, var(--bs-gray-300))
}

.bb-callout h4[b-5p9xdwo7e3] {
    margin-bottom: .25rem;
}

.bb-callout > :last-child[b-5p9xdwo7e3] {
    margin-bottom: 0 !important;
}

.bb-callout + .bb-callout[b-5p9xdwo7e3] {
    margin-top: -.25rem;
}

.bb-callout .highlight[b-5p9xdwo7e3] {
    background-color: rgba(0,0,0,0.05)
}

.bb-callout-info[b-5p9xdwo7e3] {
    --bb-callout-color: var(--bs-info-text-emphasis);
    --bb-callout-bg: var(--bs-info-bg-subtle);
    --bb-callout-border: var(--bs-info-border-subtle)
}

.bb-callout-warning[b-5p9xdwo7e3] {
    --bb-callout-color: var(--bs-warning-text-emphasis);
    --bb-callout-bg: var(--bs-warning-bg-subtle);
    --bb-callout-border: var(--bs-warning-border-subtle)
}

.bb-callout-danger[b-5p9xdwo7e3] {
    --bb-callout-color: var(--bs-danger-text-emphasis);
    --bb-callout-bg: var(--bs-danger-bg-subtle);
    --bb-callout-border: var(--bs-danger-border-subtle)
}

.bb-callout-success[b-5p9xdwo7e3] {
    --bb-callout-color: var(--bs-success-text-emphasis);
    --bb-callout-bg: var(--bs-success-bg-subtle);
    --bb-callout-border: var(--bs-success-border-subtle)
}
/* _content/Blazor.Bootstrap/Components/Form/AutoComplete/AutoComplete.razor.rz.scp.css */
.autocomplete[b-zkhsnlgtvk] {
}

    .autocomplete > .input-group > input[b-zkhsnlgtvk] {
        border-top-right-radius: 0.25rem !important;
        border-bottom-right-radius: 0.25rem !important;
        padding-right: 2.75rem !important;
    }

.autocomplete-list[b-zkhsnlgtvk] {
    max-height: 200px;
    overflow-y: auto;
    width: 100%;
}

.autocomplete-item:focus[b-zkhsnlgtvk], .autocomplete-item:hover[b-zkhsnlgtvk] {
    color: #1e2125;
    background-color: #e9ecef;
    cursor: pointer;
}


.dropdown-item:focus[b-zkhsnlgtvk], .dropdown-item:hover[b-zkhsnlgtvk] {
    cursor: pointer;
}

.dropdown-item-highlight[b-zkhsnlgtvk] {
    color: #1e2125;
    background-color: #e9ecef;
    cursor: pointer;
}
/* _content/Blazor.Bootstrap/Components/Image/Image.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/Layout/BlazorBootstrapLayout.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/Ribbon/RibbonGroup.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/Ribbon/RibbonItem.razor.rz.scp.css */
.bb-ribbon-item[b-euvmgchcvy] {
    cursor: pointer;
    /*width: 64px;*/
}

    .bb-ribbon-item:hover[b-euvmgchcvy] {
        background-color: rgba(var(--bs-secondary-rgb), 0.10) !important;
    }

    .bb-ribbon-item.active[b-euvmgchcvy] {
    }
/* _content/Blazor.Bootstrap/Components/Ribbon/RibbonItemGroup.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/Sidebar2/Sidebar2.razor.rz.scp.css */
.bb-sidebar2[b-hjh7gjrlxu] {
    background-color: var(--bb-sidebar2-background-color);
    border-right: 1px solid var(--bb-sidebar2-content-border-color);
}

    .bb-sidebar2.collapsed[b-hjh7gjrlxu] {
        width: var(--bb-sidebar2-collapsed-width);
    }

        .bb-sidebar2.collapsed .expanded-only[b-hjh7gjrlxu] {
            display: none;
        }

@media (min-width: 641px) {
    .bb-sidebar2[b-hjh7gjrlxu] {
        width: var(--bb-sidebar2-width);
        position: sticky;
        top: 0;
    }
}

.bb-sidebar2-top-row[b-hjh7gjrlxu] {
    height: 3.5rem;
    background-color: var(--bb-sidebar2-top-row-background-color);
    border-bottom: 1px solid var(--bb-sidebar2-top-row-border-color);
    border-right: 1px solid var(--bb-sidebar2-top-row-border-color);
}

.navbar-toggler[b-hjh7gjrlxu] {
    background-color: var(--bb-sidebar2-navbar-toggler-background-color);
    color: rgb(var(--bb-sidebar2-nav-item-text-active-color-rgb));
    padding: inherit !important;
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
    line-height: inherit !important;
    font-size: inherit !important;
}

.navbar-toggler-icon[b-hjh7gjrlxu] {
    background-image: inherit !important;
    color: var(--bb-sidebar2-navbar-toggler-icon-color) !important;
}

.navbar-toggler:focus[b-hjh7gjrlxu] {
    box-shadow: none !important;
}

.navbar-brand[b-hjh7gjrlxu] {
    font-size: 1.1rem;
}

.navbar-brand-icon[b-hjh7gjrlxu] {
    color: var(--bb-sidebar2-brand-icon-color);
}

.navbar-brand-image[b-hjh7gjrlxu] {
    height: var(--bb-sidebar2-brand-image-height);
}

    .navbar-brand-image img[b-hjh7gjrlxu] {
        width: var(--bb-sidebar2-brand-image-width);
        height: var(--bb-sidebar2-brand-image-height);
        vertical-align: initial !important;
    }

.navbar-brand-text[b-hjh7gjrlxu] {
    color: var(--bb-sidebar2-title-text-color);
    font-weight: 600 !important;
}

.navbar-brand-badge[b-hjh7gjrlxu] {
    color: var(--bb-sidebar2-title-badge-text-color);
    background-color: var(--bb-sidebar2-title-badge-background-color);
}

@media (min-width: 641px) {
    .navbar-toggler[b-hjh7gjrlxu] {
        display: none;
    }

    .collapse[b-hjh7gjrlxu] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }

    .nav-scrollable[b-hjh7gjrlxu] {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
/* _content/Blazor.Bootstrap/Components/Sidebar2/Sidebar2Item.razor.rz.scp.css */
.bb-sidebar2.collapsed .expanded-only[b-3gpeuglz5y] {
    display: none;
}

.bb-sidebar2.collapsed .bi.expanded-only[b-3gpeuglz5y] {
    display: none !important;
}

.bb-sidebar2 .nav-link-icon[b-3gpeuglz5y] {
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
}

.bb-sidebar2 nav .nav-item[b-3gpeuglz5y]  a {
    color: var(--bb-sidebar2-nav-item-text-color);
    align-items: center;
    padding: 0.5rem 1rem;
}

    .bb-sidebar2 nav .nav-item[b-3gpeuglz5y]  a:hover {
        background-color: var(--bb-sidebar2-nav-item-background-hover-color);
        color: var(--bb-sidebar2-nav-item-text-hover-color);
    }

    .bb-sidebar2 nav .nav-item[b-3gpeuglz5y]  a.active {
        background-color: var(--bb-sidebar2-nav-item-background-hover-color);
        color: var(--bb-sidebar2-nav-item-text-active-color);
        font-weight: 600;
    }

/* MDN REFERENCE: https://developer.mozilla.org/en-US/docs/Web/CSS/:has#browser_compatibility */
.bb-sidebar2 nav .nav-item.nav-item-group:has(.nav-link.active)[b-3gpeuglz5y] {
    background-color: var(--bb-sidebar2-nav-item-group-background-color);
}

.bb-sidebar2 nav .nav-item.nav-item-group:has(.nav-item-group.active)[b-3gpeuglz5y] {
    background-color: var(--bb-sidebar2-nav-item-group-background-color);
}

.bb-sidebar2 nav .nav-item.nav-item-group.active[b-3gpeuglz5y] {
    background-color: var(--bb-sidebar2-nav-item-group-background-color);
}
/* _content/Blazor.Bootstrap/Components/Sidebar2/Sidebar2ItemGroup.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/Sidebar/Sidebar.razor.rz.scp.css */
.bb-sidebar[b-h9l5f7lx3r] {
    background-color: var(--bb-sidebar-background-color);
}

    .bb-sidebar.collapsed[b-h9l5f7lx3r] {
        width: var(--bb-sidebar-collapsed-width);
    }

        .bb-sidebar.collapsed .expanded-only[b-h9l5f7lx3r] {
            display: none;
        }

@media (min-width: 641px) {
    .bb-sidebar[b-h9l5f7lx3r] {
        width: var(--bb-sidebar-width);
        position: sticky;
        top: 0;
    }
}

.navbar-toggler[b-h9l5f7lx3r] {
    background-color: var(--bb-sidebar-navbar-toggler-background-color);
    color: rgb(var(--bb-sidebar-nav-item-text-active-color-rgb));
    padding: inherit !important;
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
    line-height: inherit !important;
    font-size: inherit !important;
}

.navbar-toggler-icon[b-h9l5f7lx3r] {
    background-image: inherit !important;
    color: var(--bb-sidebar-navbar-toggler-icon-color) !important;
}

.navbar-toggler:focus[b-h9l5f7lx3r] {
    box-shadow: none !important;
}

.bb-sidebar-top-row[b-h9l5f7lx3r] {
    height: 3.5rem;
    background-color: var(--bb-sidebar-top-row-background-color);
    border-bottom: 1px solid var(--bb-sidebar-top-row-border-color);
    border-right: 1px solid var(--bb-sidebar-top-row-border-color);
}

.bb-sidebar-content[b-h9l5f7lx3r] {
    border-right: 1px solid var(--bb-sidebar-content-border-color);
}

.navbar-brand[b-h9l5f7lx3r] {
    font-size: 1.1rem;
}

.navbar-brand-icon[b-h9l5f7lx3r] {
    color: var(--bb-sidebar-brand-icon-color);
}

.navbar-brand-image[b-h9l5f7lx3r] {
    height: var(--bb-sidebar-brand-image-height);
}

    .navbar-brand-image img[b-h9l5f7lx3r] {
        width: var(--bb-sidebar-brand-image-width);
        height: var(--bb-sidebar-brand-image-height);
        vertical-align: initial !important;
    }

.navbar-brand-text[b-h9l5f7lx3r] {
    color: var(--bb-sidebar-title-text-color);
    font-weight: 600 !important;
}

.navbar-brand-badge[b-h9l5f7lx3r] {
    color: var(--bb-sidebar-title-badge-text-color);
    background-color: var(--bb-sidebar-title-badge-background-color);
}

@media (min-width: 641px) {
    .navbar-toggler[b-h9l5f7lx3r] {
        display: none;
    }

    .collapse[b-h9l5f7lx3r] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }

    .nav-scrollable[b-h9l5f7lx3r] {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
/* _content/Blazor.Bootstrap/Components/Sidebar/SidebarItem.razor.rz.scp.css */
.bb-sidebar.collapsed .expanded-only[b-jbgvk8pcne] {
    display: none;
}

.bb-sidebar.collapsed .bi.expanded-only[b-jbgvk8pcne] {
    display: none !important;
}

.bb-sidebar .nav-link-icon[b-jbgvk8pcne] {
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
}

.bb-sidebar nav .nav-item[b-jbgvk8pcne]  a {
    color: var(--bb-sidebar-nav-item-text-color);
    align-items: center;
    padding: 0.5rem 1rem;
}

    .bb-sidebar nav .nav-item[b-jbgvk8pcne]  a:hover {
        background-color: var(--bb-sidebar-nav-item-background-hover-color);
        color: var(--bb-sidebar-nav-item-text-hover-color);
    }

    .bb-sidebar nav .nav-item[b-jbgvk8pcne]  a.active {
        background-color: var(--bb-sidebar-nav-item-background-hover-color);
        color: var(--bb-sidebar-nav-item-text-active-color);
        font-weight: 600;
    }

/* MDN REFERENCE: https://developer.mozilla.org/en-US/docs/Web/CSS/:has#browser_compatibility */
.bb-sidebar nav .nav-item.nav-item-group:has(.nav-link.active)[b-jbgvk8pcne] {
    background-color: var(--bb-sidebar-nav-item-group-background-color);
}
/* _content/Blazor.Bootstrap/Components/Sidebar/SidebarItemGroup.razor.rz.scp.css */
/* _content/Blazor.Bootstrap/Components/SortableList/SortableList.razor.rz.scp.css */
[b-jo00jd6nmv] .bb-sortable-list-handle {
    cursor: grab !important;
}
/* _content/Blazor.Bootstrap/Components/Spinner/Spinner.razor.rz.scp.css */
/* scss-docs-start spinner-dots-keyframes */
@keyframes spinner-dots-b-pp5swe6s25 {
    0% {
        opacity: 1;
    }

    50%, 100% {
        opacity: .3;
    }
}

/* scss-docs-end spinner-dots-keyframes */
.spinner-dots[b-pp5swe6s25] {
    user-select: none;
}

    .spinner-dots > circle:first-of-type[b-pp5swe6s25], 
    .spinner-dots > circle:last-of-type[b-pp5swe6s25], 
    .spinner-dots > circle:nth-of-type(2)[b-pp5swe6s25] {
        fill: currentcolor;
        animation: spinner-dots-b-pp5swe6s25 .75s infinite ease-in-out alternate;
    }

    .spinner-dots > circle:nth-of-type(2)[b-pp5swe6s25] {
        animation-delay: .25s;
    }

    .spinner-dots > circle:last-of-type[b-pp5swe6s25] {
        animation-delay: .5s;
    }

/* Size: START */

/* default */
.spinner-border-sm[b-pp5swe6s25] {
    --bs-spinner-width: 1rem;
    --bs-spinner-height: 1rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
/* custom CSS classes */
.spinner-border-md[b-pp5swe6s25] {
    --bs-spinner-width: 2rem;
    --bs-spinner-height: 2rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
.spinner-border-lg[b-pp5swe6s25] {
    --bs-spinner-width: 3rem;
    --bs-spinner-height: 3rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
.spinner-border-xl[b-pp5swe6s25] {
    --bs-spinner-width: 4rem;
    --bs-spinner-height: 4rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}

/* default */
.spinner-grow-sm[b-pp5swe6s25] {
    --bs-spinner-width: 1rem;
    --bs-spinner-height: 1rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
/* custom CSS classes */
.spinner-grow-md[b-pp5swe6s25] {
    --bs-spinner-width: 2rem;
    --bs-spinner-height: 2rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
.spinner-grow-lg[b-pp5swe6s25] {
    --bs-spinner-width: 3rem;
    --bs-spinner-height: 3rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}
.spinner-grow-xl[b-pp5swe6s25] {
    --bs-spinner-width: 4rem;
    --bs-spinner-height: 4rem;
    --bs-spinner-border-width: 0.25em;
    --bs-spinner-vertical-align: -0.125em;
}

/* Size: END */
/* _content/Blazor.Bootstrap/Components/ThemeSwitcher/ThemeSwitcher.razor.rz.scp.css */
