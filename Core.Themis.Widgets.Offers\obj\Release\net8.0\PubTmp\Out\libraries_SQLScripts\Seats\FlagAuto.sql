/* flag auto */
DECLAR<PERSON> @RC int 
DECLARE @TABLE varchar(64) 
DECLARE @SEA int 
DECLARE @CAT int 
DECLARE @RSV varchar(500) 
DECLARE @NBP int 
DECLARE @FLG varchar(10) 
DECLARE @SCTIONID int 
DECLARE @ETAGEID int 
DECLARE @ZNEID int 

declare @eventId int
SELECT @eventId = manifestation_id FROM seance WHERE seance_id = @mysessionId
declare @myTbl varchar(64)
set @myTbl = 'ENTREE_' + convert(varchar(10),@eventId )
declare @myR varchar(100)


 

DECLARE @CONTINGENTID varchar(50) 
DECLARE @ORDREPLACEMENT int 

EXECUTE @RC = [SP_WS_AUTOPLACES] 
                @TABLE=@myTbl
                ,@SEA=@mysessionId 
                ,@CAT=@mycategid 
                ,@RSV= @myListReserves
                ,@NBP=@mycount
                ,@FLAG=@myflag
                ,@SECTIONID=@mysectionId 
                ,@ETAGEID=@myFloorId 
                ,@ZONEID=@myZoneId 
                ,@CONTINGENTID=0
                ,@ORDREPLACEMENT=0 