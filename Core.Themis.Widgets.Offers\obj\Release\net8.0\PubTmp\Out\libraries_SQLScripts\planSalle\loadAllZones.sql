
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT z.lieu_id, z.zone_id,
	case when t.zone_nom is null then z.zone_nom else t.zone_nom end as zone_nom,
	z.zone_code, z.pref_affichage, z.zone_couleur_id 
FROM zone z
LEFT OUTER JOIN traduction_zone t on t.zone_id = z.zone_id and t.langue_id = @LgId
order by  z.pref_affichage, z.lieu_id

