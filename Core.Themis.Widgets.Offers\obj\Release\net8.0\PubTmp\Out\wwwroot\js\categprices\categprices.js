﻿function doFlag(structureId, eventId, sessionId, zoneId, floorId, sectionId, identityId, webUserId, buyerProfilId, token) {

    var urlFlag = "/" + structureId + "/FlagAjax/" + eventId + "/" + sessionId + "/" + zoneId + "/" + floorId + "/" + sectionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "";
    var arrayFlags = []
    //alert('doFlag !!!!!!!!!!!!!');
    $('select.select_seatscount').each(function (i, e) {
        if ($(e).find('option:selected').val() > 0) {
            var objFlag = {}
            objFlag.scount = $(e).find('option:selected').val();
            objFlag.gpid = $(e).attr('data-gpid');
            objFlag.categid = $(e).attr('data-categid');
            objFlag.priceid = $(e).attr('data-priceid');
            arrayFlags.push(objFlag)
            //alert(objFlag.gpid);
        }
        //listCategs.push($(e).attr('data-categid'));
    });
    
    if (arrayFlags.length > 0) {

        jQuery.ajax({
            type: "POST",
            url: urlFlag,
            data: {
                structureiId: structureId,
                token : token,
                toflags: arrayFlags,
            },

            success: function (data) {
                $("#wdgInsertPlanSalle").html(data);

                // charge le plan
                sendIframeSize()

            },
            error: function (a, b, c) {
                console.log("CategPriceSelectionAjax -> Error")
                jQuery("#wdgInsertPlanSalle").html(a.responseText);
            }
        });
       
    }






}