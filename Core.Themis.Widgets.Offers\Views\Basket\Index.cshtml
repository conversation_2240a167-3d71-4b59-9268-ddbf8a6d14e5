﻿@using System.Globalization;
@using Core.Themis.Libraries.DTO.InfoComps.Json
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@using Core.Themis.Widgets.Offers.Helpers
@using System.Text.RegularExpressions;
@using System.Linq;

@using Core.Themis.Libraries.Razor.Areas.CustomerArea.ViewModels
@using Core.Themis.Libraries.Razor.Areas.CustomerArea
@using Newtonsoft.Json.Linq;

@model CustomerAreaAppSettings




@*************** VARIABLES *************@
@{
    ViewBag.Title = "Panier";
    string UrlToPath = $"{@Context.Request.Scheme}://{@Context.Request.Host}{@Context.Request.PathBase}";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
    List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO> TranslationsList = ViewBag.TranslationsList as List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO>;

    string langCode = (string)ViewBag.LangCode;
}
@*************** STYLES **************@
@section styles {
    <link rel="stylesheet" type="text/css" href="@Configuration["AssetsUrlPath"]LIBS/bootstrap-select/1.14.0-beta3/bootstrap-select.min.css">
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/Basket/style.less">
}

@{
    var EventsSubTotalTTC = 0;
    var AboSubTotalTTC = 0;
    var EventsSubSpecialTotalTTC = 0;
    var ProductsSubTotalTTC = 0;
    var MoreProductsSubTotalTTC = 0;
    var FeesSubTotalTTC = 0;
    var FeesDeliverySubTotalTTC = 0;
    var lstTauxTva = new List<dynamic>();
    var AmontOrderPaid = 0;
}
<!-- recap content start -->
<div id="summaryWrapper">
    <!-- résumé du panier start -->
    <div id="summaryEventsProducts" class="boxedBlock nopaddingX nopaddingY">
        <h2 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSummary")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSummary") : "Résumé de mon panier")</h2>

        @if (ViewBag.WrongBasketLineForAdhesion != null && ViewBag.WrongBasketLineForAdhesion.Count > 0)
        {
            <!-- error : basket is modified start -->
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <p>
                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorNotFoundSponsorPanierEntrees")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorNotFoundSponsorPanierEntrees") : "Attention, Il semblerait qu'il y ait un problème avec certaines places que vous avez tenté d'ajouter à votre panier.<br/> Les places suivantes ont été retirées de votre panier :(trad)")
                </p>
                @foreach (var wrongline in ViewBag.WrongBasketLineForAdhesion)
                {
                    <div>@wrongline.EventName - @wrongline.SessionDescription - @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblRank")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblRank") : "Rang") : @wrongline.Rank, @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSeat")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSeat") : "Siège") : @wrongline.Seat - @wrongline.PriceName - <span data-pricetoformat="@wrongline.AmountTTCCents">@wrongline.AmountTTCCents</span></div>
                }
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <!-- error : basket is modified end -->
        }

        <!--------- abonnements start -->
        @if (ViewBag.Basket.ListAbonnements != null && ViewBag.Basket.ListAbonnements.Count > 0)
        {

            <div class="oneSummaryLine allAbos">
                @foreach (var aboF in ViewBag.Basket.ListAbonnements)
                {
                    <!-- one event start -->
                    <div class="oneSummaryLine oneAbo" data-eventid="@aboF.FormulaId">
                        <span class="formulaName">@aboF.FormulaName</span>

                        @foreach (var price in aboF.ListPrices)
                        {
                            <!-- one event start -->
                            <div class="oneSummaryLine onePrice" data-priceid="@price.PriceId">

                                <span class="eventName">@price.PriceName</span>

                                @foreach (var s in price.ListAboSeats)
                                {
                                    <!-- one event start -->
                                    AboSubTotalTTC += s.AmountTTCCents;
                                }

                            </div>
                        }


                    </div>
                }
                <span class="priceTotal" data-pricetocalcul="add" data-pricetoformat="@AboSubTotalTTC">@AboSubTotalTTC</span>
            </div>
        }
        <!--------- abonnements end -->
        <!-- ListEventsUnitSales start -->
        @if (ViewBag.Basket.ListEventsUnitSales != null && ViewBag.Basket.ListEventsUnitSales.Count > 0)
        {

            var listEvProds = (List<Core.Themis.Libraries.DTO.EventsSessions.EventDTO>)ViewBag.prodsByEvents;
            var listSessionsProds = (List<Core.Themis.Libraries.DTO.EventsSessions.SessionDTO>)ViewBag.prodsBySessions;
            var eventsHasTicketAmount = false;

            //en mode NON revendeur, on cherche si parmi les tarifs du pan ier, au moins 1 affiche la valeur
            if (!ViewBag.BuyerProfil.IsReseller)
            {
                foreach (var events in ViewBag.Basket.ListEventsUnitSales)
                {
                    foreach (var eventSessions in events.ListSessions)
                    {
                        foreach (var oneZone in eventSessions.ListZones)
                        {
                            foreach (var oneFloor in oneZone.ListFloors)
                            {
                                foreach (var oneSection in oneFloor.ListSections)
                                {
                                    foreach (var oneCateg in oneSection.ListCategories)
                                    {
                                        foreach (var onePrice in oneCateg.ListPrices)
                                        {
                                            if (onePrice.UnitValue > 0)
                                            {
                                                eventsHasTicketAmount = true;



                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }




            foreach (var events in ViewBag.Basket.ListEventsUnitSales)
            {
                <!-- one event start -->
                <div class="oneSummaryLine oneEvent" data-eventid="@events.EventId">
                    <span class="eventName">@events.EventName</span>
                    <!-- session wrapper start -->
                    <div class="sessionsWrapper">
                        @foreach (var eventSessions in events.ListSessions)
                        {
                            var sessionIsFeedBook = true;
                            var oneSummaryLineTotalFeesTTC = 0;
                            var oneSummaryLineTotalTTC = 0;
                            var oneSummaryLineSpecialTotalTTC = 0;
                            var showSeeMyPlacement = false;

                            var countSeat = 0;
                            var oneSummaryLineTotalHT = 0;
                            string datasSeeMyPlacement = "";

                            List<int> eventZoneIds = new List<int>();
                            List<int> eventFloorIds = new List<int>();
                            List<int> eventSectionIds = new List<int>();

                            foreach (var oneZone in eventSessions.ListZones)
                            {
                                foreach (var oneFloor in oneZone.ListFloors)
                                {
                                    foreach (var oneSection in oneFloor.ListSections)
                                    {
                                        foreach (var oneCateg in oneSection.ListCategories)
                                        {
                                            foreach (var onePrice in oneCateg.ListPrices)
                                            {
                                                foreach (var oneGp in onePrice.ListGestionPlace)
                                                {
                                                    if (oneGp.IsVoirPlace)
                                                    {
                                                        showSeeMyPlacement = true;
                                                    }
                                                }
                                                foreach (var oneSeat in onePrice.ListSeats)
                                                {
                                                    eventZoneIds.Add(oneZone.ZoneId);
                                                    eventFloorIds.Add(oneFloor.FloorId);
                                                    eventSectionIds.Add(oneSection.SectionId);

                                                    //datasSeeMyPlacement = "data-eventid=" + events.EventId + " data-sessionid=" + eventSessions.SessionId + " data-zoneid=" + oneZone.ZoneId + " data-floorid=" + oneFloor.FloorId + " data-sectionid=" + oneSection.SectionId + " data-placeid=" + eventSessions.Place.PlaceId;
                                                    int fees = 0;
                                                    int normalPrice = 0;
                                                    int specialPrice = 0;

                                                    countSeat += 1;

                                                    if (oneSeat.FeedBookToken == null)
                                                    {
                                                        sessionIsFeedBook = false;
                                                        if (onePrice.UnitValue > 0 && ViewBag.BuyerProfil.IsReseller)
                                                        {
                                                            //si la valeur est supérieur à 0 et que le profil est en mode revendeur, alors le prix principal est la valeur
                                                            normalPrice = onePrice.UnitValue;
                                                        }
                                                        else
                                                        {
                                                            //la valeur est barré
                                                            if (onePrice.UnitValue > 0)
                                                            {
                                                                specialPrice = onePrice.UnitValue;
                                                            }
                                                            else if (eventsHasTicketAmount)
                                                            {
                                                                specialPrice = onePrice.UnitTTCAmount;
                                                            }
                                                            //le prix principal est le montant normal
                                                            normalPrice = onePrice.UnitTTCAmount;

                                                        }

                                                        if (oneSeat.CurrentState == "B" || oneSeat.CurrentState == "P")
                                                        {
                                                            AmontOrderPaid += normalPrice;
                                                        }
                                                        oneSummaryLineTotalFeesTTC +=  onePrice.UnitFeeAmount;
                                                        oneSummaryLineTotalTTC += normalPrice;
                                                        oneSummaryLineSpecialTotalTTC += specialPrice;
                                                        EventsSubTotalTTC += normalPrice;
                                                        EventsSubSpecialTotalTTC += specialPrice;
                                                    }

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            <!-- session start -->
                            <div class="oneSession" data-seatscount="@countSeat" data-sessionid="@eventSessions.SessionId" data-eventid="@events.EventId" data-eventtitle="@events.EventName" data-eventstart="@eventSessions.SessionStartDate"
                                 data-eventend="@eventSessions.SessionStartDate" data-eventlieu="@eventSessions.Place.PlaceName">
                                <div class="basketDeleteSession text-danger"><i class="fa fa-trash" aria-hidden="true"></i></div>
                                <div class="row">
                                    <div class="col">
                                        <div class="collapsed" data-toggle="collapse" data-target="#collapse@(eventSessions.SessionId)" aria-expanded="false" aria-controls="collapse@(eventSessions.SessionId)">
                                            @if (eventSessions.IsShowSessionDate == true)
                                            {
                                                <div class="productSessionWrapper">
                                                    @if (eventSessions.IsShowSessionHour == true)
                                                    {
                                                        <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession">@eventSessions.SessionStartDate.ToString("f", new CultureInfo(ViewBag.LangCode))</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="productIcon"><i class="far fa-calendar-alt" aria-hidden="true"></i></span> <span class="productSession">@eventSessions.SessionStartDate.ToString("D", new CultureInfo(ViewBag.LangCode))</span>
                                                    }
                                                </div>
                                            }
                                            @if (@eventSessions.Place.PlaceName != null)
                                            {
                                                <div class="productLieuWrapper"><span class="productIcon"><i class="fas fa-map-marked-alt" aria-hidden="true"></i></span> <span class="productLieu">@eventSessions.Place.PlaceName</span></div>
                                            }
                                        </div>
                                    </div>
                                    @{
                                        
                                        //le plan est-il afficher en entier, ou juste la zone/étage/section demandée
                                        bool showTheWholePlan_SeeMyPlacement = false;
                                        if (ViewBag.SettingsMerge.basket.showTheWholePlan_SeeMyPlacement != null)
                                        {
                                            if (ViewBag.SettingsMerge.basket.showTheWholePlan_SeeMyPlacement.ContainsKey(eventSessions.Place.PlaceId.ToString()))
                                            {
                                                //si il y a une configuration pour ce lieu
                                                showTheWholePlan_SeeMyPlacement = ViewBag.SettingsMerge.basket.showTheWholePlan_SeeMyPlacement[eventSessions.Place.PlaceId.ToString()].Value;
                                            }
                                            else if (ViewBag.SettingsMerge.basket.showTheWholePlan_SeeMyPlacement.ContainsKey("default"))
                                            {
                                                //si il y a une configuration default
                                                showTheWholePlan_SeeMyPlacement = ViewBag.SettingsMerge.basket.showTheWholePlan_SeeMyPlacement["default"].Value;
                                            }
                                        }
                                        if (showTheWholePlan_SeeMyPlacement)
                                        {
                                            datasSeeMyPlacement = "data-eventid=" + events.EventId + " data-sessionid=" + eventSessions.SessionId + " data-zoneid=0 data-floorid=0 data-sectionid=0 data-placeid=" + eventSessions.Place.PlaceId;
                                        }
                                        else
                                        {
                                            datasSeeMyPlacement = "data-eventid=" + events.EventId + " data-sessionid=" + eventSessions.SessionId + " data-zoneid=" + string.Join(",", eventZoneIds.Distinct()) + " data-floorid=" + string.Join(",", eventFloorIds.Distinct()) + " data-sectionid=" + string.Join(",", eventSectionIds.Distinct()) + " data-placeid=" + eventSessions.Place.PlaceId;
                                        }

                                        oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (eventSessions.TvaTaux / 100)));
                                        lstTauxTva.Add(new { taux = eventSessions.TvaTaux, montant = (oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                    }
                                    @if (!sessionIsFeedBook)
                                    {
                                        <div class="col-auto text-right">
                                            <div class="priceTotalWrapper">
                                                @if (oneSummaryLineSpecialTotalTTC > 0)
                                                {
                                                    <s class="ticketamount" data-amount="@oneSummaryLineSpecialTotalTTC" data-pricetoformat="@oneSummaryLineSpecialTotalTTC">@Html.Raw(oneSummaryLineSpecialTotalTTC)</s>
                                                }
                                                @*<span class="priceTotalHT"><span data-pricetoformat="@oneSummaryLineTotalHT">@oneSummaryLineTotalHT</span> <span>HT (trad)</span></span>*@
                                                <span class="priceTotal" data-tvataux="@eventSessions.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetocalcul="add" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</span>
                                                @if(oneSummaryLineTotalFeesTTC > 0 && ViewBag.SettingsMerge.global.showIncludedFees == true)
                                                {
                                                                                                
                                                    string freeAmountHtml = "<span data-pricetoformat='" + oneSummaryLineTotalFeesTTC + "'>" + oneSummaryLineTotalFeesTTC + "</span>";

                                                    <br/><small class='feeamount'><em>
                                                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblIncludedFees")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblIncludedFees").Replace("{Amount}", freeAmountHtml) : "Dont " + freeAmountHtml + " de frais (trad)")
                                                    </em></small>
                                                }
                                            </div>
                                        </div>
                                    }
                                    <div class="col-12">

                                        <span class="collapeIconWrapper collapsed productDetailsCollapseBtn btn btn-sm btn-outline-secondary" data-toggle="collapse" data-target="#collapse@(eventSessions.SessionId)" aria-expanded="false" aria-controls="collapse@(eventSessions.SessionId)">
                                            <span class="productIcon"><i class="fas fa-ticket" aria-hidden="true"></i></span>
                                            <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsTickets")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsTickets") : "Détails des tickets")</span> <span class="productCount">(@countSeat)</span>
                                        </span>
                                        @if (showSeeMyPlacement)
                                        {
                                            <span class="btnSeeMyPlacement ml-3 btn btn-sm btn-secondary" @(datasSeeMyPlacement)>
                                                <i class="fas fa-eye"></i>
                                                <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSeeMyPlacement")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSeeMyPlacement") : "Voir mon placement")</span>
                                            </span>
                                        }
                                    </div>
                                </div>
                                <!-- details start -->
                                <div class="productSeatsWrapper collapse" id="collapse@(eventSessions.SessionId)">
                                    <div class="row">
                                        <div class="col">
                                            @foreach (var oneZone in eventSessions.ListZones)
                                            {
                                                foreach (var oneFloor in oneZone.ListFloors)
                                                {
                                                    foreach (var oneSection in oneFloor.ListSections)
                                                    {
                                                        foreach (var oneCateg in oneSection.ListCategories)
                                                        {
                                                            foreach (var onePrice in oneCateg.ListPrices)
                                                            {


                                                                bool isAdhesion = false;
                                                                string specialIcon = "";
                                                                foreach (var gestionPlace in onePrice.ListGestionPlace)
                                                                {
                                                                    if (gestionPlace.Offer != null)
                                                                    {
                                                                        if (gestionPlace.Offer.AdhesionCatalog != null)
                                                                        {
                                                                            isAdhesion = true;
                                                                            //specialIcon = "<i class='fas fa-bolt'></i> ";
                                                                            specialIcon = "<i class='adhesionIcon adhesionIconMedaillon'></i> ";
                                                                        }
                                                                    }
                                                                }
                                                                foreach (var oneSeat in onePrice.ListSeats)
                                                                {
                                                                    if (oneSeat.FeedBookToken != null)
                                                                    {
                                                                        specialIcon = "<i class='feedbook-icon-wrapper'><i class='fa-solid fa-ticket-simple'></i><i class='fa-solid fa-ticket-simple'></i></i> ";
                                                                    }
                                                                    <!--one seat start-->
                                                                    <div class="productDetailsWrapper" data-seatid="@oneSeat.SeatId" data-categid="@oneCateg.CategId">
                                                                        <div class="productDetails row align-items-center">
                                                                            <div class="col-12 col-md-4 text-center text-md-left">
                                                                                <span class="productSeatDetail">

                                                                                    @if (oneSeat.IsFreePlacement == true)
                                                                                    {
                                                                                        <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblFreePlacement")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblFreePlacement") : "Placement libre")</span>
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        <span>
                                                                                            <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblRank")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblRank") : "Rang")</span> @oneSeat.Rank,
                                                                                            <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSeat")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSeat") : "Siège")</span> @oneSeat.Seat
                                                                                        </span>
                                                                                    }
                                                                                </span>

                                                                                @if (ViewBag.SettingsMerge.basket.showDenomination != null && ViewBag.SettingsMerge.basket.showDenomination == "True")
                                                                                {
                                                                                    <div>
                                                                                        <strong>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDenomination") : "") @oneSeat.DenominationName</strong>
                                                                                    </div>
                                                                                }
                                                                                <span class="productSeatDetailZESC">
                                                                                    @if (!events.IsZapperZoneEtageSectionIndiv)
                                                                                    {
                                                                                        if (!events.IsZapperZoneEtageIndiv)
                                                                                        {
                                                                                            <span>@oneZone.ZoneName</span>

                                                                                            <span>@oneFloor.FloorName</span>
                                                                                        }
                                                                                        <span>@oneSection.SectionName</span>
                                                                                    }
                                                                                </span>



                                                                            </div>
                                                                            <div class="col-12 col-md text-center text-md-left">
                                                                                <div class="productSeatDetailCoupleTarifCateg" data-isadhesion="@isAdhesion">
                                                                                    <div>@Html.Raw(specialIcon)@onePrice.PriceName</div>
                                                                                    @if (oneSeat.Identity != null && !string.IsNullOrEmpty(oneSeat.Identity.FirstName) && !string.IsNullOrEmpty(oneSeat.Identity.SurName))
                                                                                    {
                                                                                        <div>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblAttachTo")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblAttachTo") : "Attaché à") <strong>@oneSeat.Identity.FirstName @oneSeat.Identity.SurName</strong></div>
                                                                                    }
                                                                                    <div>@oneSeat.CategoryName</div>
                                                                                </div>

                                                                            </div>
                                                                            <div class="col-12 col-md-auto text-center text-md-right">
                                                                                @{
                                                                                    var normalPrice = 0;
                                                                                    var specialPrice = 0;
                                                                                    var fees = 0;

                                                                                    if (onePrice.UnitValue > 0 && ViewBag.BuyerProfil.IsReseller)
                                                                                    {
                                                                                        //si la valeur est supérieur à 0 et que le profil est en mode revendeur, alors le prix principal est la valeur
                                                                                        normalPrice = onePrice.UnitValue;
                                                                                    }
                                                                                    else
                                                                                    {
                                                                                        //la valeur est barré
                                                                                        if (onePrice.UnitValue > 0)
                                                                                        {
                                                                                            specialPrice = onePrice.UnitValue;
                                                                                        }
                                                                                        //le prix principal est le montant normal
                                                                                        normalPrice = onePrice.UnitTTCAmount;
                                                                                    }
                                                                                    fees = onePrice.UnitFeeAmount;
                                                                                    var onePriceUnitHTAmount = (int)(onePrice.UnitTTCAmount / (1 + (eventSessions.TvaTaux / 100)));
                                                                                }
                                                                                <div class="priceUnitWrapper">

                                                                                    @*<div class="priceUnitHT"><span data-pricetoformat="@onePriceUnitHTAmount">@onePriceUnitHTAmount</span> <span>HT (trad)</span></div>*@
                                                                                    <div>

                                                                                        @if (oneSeat.FeedBookToken == null)
                                                                                        {

                                                                                            @if (specialPrice > 0)
                                                                                            {
                                                                                                <s class="ticketamount" data-amount="@specialPrice" data-pricetoformat="@specialPrice">@specialPrice</s>
                                                                                            }
                                                                                            <span class="priceUnitTTC" data-pricetoformat="@normalPrice">@normalPrice</span>
                                                                                            @if(fees > 0 && ViewBag.SettingsMerge.sessions.showIncludedFees == true)
                                                                                            {
                                                                                                
                                                                                               string freeAmountHtml = "<span data-pricetoformat='" + fees + "'>" + fees + "</span>";

                                                                                                <br/><small class='feeamount'><em>
                                                                                                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblIncludedFees")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblIncludedFees").Replace("{Amount}", freeAmountHtml) : "Dont " + freeAmountHtml + " de frais (trad)")
                                                                                                </em></small>
                                                                                            }
                                                                                        }
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                    </div>
                                                                    <!-- one seat end -->
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        </div>
                                    </div>
                                </div>
                                <!-- details end -->
                                @if (listSessionsProds != null && listSessionsProds.Count > 0)
                                {
                                    var thisSessionWithProd = listSessionsProds.Where(e => e.SessionId == eventSessions.SessionId).FirstOrDefault();
                                    if (thisSessionWithProd != null && thisSessionWithProd.ProductsBoutique != null)
                                    {
                                        //on compte combien de produits sont associés à cette séance
                                        var ProdSessionCount = 0;
                                        var BtncollapseProductsClass = "collapsed";
                                        var collapseProductsClass = "collapse";

                                        foreach (var pfam in thisSessionWithProd.ProductsBoutique.ProductFamiliesList)
                                        {
                                            foreach (var psubfam in pfam.SubFamiliesList)
                                            {
                                                ProdSessionCount = psubfam.ProductsList.Count;
                                                foreach (var prod in psubfam.ProductsList)
                                                {
                                                    if (prod.TypeInsertion == "AUTO" && prod.Count > 0)
                                                    {
                                                        BtncollapseProductsClass = "";
                                                        collapseProductsClass = "collapse show";
                                                    }
                                                }
                                            }
                                        }

                                        if (ViewBag.SettingsMerge.basket.collapseProductsEventSession == "True" && ViewBag.SettingsMerge.basket.collapseProductsEventSessionMinNumber <= ProdSessionCount)
                                        {
                                            @:<div class="btn btn-sm btn-outline-dark mt-2 productsSessionBtn @(BtncollapseProductsClass)" data-toggle="collapse" data-target="#collapseProductsSession_@(eventSessions.SessionId)" aria-expanded="@((BtncollapseProductsClass == "").ToString().ToLower())" aria-controls="collapseProductsSession_@(eventSessions.SessionId)">
                                                @:<span class="productsSessionCollapsed"><i class="fa-solid fa-circle-plus"></i> @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnCollapseProductsSession")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnCollapseProductsSession") : "Ajouter des produits associés à cette séance (trad)") (@ProdSessionCount)</span>
                                                @:<span class="productsSessionNotCollapsed"><i class="fa-solid fa-circle-minus"></i></span>
                                            @:</div>
                                            @:<div class="@(collapseProductsClass) pt-2" id="collapseProductsSession_@(eventSessions.SessionId)">
                                            }

                                            @foreach (var pfam in thisSessionWithProd.ProductsBoutique.ProductFamiliesList)
                                                foreach (var psubfam in pfam.SubFamiliesList)
                                                    foreach (var prod in psubfam.ProductsList)
                                                    {
                                                        oneSummaryLineTotalTTC = prod.AmountTTCCents * prod.Count;
                                                        oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (prod.TvaTaux / 100)));
                                                        if (prod.Count > 0)
                                                        {
                                                            lstTauxTva.Add(new { taux = prod.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                                            ProductsSubTotalTTC += oneSummaryLineTotalTTC;
                                                        }
                                                        string isActive = "";
                                                        if (prod.AmountIsVariable && prod.AmountTTCCents > 0)
                                                        {
                                                            isActive = "active";
                                                        }
                                                        if (!prod.AmountIsVariable && prod.Count > 0)
                                                        {
                                                            isActive = "active";
                                                        }
                                                        <!-- produits à la séance start-->
                                                        <div class="row oneProduct notMandatory @isActive pl-0">
                                                            <div class="col">
                                                                @{
                                                                    var ProductImgPhysicalPath = Configuration["Images:BaseImagesPhysicalPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits\\" + prod.ProductId + ".gif";
                                                                    var ProductImgUrlPath = Configuration["Images:BaseImagesUrlPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits/" + prod.ProductId + ".gif";

                                                                    if (!System.IO.File.Exists(ProductImgPhysicalPath))
                                                                    {
                                                                        ProductImgUrlPath = Configuration["Images:AssetsImagesUrlPath"] + "product-noimage.jpg";
                                                                    }
                                                                }
                                                                <div class="productNameWrapper mb-1">
                                                                    <img class="productImg img-fluid rounded" src="@ProductImgUrlPath" />
                                                                    @if (prod.AmountIsVariable)
                                                                    {
                                                                        <div class="productName">@prod.ProductName</div>
                                                                    }
                                                                    else
                                                                    {
                                                                        @*<div class="productName inptProductVariableUnitWrapper"><input class="inptProductVariableUnit" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@prod.Count" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" />&nbsp;x&nbsp;@prod.ProductName</div>
                                                                            <div class="priceUnitWrapper">
                                                                            (<span class="priceUnitTTC" data-pricetoformat="@prod.AmountTTCCents">@prod.AmountTTCCents</span>)
                                                                            </div>*@
                                                                        var bdisableThisSelect = prod.TypeInsertion == "AUTO";
                                                                        var classdisableThisSelect = bdisableThisSelect ? "disabled" : ""; // produit AUTO ne doivent pas être modifiables
                                                                        var attributdisableThisSelect = bdisableThisSelect ? "disabled='disabled'" : ""; // produit AUTO ne doivent pas être modifiables

                                                                        <div class="productName inptProductVariableUnitWrapper form-inline">
                                                                            <select class="inptProductVariableUnit form-control @classdisableThisSelect"
                                                                                    @attributdisableThisSelect
                                                                                    data-step="@prod.Step" data-min="@prod.MinSellable"
                                                                                    data-max="@prod.MaxSellable" data-productid="@prod.ProductId" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux">
                                                                                <option value="0">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") 0</option>
                                                                                @{
                                                                                    var step = prod.Step;
                                                                                    if (prod.Step <= 0)
                                                                                    {
                                                                                        step = 1;
                                                                                    }
                                                                                    for (int i = prod.MinSellable; i <= prod.MaxSellable; i = i + int.Parse((step.ToString())))
                                                                                    {
                                                                                        if (i != 0)
                                                                                        {
                                                                                            if (prod.Count == i)
                                                                                            {
                                                                                                <option value="@i" selected>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                                            }
                                                                                            else
                                                                                            {
                                                                                                <option value="@i">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                                            }

                                                                                        }

                                                                                    }
                                                                                }
                                                                            </select>
                                                                            &nbsp;x&nbsp;@prod.ProductName
                                                                        </div>
                                                                        <div class="priceUnitWrapper">
                                                                            (<span class="priceUnitTTC" data-pricetoformat="@prod.AmountTTCCents">@prod.AmountTTCCents</span>)
                                                                        </div>
                                                                    }
                                                                </div>
                                                            </div>
                                                            <div class="col-12 col-md-auto text-right">
                                                                <div class="priceTotalWrapper">
                                                                    @if (prod.AmountIsVariable)
                                                                    {
                                                                        <div class="inptProductVariableAmountWrapper">
                                                                            @*si  produit montant variable *@
                                                                            @if (ViewBag.DeviseCode.IsBefore)
                                                                            {
                                                                                @Html.Raw(ViewBag.DeviseCode.Code) <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" />

                                                                            }
                                                                            else
                                                                            {
                                                                                <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" /> @Html.Raw(ViewBag.DeviseCode.Code)

                                                                            }
                                                                        </div>
                                                                    }
                                                                    else
                                                                    {
                                                                        @*si  produit unité variable *@
                                                                        oneSummaryLineTotalTTC = prod.AmountTTCCents * prod.Count;
                                                                        oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (prod.TvaTaux / 100)));
                                                                        if (prod.Count > 0)
                                                                        {
                                                                            lstTauxTva.Add(new { taux = prod.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                                                            MoreProductsSubTotalTTC += oneSummaryLineTotalTTC;
                                                                        }

                                                                        <div class="priceTotal" data-producttype="productVariableUnit" data-tvataux="@prod.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</div>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- produits à la séance end-->
                                                    }
                                            @if (ViewBag.SettingsMerge.basket.collapseProductsEventSession == "True" && ViewBag.SettingsMerge.basket.collapseProductsEventSessionMinNumber <= ProdSessionCount)
                                            {
                                            @:</div>
                                        }
                                        }
                                    }

                            </div>
                            <!-- session end -->
                        }
                    </div>
                    <!-- session wrapper end -->
                    @if (listEvProds != null && listEvProds.Count > 0)
                    {
                        var thisEventWithProd = listEvProds.Where(e => e.EventId == events.EventId).FirstOrDefault();
                        if (thisEventWithProd != null && thisEventWithProd.ProductsBoutique != null)
                        {
                            //on compte combien de produits sont associés à cette évènement
                            var ProdEventCount = 0;
                            var BtncollapseProductsClass = "collapsed";
                            var collapseProductsClass = "collapse";

                            foreach (var pfam in thisEventWithProd.ProductsBoutique.ProductFamiliesList)
                            {
                                foreach (var psubfam in pfam.SubFamiliesList)
                                {
                                    ProdEventCount = psubfam.ProductsList.Count;
                                    foreach (var prod in psubfam.ProductsList)
                                    {
                                        if (prod.TypeInsertion == "AUTO" && prod.Count > 0)
                                        {
                                            BtncollapseProductsClass = "";
                                            collapseProductsClass = "collapse show";
                                        }
                                    }
                                }
                            }
                            if (ViewBag.SettingsMerge.basket.collapseProductsEventSession == "True" && ViewBag.SettingsMerge.basket.collapseProductsEventSessionMinNumber <= ProdEventCount)
                            {
                                @:<div class="btn btn-sm btn-outline-dark mt-2 productsEventBtn @(BtncollapseProductsClass)" data-toggle="collapse" data-target="#collapseProductsEvent_@(events.EventId)" aria-expanded="@((BtncollapseProductsClass == "").ToString().ToLower())" aria-controls="collapseProductsEvent_@(events.EventId)">
                                    @:<span class="productsEventCollapsed"><i class="fa-solid fa-circle-plus"></i> @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnCollapseProductsEvent")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnCollapseProductsEvent") : "Ajouter des produits associés à cet évènement (trad)") (@ProdEventCount)</span>
                                    @:<span class="productsEventNotCollapsed"><i class="fa-solid fa-circle-minus"></i></span>
                                @:</div>
                                @:<div class="@(collapseProductsClass) pt-2" id="collapseProductsEvent_@(events.EventId)">
                                }
                                @foreach (var pfam in thisEventWithProd.ProductsBoutique.ProductFamiliesList)
                                    foreach (var psubfam in pfam.SubFamiliesList)
                                        foreach (var prod in psubfam.ProductsList)
                                        {
                                            var oneSummaryLineTotalTTC = prod.AmountTTCCents * prod.Count;
                                            var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (prod.TvaTaux / 100)));
                                            if (prod.Count > 0)
                                            {
                                                lstTauxTva.Add(new { taux = prod.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                                ProductsSubTotalTTC += oneSummaryLineTotalTTC;
                                            }
                                            string isActive = "";
                                            if (prod.AmountIsVariable && prod.AmountTTCCents > 0)
                                            {
                                                isActive = "active";
                                            }
                                            if (!prod.AmountIsVariable && prod.Count > 0)
                                            {
                                                isActive = "active";
                                            }
                                            <!-- produits à la manif start-->
                                            <div class="row oneProduct notMandatory @isActive pl-0">
                                                <div class="col">
                                                    @{
                                                        var ProductImgPhysicalPath = Configuration["Images:BaseImagesPhysicalPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits\\" + prod.ProductId + ".gif";
                                                        var ProductImgUrlPath = Configuration["Images:BaseImagesUrlPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits/" + prod.ProductId + ".gif";

                                                        if (!System.IO.File.Exists(ProductImgPhysicalPath))
                                                        {
                                                            ProductImgUrlPath = Configuration["Images:AssetsImagesUrlPath"] + "product-noimage.jpg";
                                                        }
                                                    }
                                                    <div class="productNameWrapper mb-1">
                                                        <img class="productImg img-fluid rounded" src="@ProductImgUrlPath" />
                                                        @if (prod.AmountIsVariable)
                                                        {
                                                            <div class="productName">@prod.ProductName</div>
                                                        }
                                                        else
                                                        {
                                                            <!--<div class="productName inptProductVariableUnitWrapper"><input class="inptProductVariableUnit" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",", "."))" data-productid="@prod.ProductId" value="@prod.Count" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" />&nbsp;x&nbsp;@prod.ProductName</div>
                                                            <div class="priceUnitWrapper">
                                                                (<span class="priceUnitTTC" data-pricetoformat="@prod.AmountTTCCents">@prod.AmountTTCCents</span>)
                                                            </div>-->
                                                            var bdisableThisSelect = prod.TypeInsertion == "AUTO";
                                                            var classdisableThisSelect = bdisableThisSelect ? "disabled" : ""; // produit AUTO ne doivent pas être modifiables
                                                            var attributdisableThisSelect = bdisableThisSelect ? "disabled='disabled'" : ""; // produit AUTO ne doivent pas être modifiables

                                                            <div class="productName inptProductVariableUnitWrapper form-inline">
                                                                <select class="inptProductVariableUnit form-control @classdisableThisSelect"
                                                                        @attributdisableThisSelect
                                                                        data-step="@prod.Step" data-min="@prod.MinSellable" data-max="@prod.MaxSellable" data-productid="@prod.ProductId" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux">
                                                                    <option value="0">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") 0</option>
                                                                    @{
                                                                        var step = prod.Step;
                                                                        if (prod.Step <= 0)
                                                                        {
                                                                            step = 1;
                                                                        }
                                                                        for (int i = prod.MinSellable; i <= prod.MaxSellable; i = i + int.Parse((step.ToString())))
                                                                        {
                                                                            if (i != 0)
                                                                            {
                                                                                if (prod.Count == i)
                                                                                {
                                                                                    <option value="@i" selected>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                                }
                                                                                else
                                                                                {
                                                                                    <option value="@i">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                                }

                                                                            }

                                                                        }
                                                                    }
                                                                </select>
                                                                &nbsp;x&nbsp;@prod.ProductName
                                                            </div>
                                                            <div class="priceUnitWrapper">
                                                                (<span class="priceUnitTTC" data-pricetoformat="@prod.AmountTTCCents">@prod.AmountTTCCents</span>)
                                                            </div>

                                                        }

                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-auto text-right">
                                                    <div class="priceTotalWrapper">
                                                        @if (prod.AmountIsVariable)
                                                        {
                                                            <div class="inptProductVariableAmountWrapper">
                                                                @*si  produit montant variable *@
                                                                @if (ViewBag.DeviseCode.IsBefore)
                                                                {
                                                                    @Html.Raw(ViewBag.DeviseCode.Code) <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" />

                                                                }
                                                                else
                                                                {
                                                                    <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" /> @Html.Raw(ViewBag.DeviseCode.Code)

                                                                }
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            @*si  produit unité variable *@
                                                            oneSummaryLineTotalTTC = prod.AmountTTCCents * prod.Count;
                                                            oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (prod.TvaTaux / 100)));
                                                            if (prod.Count > 0)
                                                            {
                                                                lstTauxTva.Add(new { taux = prod.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                                                MoreProductsSubTotalTTC += oneSummaryLineTotalTTC;
                                                            }

                                                            <div class="priceTotal" data-producttype="productVariableUnit" data-tvataux="@prod.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</div>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- produits à la manif end -->
                                        }
                                @if (ViewBag.SettingsMerge.basket.collapseProductsEventSession == "True" && ViewBag.SettingsMerge.basket.collapseProductsEventSessionMinNumber <= ProdEventCount)
                                {
                                @:</div>
                            }
                            }
                        }
                </div>
                <!-- one event end -->
            }
        }
        <!-- ListEventsUnitSales end -->
        <!-- all products adhesion start -->
        @if (ViewBag.Basket.ListProductCartesAdhesion != null && ViewBag.Basket.ListProductCartesAdhesion.Count > 0)
        {
            foreach (var product in ViewBag.Basket.ListProductCartesAdhesion)
            {
                <!-- one product adhesion start -->
                var oneSummaryLineTotalTTC = product.AmountTTCinCent * product.Count;
                var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (product.TvaTaux / 100)));
                var oneSummaryLineUnitPriceTTC = (oneSummaryLineTotalTTC / @product.Count);
                lstTauxTva.Add(new { taux = product.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                ProductsSubTotalTTC += oneSummaryLineTotalTTC;

                <div class="oneSummaryLine oneProduct @((product.TypeInsertion !="AUTO") ? "oneProductWithDelete" : "")" data-productid="@product.ProductId" data-consumerid="@product.ConsommateurIdentiteId" data-basketlineid="@product.BasketLineId">
                    @if (product.TypeInsertion != "AUTO")
                    {
                        <div class="basketDeleteProduct text-danger"><i class="fa fa-trash" aria-hidden="true"></i></div>
                    }
                    <div class="row">
                        <div class="col">
                            <div class="collapsed" data-toggle="collapse" data-target="#collapse@(product.ProductId)_@(product.ConsommateurIdentiteId)" aria-expanded="false" aria-controls="collapse@(product.ProductId)_@(product.ConsommateurIdentiteId)">
                                <div class="productName"><span class="img-adhesion"><i class='adhesionIcon adhesionIconMedaillon'></i></span> @product.ProductName</div>
                                @if (product.Identity != null)
                                {
                                    <div class="productAttachedTo">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblAttachTo")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblAttachTo") : "Attaché à") <strong>@product.Identity.FirstName @product.Identity.SurName</strong></div>
                                }
                                <div class="collapeIconWrapper">
                                    <span class="productDetailsCollapseBtn btn btn-sm btn-outline-secondary">
                                        <span class="productIcon"><i class="fas fa-box-open"></i></span>
                                        <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsProducts")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsProducts") : "Détails des produits")</span> <span class="productCount">(@product.Count)</span>
                                    </span>
                                </div>
                            </div>

                        </div>
                        <div class="col-12 col-md-auto text-right productPriceTotalWrapper">
                            <div class="priceTotalWrapper">
                                @*<span class="priceTotalHT"><span data-pricetoformat="@oneSummaryLineTotalHT">@oneSummaryLineTotalHT</span> <span>HT (trad)</span></span>*@
                                <span class="priceTotal" data-tvataux="@product.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetocalcul="add" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</span>
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="collapse productSeatsWrapper" id="collapse@(product.ProductId)_@(product.ConsommateurIdentiteId)">
                                <div class="row">
                                    <div class="col">
                                        @for (int i = 0; i < product.Count; i++)
                                        {
                                            <div class="productDetailsWrapper">
                                                <div class="row productDetails">
                                                    <div class="col align-self-center text-center text-md-left">
                                                        <span>1 x @product.ProductName</span>
                                                    </div>
                                                    <div class="col-12 col-md-auto text-center text-md-right">
                                                        <div class="">
                                                            <div class="priceUnitWrapper">
                                                                @*<div class="priceUnitHT"><span data-pricetoformat="@oneSummaryLineTotalHT">@oneSummaryLineTotalHT</span> <span>HT (trad)</span></div>*@
                                                                <div class="priceUnitTTC" data-pricetoformat="@product.AmountTTCinCent">@product.AmountTTCinCent</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!-- one product adhesion end -->
            }
        }
        <!-- all products adhesion end -->
        <!-- all products purs start -->
        @if (ViewBag.Basket.ListProduitsWT != null && ViewBag.Basket.ListProduitsWT.Count > 0)
        {
            var idxProduit = 0;

            foreach (var product in ViewBag.Basket.ListProduitsWT)
            {
                @* on affiche pas les produits associés à des events et/ou session *@
                if (product.EventId == 0 && product.SessionId == 0 && product.Count > 0)
                {
                    <!-- one product pur start -->
                    var oneSummaryLineTotalTTC = product.AmountTTCinCent * product.Count;
                    var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (product.TvaTaux / 100)));
                    var oneSummaryLineUnitPriceTTC = (oneSummaryLineTotalTTC / @product.Count);
                    lstTauxTva.Add(new { taux = product.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                    ProductsSubTotalTTC += oneSummaryLineTotalTTC;

                    <div class="oneSummaryLine oneProduct oneProductWithDelete" data-productid="@product.ProductId" data-basketlineid="@product.BasketLineId">
                        <div class="basketDeleteProduct text-danger"><i class="fa fa-trash" aria-hidden="true"></i></div>
                        <div class="row">
                            <div class="col">
                                <div class="collapsed" data-toggle="collapse" data-target="#collapse@(product.ProductId)_@idxProduit" aria-expanded="false" aria-controls="collapse@(product.ProductId)_@idxProduit">
                                    @{
                                        var ProductImgPhysicalPath = Configuration["Images:BaseImagesPhysicalPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits\\" + product.ProductId + ".gif";
                                        var ProductImgUrlPath = Configuration["Images:BaseImagesUrlPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits/" + product.ProductId + ".gif";

                                        if (!System.IO.File.Exists(ProductImgPhysicalPath))
                                        {
                                            ProductImgUrlPath = Configuration["Images:AssetsImagesUrlPath"] + "product-noimage.jpg";
                                        }
                                    }
                                    <div class="productNameWrapper mb-1">
                                        <img class="productImg img-fluid rounded" src="@ProductImgUrlPath" />
                                        <div class="productName">@product.ProductName</div>
                                    </div>


                                    <div class="collapeIconWrapper">
                                        <span class="productDetailsCollapseBtn btn btn-sm btn-outline-secondary">
                                            <span class="productIcon"><i class="fas fa-box-open"></i></span>
                                            <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsProducts")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblDetailsProducts") : "Détails des produits")</span> <span class="productCount">(@product.Count)</span>
                                        </span>
                                    </div>
                                </div>

                            </div>
                            <div class="col-12 col-md-auto text-right productPriceTotalWrapper">
                                <div class="priceTotalWrapper">
                                    @*<span class="priceTotalHT"><span data-pricetoformat="@oneSummaryLineTotalHT">@oneSummaryLineTotalHT</span> <span>HT (trad)</span></span>*@
                                    <span class="priceTotal" data-tvataux="@product.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetocalcul="add" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</span>
                                </div>
                            </div>
                            <div class="col-12 ">
                                <div class="collapse productSeatsWrapper" id="collapse@(product.ProductId)_@idxProduit">
                                    <div class="row">
                                        <div class="col">
                                            @for (int i = 0; i < product.Count; i++)
                                            {
                                                <div class="productDetailsWrapper">
                                                    <div class="row productDetails">
                                                        <div class="col align-self-center text-center text-md-left">
                                                            <span>1 x @product.ProductName</span>
                                                        </div>
                                                        <div class="col-12 col-md-auto text-center text-md-right">
                                                            <div class="">
                                                                <div class="priceUnitWrapper">
                                                                    @*<div class="priceUnitHT"><span data-pricetoformat="@oneSummaryLineTotalHT">@oneSummaryLineTotalHT</span> <span>HT (trad)</span></div>*@
                                                                    <div class="priceUnitTTC" data-pricetoformat="@product.AmountTTCinCent">@product.AmountTTCinCent</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- one product pur end -->
                    idxProduit++;
                }



            }
        }
        <!-- all products purs end -->
        <!-- sub total ttc start-->
        <div id="subTotalEventsProductsWrapper" class="oneSummaryLine bg-primary-12">
            <div class="row">
                <div class="col align-self-center">
                    <div>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes") : "Sous-total TTC")</div>
                </div>
                <div class="col-4 col-md-3 text-right align-self-center">
                    @if (EventsSubSpecialTotalTTC > 0)
                    {
                        <s class="ticketamount" data-amount="@(EventsSubSpecialTotalTTC + ProductsSubTotalTTC)" data-pricetoformat="@(EventsSubSpecialTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC)">@(EventsSubSpecialTotalTTC + ProductsSubTotalTTC)</s>
                    }
                    <span class="priceTotal" data-pricetoformat="@(EventsSubTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC)">@(EventsSubTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC)</span>
                </div>
            </div>
        </div>
        <!-- sub total ttc end-->
    </div>
    <!-- résumé du panier end -->
    @if (ViewBag.prodsGlob != null && ViewBag.prodsGlob.Count > 0)
    {

        <!-- add more products end-->
        <div id="summaryMoreProducts" class="boxedBlock nopaddingX nopaddingY">
            <h2 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleAddMoreProducts")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleAddMoreProducts") : "Ajouter des produits supplémentaires ?")</h2>
            @foreach (var pfam in ViewBag.prodsGlob)
                foreach (var psubfam in pfam.SubFamiliesList)
                    foreach (var prod in psubfam.ProductsList)
                    {
                        string isActive = "";
                        if (prod.AmountIsVariable && prod.AmountTTCCents > 0)
                        {
                            isActive = "active";
                        }
                        if (!prod.AmountIsVariable && prod.Count > 0)
                        {
                            isActive = "active";
                        }

                        <div class="oneSummaryLine oneProduct notMandatory @isActive" data-productid="@prod.ProductId" data-basketlineid="@prod.BasketLineId">
                            <div class="row">
                                <div class="col">
                                    @{
                                        var ProductImgPhysicalPath = Configuration["Images:BaseImagesPhysicalPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits\\" + prod.ProductId + ".gif";
                                        var ProductImgUrlPath = Configuration["Images:BaseImagesUrlPath"].ToString().Replace("{structureId}", ViewBag.StructureId.ToString("0000")) + "produits/" + prod.ProductId + ".gif";

                                        if (!System.IO.File.Exists(ProductImgPhysicalPath))
                                        {
                                            ProductImgUrlPath = Configuration["Images:AssetsImagesUrlPath"] + "product-noimage.jpg";
                                        }
                                    }
                                    <div class="productNameWrapper mb-1">
                                        <img class="productImg img-fluid rounded" src="@ProductImgUrlPath" />
                                        @if (prod.AmountIsVariable)
                                        {
                                            @*si  produit montant variable *@
                                            <div class="productName">@prod.ProductName</div>
                                        }
                                        else
                                        {
                                            @*si  produit unité variable *@
                                            <!--<div class="productName inptProductVariableUnitWrapper"><input class="inptProductVariableUnit" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",", "."))" data-productid="@prod.ProductId" value="@prod.Count" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" />&nbsp;x&nbsp;@prod.ProductName</div>
                                            <div class="priceUnitWrapper">
                                                (<span class="priceUnitTTC" data-pricetoformat="@prod.AmountTTCCents">@prod.AmountTTCCents</span>)
                                            </div>-->
                                            bool bdisableThisSelect = prod.MinSellable == prod.MaxSellable;

                                            <div class="productName inptProductVariableUnitWrapper form-inline">
                                                <select disabled="@bdisableThisSelect" class="inptProductVariableUnit form-control" data-step="@prod.Step" data-min="@prod.MinSellable" data-max="@prod.MaxSellable" data-productid="@prod.ProductId" data-unitpricettccents="@prod.AmountTTCCents" data-tvataux="@prod.TvaTaux" data-typemontant="@prod.TypeMontant">
                                                    <option value="0">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") 0</option>
                                                    @{
                                                        var step = prod.Step;
                                                        if (prod.Step <= 0)
                                                        {
                                                            step = 1;
                                                        }
                                                        for (int i = prod.MinSellable; i <= prod.MaxSellable; i = i + int.Parse((step.ToString())))
                                                        {
                                                            if (i != 0)
                                                            {

                                                                if (prod.MinSellable == i)
                                                                {
                                                                    <option value="@i" selected>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                }
                                                                else
                                                                {
                                                                    <option value="@i">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblQuantityShortDot") : "Qté :") @i</option>
                                                                }

                                                            }

                                                        }
                                                    }
                                                </select>
                                                &nbsp;x&nbsp;@prod.ProductName
                                            </div>
                                            <div class="priceUnitWrapper">
                                                @if (prod.TypeMontant == 2)
                                                {
                                                    @Html.Raw($"(<span>{@prod.Description}</span>)")
                                                }
                                                else
                                                {
                                                    @Html.Raw($"(<span class=\"priceUnitTTC\" data-pricetoformat=\"{@prod.AmountTTCCents}\">{@prod.AmountTTCCents}</span>)")
                                                }
                                            </div>
                                        }

                                    </div>
                                </div>
                                <div class="col-12 col-md-auto text-right productPriceTotalWrapper">
                                    <div class="priceTotalWrapper">

                                        @if (prod.AmountIsVariable)
                                        {
                                            <div class="inptProductVariableAmountWrapper">
                                                @*si  produit montant variable *@
                                                @if (ViewBag.DeviseCode.IsBefore)
                                                {
                                                    @Html.Raw(ViewBag.DeviseCode.Code) <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@(prod.AmountTTCCents)" data-tvataux="@prod.TvaTaux" />

                                                }
                                                else
                                                {
                                                    <input class="priceTotal inptProductVariableAmount" data-producttype="productVariableAmount" type="number" min="0" data-realmin="@prod.MinSellable" max="@prod.MaxSellable" step="@(prod.Step.ToString().Replace(",","."))" data-productid="@prod.ProductId" value="@(((double)prod.AmountTTCCents / 100).ToString("0.00").Replace(",","."))" data-pricetoformat="@(prod.AmountTTCCents)" data-tvataux="@prod.TvaTaux" /> @Html.Raw(ViewBag.DeviseCode.Code)

                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            @*si  produit unité variable *@
                                            var oneSummaryLineTotalTTC = prod.AmountTTCCents * prod.Count;
                                            var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (prod.TvaTaux / 100)));
                                            if (prod.Count > 0)
                                            {
                                                lstTauxTva.Add(new { taux = prod.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                                                MoreProductsSubTotalTTC += oneSummaryLineTotalTTC;
                                            }

                                            <div class="priceTotal" data-producttype="productVariableUnit" data-tvataux="@prod.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)" data-pricetoformat="@oneSummaryLineTotalTTC">@oneSummaryLineTotalTTC</div>
                                        }
                                    </div>
                                </div>

                            </div>
                        </div>
                    }
            <!-- sub total ttc start-->
            <div id="subTotalMoreProductsWrapper" class="oneSummaryLine bg-primary-12">
                <div class="row">
                    <div class="col align-self-center">
                        <div>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes") : "Sous-total TTC")</div>
                    </div>
                    <div class="col-4 col-md-3 text-right align-self-center">
                        <div class="priceTotal" data-pricetoformat="@(MoreProductsSubTotalTTC)">@(MoreProductsSubTotalTTC)</div>
                    </div>
                </div>
            </div>
            <!-- sub total ttc end-->
        </div>
        <!-- add more products end-->
    }


    <!-- Profil acheteur : renseigner le "consommeateur si profilacheteur.ConsumerNeeded = true "-->
    @if (ViewBag.BuyerProfil != null && ViewBag.BuyerProfil.ConsumerNeeded)
    {
        <div class="boxedBlock nopaddingX nopaddingY">
            <h2 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleNameYourTickets")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleNameYourTickets") : "Nommer vos billets")</h2>
            <div class="row pl-3 pr-3 mb-3" id="inputConsommateurPAWrapper">
                <div class="col-12">
                    <label for="inputConsommateurPA" class="sr-only">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblNameConsumerPA")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblNameConsumerPA") : "Nom du consommateur")</label>
                    <input type="text" maxlength="50" id="inputConsommateurPA" class="form-control" placeholder="@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblNameConsumerPA")) ? Regex.Replace(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblNameConsumerPA"), "<.*?>", String.Empty): "Nom du consommateur" )" />

                </div>
            </div>
        </div>
    }
    <!-- Profil acheteur end-->
    <!-- Mode d'obtention et frais start -->
    <div class="boxedBlock nopaddingX nopaddingY">
        <h2 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleObtainingMethodFees")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleObtainingMethodFees") : "Mode d'obtention et frais")</h2>
        <div id="ModesObtentionsWrapper">
            <partial name="_ModesObtentions" model="@ViewBag.initialmodelToUseInPartial" />
        </div>
        <!-- all fees start -->
        <div id="basketFeesWrapper">
            @if (ViewBag.Basket.ListFrais != null)
            {
                foreach (var oneFees in ViewBag.Basket.ListFrais)
                {
                    var oneSummaryLineTotalTTC = oneFees.AmountTTCinCent * oneFees.Count;
                    var oneSummaryLineTotalHT = (int)(oneSummaryLineTotalTTC / (1 + (oneFees.TvaTaux / 100)));
                    var oneSummaryLineUnitPriceTTC = (oneSummaryLineTotalTTC / @oneFees.Count);
                    lstTauxTva.Add(new { taux = oneFees.TvaTaux, montant = (int)(oneSummaryLineTotalTTC - oneSummaryLineTotalHT) });
                    FeesSubTotalTTC += oneSummaryLineTotalTTC;
                    <div class="oneSummaryLine oneFees bg-primary-5" data-productid="@oneFees.ProductId">
                        <div class="row">
                            <div class="col align-self-center">
                                <div>@oneFees.ProductName</div>
                            </div>
                            <div class="col-auto align-self-center text-right">
                                <div class="priceTotal" data-pricetocalcul="add" data-pricetoformat="@oneFees.AmountTTCinCent" data-tvataux="@oneFees.TvaTaux" data-tvaamount="@(oneSummaryLineTotalTTC - oneSummaryLineTotalHT)">@oneFees.AmountTTCinCent</div>
                            </div>
                        </div>
                    </div>
                }

            }
        </div>
        <!-- all fees end -->
        <!-- sub total ttc start-->
        <div id="subTotalFeesWrapper" class="oneSummaryLine bg-primary-12">
            <div class="row">
                <div class="col align-self-center">
                    <div>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblSubTotalWithTaxes") : "Sous-total TTC")</div>
                </div>
                <div class="col-4 col-md-3 text-right align-self-center">
                    <div class="priceTotal" data-pricetoformat="@FeesSubTotalTTC">@FeesSubTotalTTC</div>
                </div>
            </div>
        </div>
        <!-- sub total ttc end-->
    </div>
    <!-- Mode d'obtention et frais start -->
    <!-- total cmd start -->
    <div id="basketTotalCmdWrapper" class="boxedBlock nopaddingX nopaddingY">
        <div class="oneSummaryLine summaryLineTotalOrder bigTotalWrapper">
            <div class="row">
                <div class="col align-self-center">
                    <div>
                        @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblOrderTotalWithTaxes")) ?
                        @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_LblOrderTotalWithTaxes") : "Total de la commande TTC")
                    </div>
                </div>
                <div class="col-4 col-md-3 text-right align-self-cente">
                    @if (EventsSubSpecialTotalTTC > 0)
                    {
                        <s class="ticketamount" data-amount="@(EventsSubSpecialTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)" data-pricetoformat="@(EventsSubSpecialTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)">@(EventsSubSpecialTotalTTC + ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)</s>
                    }
                    <span class="priceTotal" data-pricetoformat="@(EventsSubTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)">aaa @(EventsSubTotalTTC + AboSubTotalTTC + ProductsSubTotalTTC + FeesSubTotalTTC + FeesDeliverySubTotalTTC)</span>
                </div>
            </div>
        </div>
        @*<div id="totalTVABigWrapper">
            <div class="row collapse" id="collapseDetailsTVA">
            <div class="col align-self-center collapseDetailsWrapper">
            @{
            var TauxTvaSubTotalTTC = 0;
            var lstTauxTvaGrouped = (from oneTaux in lstTauxTva group oneTaux by oneTaux.taux into tauxGroup
            select new {
            taux = Convert.ToDecimal(string.Format("{0:0.#####}", tauxGroup.Key)),
            montant = tauxGroup.Sum(x => x.montant)
            }).ToList();
            }
            @foreach (var oneTaux in lstTauxTvaGrouped )
            {
            if(oneTaux.taux > 0) {
            <div class="oneSummaryLine">
            <div class="row">
            <div class="col">
            <div>@oneTaux.taux%</div>
            </div>
            <div class="col-4 col-md-3 text-right">
            <div class="priceTotal" data-pricetoformat="@oneTaux.montant">@oneTaux.montant</div>
            </div>
            </div>
            </div>
            TauxTvaSubTotalTTC += @oneTaux.montant;
            }
            }
            </div>
            </div>
            @if (TauxTvaSubTotalTTC > 0) {
            <div class="oneSummaryLine totalTVAWrapper">
            <div class="row">
            <div class="col align-self-center">
            <div data-toggle="collapse" data-target="#collapseDetailsTVA" aria-expanded="false" aria-controls="collapseDetailsTVA"><i class="collapseArrow collapseArrowUp fas fa-chevron-down"></i> <span>dont TVA (trad)</span></div>
            </div>
            <div class="col-4 col-md-3 text-right align-self-center">
            <div class="priceTotal" data-pricetoformat="@TauxTvaSubTotalTTC">@TauxTvaSubTotalTTC</div>
            </div>
            </div>
            </div>
            }*@
    </div>
</div>
<!-- total cmd end-->
<!-- CGV & extra optin start-->
<div class="row mb-1">
    <div class="col">
        <div id="CGVExtraOptinWrapper">
            <!-- CGV start -->
            <div class="mb-3">
                <div class="custom-control custom-checkbox" id="CGVWrapper">
                    <input class="custom-control-input" type="checkbox" value="" id="checkboxCGV" required="required">
                    <label class="custom-control-label" for="checkboxCGV">
                        @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_CheckboxCGV")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_CheckboxCGV").Replace("{Link}", "<a href='#' id='seeCGV' data-url='" + ViewBag.CGVurl + "'>").Replace("{/Link}", "</a>") : "je reconnais avoir pris connaissance et j’accepte <a href='#' id='seeCGV' data-url='" + ViewBag.CGVurl + "'>les conditions générales de vente</a>")
                    </label>
                </div>
            </div>
            <!-- CGV end -->

            @if (ViewBag.infosCompJson != null)
            {
                var root = (JsonInfoCompGroupSettings)ViewBag.infosCompJson;
                var pageToShow = "Basket";
                if(root.Groups.Count > 0)
                {
                    <!-- extra optin start -->
                    foreach (var group in root.Groups.OrderBy(g => g.PrefAffichage))
                    {
                        var displayGroup = true;
                        foreach (var infocomp in group.InfoComps)
                        {
                            if(infocomp.PageToShow.Contains(pageToShow) && group.HideWhenChecked.Contains(pageToShow) && infocomp.IsAssignedToIdentity)
                            {
                                displayGroup = false;
                            }
                        }

                        if(displayGroup) {
                            <div class="mb-3" data-groupid="@group.Id">
                               @{
                                    JsonInfoCompTraduction groupTitle = group.TradTitle.FirstOrDefault(t => t.LangCode.ToLower() == langCode.ToLower());
                               }
                                @if( groupTitle is not null && !String.IsNullOrEmpty(groupTitle.Traduction))
                                {
                                    <div class="mb-1"><strong>@groupTitle.Traduction</strong></div>
                                }
                            
                                @switch(group.Format)
                                {
                                    case "checkbox":
                                        foreach(var infocomp in group.InfoComps)
                                        {
                                            if(infocomp.PageToShow.Contains(pageToShow))
                                            {
                                                
                                                JsonInfoCompTraduction infoCompLabel = infocomp.TradLabel.FirstOrDefault(t => t.LangCode.ToLower() == langCode.ToLower());
                                                
                                               <div class="custom-control custom-checkbox">
                                                  <input class="custom-control-input" type="checkbox" value="" id="infocomp_@(infocomp.Id)" @((infocomp.Precheck || infocomp.IsAssignedToIdentity) ? "checked" : "") @((infocomp.Mandatory) ? "required" : "") @((infocomp.Enable) ? "" : "disabled")>
                                                  <label class="custom-control-label" for="infocomp_@(infocomp.Id)">@infoCompLabel.Traduction</label>
                                                </div>
                                            }
                                        }
                                    
                                        break;
                                    case "radio":
                                        foreach(var infocomp in group.InfoComps)
                                        {
                                            if(infocomp.PageToShow.Contains(pageToShow))
                                            {
                                                
                                                JsonInfoCompTraduction infoCompLabel = infocomp.TradLabel.FirstOrDefault(t => t.LangCode.ToLower() == langCode.ToLower());

                                               <div class="custom-control custom-radio">
                                                  <input class="custom-control-input" type="radio" name="infocomp_group_@(group.Id)" id="infocomp_@(infocomp.Id)" value="@(infocomp.Id)" @((infocomp.Precheck || infocomp.IsAssignedToIdentity) ? "checked" : "") @((infocomp.Mandatory) ? "required" : "") @((infocomp.Enable) ? "" : "disabled")>
                                                  <label class="custom-control-label" for="infocomp_@(infocomp.Id)">@infoCompLabel.Traduction</label>
                                                </div>
                                            }
                                        }

                                        break;
                                    case "select":
                                        var select_mandatory = false;
                                        var enable = false;
                                        foreach (var infocomp in group.InfoComps)
                                        {
                                            if(infocomp.Mandatory)
                                            {
                                                select_mandatory = true;
                                            }
                                            if(infocomp.Enable)
                                            {
                                                enable = true;
                                            }
                                        }
                                        <div class="mt-1">
                                            <div class="form-group form-inline mb-0">
                                                <select class="form-control" id="infocomp_group_@(group.Id)" @((select_mandatory) ? "required" : "")@((enable) ? "" : "disabled")>
                                                    @if(group.Format == "select") {
                                                        <option value="">Sélectionner une option</option>
                                                    }
                                                    @foreach(var infocomp in group.InfoComps)
                                                    {
                                                        if(infocomp.PageToShow.Contains(pageToShow))
                                                        {
                                                            
                                                            JsonInfoCompTraduction infoCompLabel = infocomp.TradLabel.FirstOrDefault(t => t.LangCode.ToLower() == langCode.ToLower());

                                                            if(infocomp.Precheck || infocomp.IsAssignedToIdentity) {
                                                                <option value="@(infocomp.Id)" selected>@infoCompLabel.Traduction</option>
                                                            } else {
                                                                <option value="@(infocomp.Id)" >@infoCompLabel.Traduction</option>
                                                            }
                                                  
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        

                                        break;
                                    case "select_multiple":
                                        var select_multiple_mandatory = false;
                                        foreach (var infocomp in group.InfoComps)
                                        {
                                            if(infocomp.Mandatory)
                                            {
                                                select_multiple_mandatory = true;
                                            }
                                        }
                                        <div class="mt-1">
                                            <div class="form-group form-inline mb-0">
                                                <select multiple class="form-control selectpicker" id="infocomp_group_@(group.Id)" @((select_multiple_mandatory) ? "required" : "")  title="Sélectionner une option" data-selected-text-format="count > 0"  data-count-selected-text="{0} option(s) sélectionnée(s)">
                                                    @if(group.Format == "select") {
                                                        <option value="">Sélectionner une option</option>
                                                    }
                                                    @foreach(var infocomp in group.InfoComps)
                                                    {
                                                        if(infocomp.PageToShow.Contains(pageToShow))
                                                        {
                                                            
                                                            JsonInfoCompTraduction infoCompLabel = infocomp.TradLabel.FirstOrDefault(t => t.LangCode.ToLower() == langCode.ToLower());

                                                            if(infocomp.Precheck || infocomp.IsAssignedToIdentity) {
                                                                if(!infocomp.Enable)
                                                                {
                                                                    <option value="@(infocomp.Id)" selected disabled>@infoCompLabel.Traduction</option>
                                                                }else {
                                                                    <option value="@(infocomp.Id)" selected>@infoCompLabel.Traduction</option>
                                                                }
                                                                
                                                            } else {
                                                                    if(!infocomp.Enable)
                                                                {
                                                                    <option value="@(infocomp.Id)" disabled>@infoCompLabel.Traduction</option>
                                                                }else {
                                                                    <option value="@(infocomp.Id)">@infoCompLabel.Traduction</option>
                                                                }
                                                            }
                                                  
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div> 
                                        
                                        break;
                                }
                            </div>
                        }
                    }
                    <!-- extra optin start -->
                }
            }
        </div>
    </div>
</div>
<!-- CGV & extra optin start-->

<div class="row">
    <div class="col-12" id="allAlertsBasket">
        <div class="alert alert-danger" id="alert-consumerPA-empty" role="alert" style="display:none;">
            @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorNameConsumerPA")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorNameConsumerPA") : "Merci de renseigner le nom du consommateur pour nommer vos billets.")
        </div>
        <div class="alert alert-danger" id="alert-mo-empty" role="alert" style="display:none;">
            @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorObtainingMethodEmpty")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorObtainingMethodEmpty") : "Merci de sélectionner un mode d'obtention pour continuer votre achat.")
        </div>
        <div class="alert alert-danger" id="alert-cgv-empty" role="alert" style="display:none;">
            @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorCgvNotChecked")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_MsgErrorCgvNotChecked") : "Merci de lire et d'accepter les conditions générales de vente pour continuer votre achat.")
        </div>
    </div>
    <div class="col-12 col-md text-center text-md-left">
        @if (ViewBag.SettingsMerge.basket.showButtonBackToEventsList == "True")
        {
            <div class="btn btn-secondary mb-2" id="backToEventsList" data-structureid="@(ViewBag.StructureId.ToString("0000"))">
                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnBackToEventsList")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnBackToEventsList") : "Ajouter un autre évènement")
            </div>
        }
        @if (ViewBag.SettingsMerge.basket.showButtonBackToLastEvent == "True" && ViewBag.EventId != 0)
        {
            <div class="btn btn-secondary mb-2" id="backToLastEvent" data-structureid="@(ViewBag.StructureId.ToString("0000"))" data-eventid="@(ViewBag.EventId)">
                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnBackToLastEvent")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnBackToLastEvent") : "Ajouter des places")
            </div>
        }
    </div>
    <div class="col-12 col-md-auto text-center text-md-right align-self-end">
        @if (ViewBag.IdentityId != 0)
        {
            <div id="confirmBasket" class="btn btn-primary mb-2">
                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnValidPayOrder")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_BtnValidPayOrder") : "Valider et payer ma commande")
            </div>
        }
        else
                                {
                                    if (@Model.UseContext == null || @Model.UseContext.ToLower() == "indiv")
                                    {

                <div id="goToLogin" class="btn btn-primary mb-2">
                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin") : "Se connecter")
                </div>
            }
            else
                                {
                <div id="goToLoginTunnel" class="btn btn-primary mb-2">
                    (@Model.UseContext)
                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin") : "Se connecter TUNNEL")
                </div>
            }

        }
    </div>
</div>
</div>
<!-- recap content end -->
<!-- modal LOGIN -->
<div class="modal fade" id="modalLogin" tabindex="-1" role="dialog" aria-labelledby="modalLoginLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">

                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!--
              <component type="typeof(CustomerAreaApp)" render-mode="Server" param-AppSettings="@Model" />
        -->
            </div>
        </div>
    </div>
</div>


<!-- modal CGV -->
<div class="modal fade" id="modalCGV" tabindex="-1" role="dialog" aria-labelledby="modalCGVLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalCGVLabel">
                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleModalCGV")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleModalCGV") : "Conditions générales de vente")
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                ...
            </div>
        </div>
    </div>
</div>

<!-- modal See My Placement -->
<div class="modal fade" id="modalSeeMyPlacement" tabindex="-1" role="dialog" aria-labelledby="modalSeeMyPlacementLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalSeeMyPlacementLabel">
                    @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSeeMyPlacement")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Basket_TitleSeeMyPlacement") : "Voir mon placement")
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                ...
            </div>
        </div>
    </div>
</div>
@*************** SCRIPTS *************@
<script id="hidden-template-tva-oneline" type="text/x-custom-template">
    <div class="oneSummaryLine">
         <div class="row">
             <div class="col">
                 <div>[tvaTaux]%</div>
             </div>
             <div class="col-4 col-md-3 text-right">
                 <div class="priceTotal" data-pricetoformat="[tvaAmountCents]">[tvaAmount]</div>
             </div>
         </div>
     </div>
</script>
<script id="hidden-template-mo-oneline" type="text/x-custom-template">
    <div class="oneSummaryLine oneMo bg-primary-5" data-moid="[moId]" data-selectmotype="[moType]">
    <div class="row">
        <div class="col align-self-center">
            <div>[moName]</div>
        </div>
        <div class="col-auto align-self-center text-right">
            <div class="priceTotal" data-pricetocalcul="add" data-pricetoformat="[moPriceCents]" data-tvataux="[moTVATaux]" data-tvaamount="[moTVAAmount]">[moPrice]</div>
        </div>
    </div>
    </div>
</script>
<script id="hidden-template-fees-oneline" type="text/x-custom-template">
    <div class="oneSummaryLine bg-primary-5">
    <div class="row">
        <div class="col align-self-center">
            <div>[feesName]</div>
        </div>
        <div class="col-4 col-md-3 align-self-center text-right">
            <div class="priceTotal" data-pricetocalcul="add" data-pricetoformat="[price]">[price]</div>
        </div>
    </div>
    </div>
</script>
@section scripts {
    <script>
        var TranslationsList = @Html.Raw(Json.Serialize(TranslationsList));
        var widgetOfferUrl = "@ViewBag.WOfferUrl";
        var widgetCatalogUrl = "@ViewBag.WCatalogUrl";
        var widgetCustomerUrl = "@ViewBag.WCustomerUrl";

        var apiToken = "@ViewBag.Token";
        var widgetSignature = "@ViewBag.WidgetSignature";
        var widgetSignatureCallsGet = "@ViewBag.SignatureWidgetGet";
        var structureId = parseInt("@ViewBag.StructureId");
        var langCode = "@ViewBag.LangCode";
        var htmlSelector = "@ViewBag.HtmlSelector";

        var identityId = @Html.Raw(ViewBag.IdentityId);
        var webUserId = @Html.Raw(ViewBag.WebUserId);
        var buyerProfilId = @Html.Raw(ViewBag.BuyerProfilId);
        var partnerToken = "@ViewBag.PartnerToken";
        var deviseCode = @Html.Raw(Json.Serialize(ViewBag.DeviseCode));
        var Basket = @Html.Raw(Json.Serialize(ViewBag.Basket));
        var productsGlobal = @Html.Raw(Json.Serialize(ViewBag.prodsGlob));

        var BaseImagesUrlPath = '@Configuration["Images:BaseImagesUrlPath"]';
        var infosCompJson = @Html.Raw(Json.Serialize(ViewBag.infosCompJson));
        var infosCompHash = "@ViewBag.infosCompHash";
    </script>
    <script src="@Configuration["AssetsUrlPath"]LIBS/bootstrap-select/1.14.0-beta3/bootstrap-select.min.js"></script>
    <script src="@(UrlToPath)js/bootstrap-spinner/bootstrap-input-spinner.js"></script>
    <script src="@(ViewBag.CustomPackageUrl)"></script>
    <script src="@(UrlToPath)js/Basket/basket.js"></script>
}