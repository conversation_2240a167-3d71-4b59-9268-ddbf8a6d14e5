

/* utilisé dans Carrousel Home */

DECLARE @TblFunctions table (BlockFunctionId int, FunctionId int, FunctionName varchar(250) )
[FOREACH.INSERTINTO]
/*

INSERT INTO @TblFunctions VALUES (29, 17, 'SELECT_Front_Title')
INSERT INTO @TblFunctions VALUES (16, 8, 'Input_Session_Date_Picker')
INSERT INTO @TblFunctions VALUES (17, 9, 'SELECT_Events_Genre_List_Multiple')
INSERT INTO @TblFunctions VALUES (18, 10, 'SELECT_Events_Subgenre_List_Multiple')
INSERT INTO @TblFunctions VALUES (19, 11, 'SELECT_Events_List_Multiple')
INSERT INTO @TblFunctions VALUES (20, 12, 'Input_Session_Filling_Min')
INSERT INTO @TblFunctions VALUES (21, 13, 'Input_Session_Filling_Max')
INSERT INTO @TblFunctions VALUES (30, 18, 'SELECT_Front_MsgIntro')
INSERT INTO @TblFunctions VALUES (27, 16, 'Input_Items_Max_To_Display')

DECLARE @pBlockEmplacement int = 191
DECLARE @pLangCode varchar(5) = 'fr'
DECLARE @pIsHomeTeasing bit = 1 --- propriete_of_manifs pour la date d'ouverture de la manif ---- ajouter IsShowComingSoon dans le résultat de la table si date ouverture > getdate() bool 1
DECLARE @pBuyerProfilId bit = 0

*/

--DECLARE @pLangCode varchar(2) = 'fr'
--DECLARE @pBlockEmplacement int = 3
--DECLARE @pIsHomeTeasing int = 0


DECLARE @langue_id int = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)



DECLARE @TblDispoManifs table (Manif_Nom varchar(250)
								, Manif_id int 
								, Manif_Groupe_id int
								, Manif_Groupe_nom varchar(250)
								, Groupe_Genre_id  int 
								, Groupe_genre_nom varchar(250)
								, Genre_id  int 
								, Genre_nom varchar(250)
								, DateMin DateTime
								, DateMax Datetime
								, PlacesDispo decimal(18,10) 
								, PlacesTotal decimal(18,10)
								)



DECLARE @TblDispoManifsGenerales table (Manif_Nom varchar(250)
								, Manif_id int 
								, Manif_Groupe_id int
								, Manif_Groupe_nom varchar(250)
								, Groupe_Genre_id  int 
								, Groupe_genre_nom varchar(250)
								, Genre_id  int 
								, Genre_nom varchar(250)
								, DateMin DateTime
								, DateMax Datetime
								, PlacesDispo decimal(18,10) 
								, PlacesTotal decimal(18,10)
								)


								
DECLARE @TblGpAdhesion table ( gestion_place_id INT    ) 


		INSERT INTO @TblGpAdhesion /* add les gestion places des offres d'adhesion mises en avant */
				SELECT gp.gestion_place_id 
							FROM offre o
							INNER JOIN offre_gestion_place ogp on ogp.offre_id = o.offre_id
							INNER JOIN gestion_place gp on gp.gestion_place_id = ogp.gestion_place_id
							INNER JOIN Adhesion_Catalog_offresliees ol on ol.offre_id = o.offre_id
							INNER JOIN Adhesion_Catalog acat on acat.Adhesion_Catalog_ID = ol.Adhesion_Catalog_ID
							INNER JOIN Adhesion_Catalog_Propriete acprop on acprop.Adhesion_Catalog_ID = acat.Adhesion_Catalog_ID and gp.type_tarif_id = acprop.Propriete_Valeur_Int1
                            LEFT JOIN offre_contrainte oc on oc.offre_id = o.offre_id
						WHERE isvalide =1		
                        AND oc.contrainte_id is null /* offres adh sans contraintes */


DECLARE @BlockFunction_ID int 
DECLARE @SQL varchar(max)

SET @BlockFunction_ID = (SELECT BlockFunctionId FROM @TblFunctions WHERE FunctionName = 'Input_Items_Max_To_Display')
DECLARE @NbTop int = ISNULL((SELECT BlockcUserConfig_Value FROM HomeModular_BlockUserConfig 
																			WHERE BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ), 0)  --- Par défaut 8, mais pourra être changé

DECLARE @ProfilAcheteurId int = @pBuyerProfilId --- Normalement, on ne gère pas le profil d'acheteur, mais à penser. donc 0 par défaut

SET @BlockFunction_ID = (SELECT BlockFunctionId FROM @TblFunctions WHERE FunctionName = 'Input_Session_Filling_Min')
DECLARE @FiltreRemplissageMin int = (SELECT BlockcUserConfig_Value FROM HomeModular_BlockUserConfig 
																			WHERE BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 
SET @BlockFunction_ID = (SELECT BlockFunctionId FROM @TblFunctions WHERE FunctionName = 'Input_Session_Filling_Max')
DECLARE @FiltreRemplissageMax int = (SELECT BlockcUserConfig_Value FROM HomeModular_BlockUserConfig 
																			WHERE BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 


SET @BlockFunction_ID = (SELECT BlockFunctionId FROM @TblFunctions WHERE FunctionName = 'Input_Session_Date_Picker')
DECLARE @FiltreDatesSeance varchar(50) = (SELECT BlockcUserConfig_Value FROM HomeModular_BlockUserConfig 
																			WHERE BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) -- les données de filtres brutes ( 04/04/2023 - 30/05/2030 )
DECLARE @FiltreDateSeanceDeb datetime --- la date deb 
DECLARE @FiltreDateSeancefin datetime --- la date fin

--- Attention, chaque filtre est additionnel

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Events_List_Multiple')
declare @FiltreManif varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )  --'1;2;3;4;7;8;9;10;11;12;13;14' --- Filtre à définir à l'appel du SQL  Select_Events_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Events_Genre_List_Multiple')
Declare @FiltreGroupeGenre varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																		where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2;3;4' --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Groupe_genre = Genre Select_Events_Genre_List_Multiple

set @BlockFunction_ID = (select BlockFunctionId from @TblFunctions where FunctionName = 'Select_Events_Subgenre_List_Multiple')
Declare @FiltreGenre Varchar(max) = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																		where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2' --- --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Genre = Sous_Genre 
--- Filtres date deb séance / Date Fin séance

SET @FiltreDateSeanceDeb = '01/01/1900'	
SET @FiltreDateSeancefin = '31/12/2199'
IF @FiltreDatesSeance > ''
BEGIN
	SET @FiltreDateSeanceDeb = (left(@FiltreDatesSeance,10))+' ' + '00:00:00'
	--SELECT @FiltreDateSeanceDeb
	SET @FiltreDateSeancefin = (right(@FiltreDatesSeance,10))+' ' + '00:00:00'
	--SELECT @FiltreDateSeancefin
END


/***************** on construit une table temp avec TOUT */


	INSERT INTO @TblDispoManifsGenerales
			SELECT m.manifestation_nom
	, gp.manif_id 
	, mg.manif_groupe_id
	, mg.manif_groupe_nom
	,ISNULL(genre.id,0) as 'Groupe_Genre_ID'
	,ISNULL(genre.nom,'') as 'Groupe_Genre'
	,ISNULL(sousg.id,0) as 'Genre_ID'
	,ISNULL(sousg.nom,'') as 'Genre'
	,(SELECT min(s1.seance_date_deb) FROM seance s1 WHERE s1.manifestation_id = gp.manif_id) as 'Date_Deb'
	,(SELECT max(s2.seance_date_deb) FROM seance s2 WHERE s2.manifestation_id = gp.manif_id) as 'Date_fin'
	,sum (gp.dispo) as PlacesDispo
	,sum(gp.placestotal) as PlacesTotal

	FROM gestion_place gp 
	INNER JOIN seance s on s.seance_Id = gp.seance_id		
	INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id 
	LEFT  JOIN GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
	INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
	LEFT  JOIN manifestation_genre sousg on sousg.id = m.ID_genre /* les sous genres de genre */
	LEFT  JOIN manifestation_groupe_genre genre on genre.id = sousg.Groupe_id /* genre */
	INNER JOIN lieu l on l.lieu_id = s.lieu_id
	
	LEFT OUTER JOIN @TblGpAdhesion gpAdh on gpAdh.gestion_place_id = gp.gestion_place_id

	WHERE dispo>0 and isvalide=1 
	AND s.seance_date_deb >= getdate() 
	--AND isContrainteIdentite =0 
	AND gp.formule_id is null 
	AND gp.gestion_place_parent_id is NOT null 
	AND gp.dispo > 0 
	AND gp.placesTotal > 0
	
	AND s.seance_date_deb BETWEEN @FiltreDateSeanceDeb AND @FiltreDateSeancefin
	--or m.manifestation_id in (SELECT manif_id FROM @ManifHomeTease) ---- VA 11/10/2023
	GROUP BY 
	m.manifestation_nom
	, gp.manif_id 
	, mg.manif_groupe_id
	, mg.manif_groupe_nom
	,ISNULL(genre.id,0)
	,ISNULL(genre.nom,'')
	,ISNULL(sousg.id,0)
	,ISNULL(sousg.nom,'') 

	--SELECT *FROM @TblDispoManifs	

	

IF @ProfilAcheteurId = 0
BEGIN
		--Ajoute les manifestation de l'offre générale
		INSERT INTO @TblDispoManifs
			select * from @TblDispoManifsGenerales
END


IF @ProfilAcheteurId > 0
BEGIN
	INSERT INTO @TblDispoManifs
		SELECT m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,ISNULL(genre.id,0) as 'Groupe_Genre_ID'
		,ISNULL(genre.nom,'') as 'Groupe_Genre'
		,ISNULL(sousg.id,0) as 'Genre_ID'
		,ISNULL(sousg.nom,'') as 'Genre'		
		,(SELECT MIN(s1.seance_date_deb) FROM seance s1 WHERE s1.manifestation_id = gp.manif_id) as 'Date_Deb'
		,(SELECT MAX(s2.seance_date_deb) FROM seance s2 WHERE s2.manifestation_id = gp.manif_id) as 'Date_fin'
		,SUM (gp.dispo)
		,SUM (gp.placestotal) 	
		FROM 
		gestion_place gp 
		INNER JOIN offre_gestion_place ogp on gp.gestion_place_id = ogp.gestion_place_id
		INNER JOIN offre o on o.offre_id = ogp.offre_id
		INNER JOIN offre_profil_acheteur opa on opa.offre_id = o.offre_id and opa.profil_acheteur_id =@ProfilAcheteurId  
		INNER JOIN seance s on s.seance_Id = gp.seance_id
		INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id 
		LEFT  JOIN GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
		INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		LEFT JOIN manifestation_genre sousg on sousg.id = m.ID_genre /* les sous genres de genre */
		LEFT JOIN manifestation_groupe_genre genre on genre.id = sousg.Groupe_id /* genre */
		INNER JOIN lieu l on l.lieu_id = s.lieu_id

		LEFT OUTER JOIN @TblGpAdhesion gpAdh on gpAdh.gestion_place_id = gp.gestion_place_id

		WHERE dispo>0 and isvalide=1 
		AND s.seance_date_deb >= getdate() 
		AND isContrainteIdentite =1 
		AND gp.formule_id is null 
		AND gp.gestion_place_parent_id is NOT null 
		AND gp.dispo > 0 
		AND gp.placesTotal > 0		
		AND s.seance_date_deb BETWEEN @FiltreDateSeanceDeb AND @FiltreDateSeancefin
	
		GROUP BY 
		m.manifestation_nom, gp.manif_id , mg.manif_groupe_id, mg.manif_groupe_nom, ISNULL(genre.id,0), ISNULL(genre.nom,''), ISNULL(sousg.id,0), ISNULL(sousg.nom,'') 	

		
		DECLARE @addgeneralsrules bit = 0
		SELECT @addgeneralsrules = count(*) FROM profil_acheteur pa WHERE pa.id = @ProfilAcheteurId AND pa.add_generals_rules = 1

		IF @addgeneralsrules = 1
		BEGIN
			--Ajoute les manifestation de l'offre générale
			INSERT INTO @TblDispoManifs
				select * from @TblDispoManifsGenerales

		END
END

---
/***************** on supp de la table temp les manifs QUI NE REPONDENT PAS aux filtres */
--- Manifs 
DECLARE @TmpFiltreManif table (id int) 

IF @FiltreManif > '' and @FiltreManif <> 'ALL'
BEGIN 
		SET @SQL = 'SELECT Manifestation_id FROM Manifestation WHERE Manifestation_id in (' + replace(@FiltreManif,';',',') +  ') ' 
		INSERT INTO @TmpFiltreManif exec (@SQL) 

		DELETE @TblDispoManifs WHERE Manif_id not in (SELECT id FROM @TmpFiltreManif)
		DELETE @TmpFiltreManif

END 
--SELECT 'manif', *FROM   @TblDispoManifs	

IF @FiltreGroupeGenre > '' and @FiltreGroupeGenre <> 'ALL'
BEGIN 
		SET @SQL = 'SELECT Manifestation_id FROM manifestation m
					INNER JOIN manifestation_genre sg on m.ID_genre = sg.id				
					WHERE sg.groupe_id in (' + replace(@FiltreGroupeGenre,';',',') +  ') '		
		INSERT INTO @TmpFiltreManif exec (@SQL) 

		DELETE @TblDispoManifs WHERE Manif_id not in (SELECT id FROM @TmpFiltreManif)
		DELETE @TmpFiltreManif

END 

--SELECT 'genre', *FROM @TblDispoManifs	
IF @FiltreGenre > '' and @FiltreGenre <> 'ALL'
BEGIN 
		SET @SQL = 'SELECT Manifestation_id FROM manifestation m			
					WHERE m.ID_genre IN (' + replace(@FiltreGenre,';',',') +  ') '
		
		INSERT INTO @TmpFiltreManif EXEC (@SQL) 

		DELETE @TblDispoManifs WHERE Manif_id not in (SELECT id FROM @TmpFiltreManif)
		DELETE @TmpFiltreManif
END 

--SELECT 'sous genre', *FROM @TblDispoManifs	

---- Requête finale 

UPDATE @TblDispoManifs SET placesTotal = placesDispo WHERE Placestotal =0 /* gestion si placetotal n'est pas mis à jour dans sp_gestion_place */


SELECT TOP (@NbTop) 
 manif_id  as EventId,
 ISNULL(trad_manif.manifestation_nom,  Manif_Nom) as eventname,
 manif_groupe_id as eventgroupeid,
 manif_groupe_nom as eventgroupename,
 groupe_genre_id as genreid,
 ISNULL(trad_manif_genre.nom,  groupe_genre_nom) as genrename,    --- sur thémis manifestation_groupe_genre = genre
 genre_id as subgenreid,
 ISNULL(trad_manif_sous_genre.nom,  genre_nom) as subgenrename,  ---- sur thémis manifestation_genre = sous_genre
 datemin as sessionstartdate,
 datemax as sessionenddate,
 0 as isshowcomingsoon,

 PlacesDispo as Dispos, ----- Pour vérifier le calcul des dispos
 PlacesTotal as Places_Total,
--, PlacesTotal-PlacesDispo as Places_Prises
 round(((PlacesTotal-PlacesDispo) / PlacesTotal *100),2) as Taux_Remplissage,
  gpm.islock 
FROM @tbldispomanifs dispM
LEFT JOIN traduction_manifestation trad_manif on dispm.manif_id = trad_manif.manifestation_id and trad_manif.langue_id = @langue_id
LEFT JOIN traduction_manifestation_groupe_genre trad_manif_genre on trad_manif_genre.id = dispm.groupe_genre_id and trad_manif_genre.langue_id = @langue_id
LEFT JOIN traduction_manifestation_genre trad_manif_sous_genre on trad_manif_sous_genre.id = dispm.genre_id and trad_manif_sous_genre.langue_id = @langue_id
LEFT OUTER JOIN GP_manifestation gpm ON gpm.manifestation_id = dispM.Manif_id
WHERE placestotal > 0 ---- VA 11/10/2023

AND ROUND(((PlacesTotal-PlacesDispo) / PlacesTotal *100),2) BETWEEN @FiltreRemplissageMin and @FiltreRemplissageMax

GROUP BY ISNULL(trad_manif.manifestation_nom,  manif_nom),manif_groupe_id,manif_id,manif_groupe_nom,groupe_genre_id,ISNULL(trad_manif_genre.nom,  groupe_genre_nom),
genre_id, ISNULL(trad_manif_sous_genre.nom,  Genre_nom), DateMin, DateMax, PlacesDispo,PlacesTotal, islock  ----- Pour vérifier le calcul des dispos

ORDER BY newid()


----
	---- FIN Requête finale 
----


--SELECT *FROM gestion_place gp 
--inner join seance s on s.seance_id = gp.seance_id 

--WHERE    s.seance_date_deb >= getdate()  and manif_id =1  
--and gp.isContrainteIdentite =0 ---> tout public
--and gp.formule_id is null and gp.gestion_place_parent_id is null

--and gp.type_tarif_id > 0

