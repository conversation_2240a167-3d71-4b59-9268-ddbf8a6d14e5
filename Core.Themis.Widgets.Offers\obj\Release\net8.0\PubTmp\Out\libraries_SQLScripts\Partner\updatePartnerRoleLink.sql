﻿
--DECLARE @PartnerId int =1
BEGIN TRANSACTION
	BEGIN TRY
	-- on commence par supprimer les relation entre un partenaire et un rôle 
	-- dans la table de liaison 
	DELETE FROM partenaires_has_role WHERE partenaire_id = @pPartnerId
	INSERT INTO partenaires_has_role(partenaire_id,partenaire_role_id) 
	 select @pPartnerId , id from partenaires_roles where id in @plistRoles
	COMMIT
	END TRY

	BEGIN CATCH

	select 'error'
	ROLLBACK;
	 THROW; 
END CATCH
