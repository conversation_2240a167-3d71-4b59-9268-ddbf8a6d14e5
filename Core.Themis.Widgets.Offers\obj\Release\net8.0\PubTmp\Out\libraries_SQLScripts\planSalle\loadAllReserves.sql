
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = 'fr'

IF @LgId IS NULL
	SET @LgId = 0



SELECT r.reserve_id, r.icone,
	case when tr.reserve_nom is null then r.reserve_nom else tr.reserve_nom end as reserve_nom,
	r.reserve_code, r.PREFAFFICHAGE as pref_affichage, r.reserve_couleur_id, r.operateur_id
	
FROM reserve r
LEFT OUTER JOIN traduction_reserve tr on tr.reserve_id = r.reserve_id and tr.langue_id = @LgId
WHERE r.MASQUER ='N'
UNION
SELECT 0, 0, 'NONE','NONE',0,0,0

