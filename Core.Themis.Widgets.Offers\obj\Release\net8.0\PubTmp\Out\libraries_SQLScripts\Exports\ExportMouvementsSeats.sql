﻿---- pour organiser par date de vente/résa etc ...

/*
declare @psinceminutes int = 300000
declare @pdatefrom datetime = '01/01/1900'
declare @pdateto datetime = '01/01/2100'
declare @pStartDate datetime = '01/01/1900'
declare @pEndDate datetime = '01/01/2100'
declare @pplaceid int = 62
declare @peventid int = 0
declare @pPageLength int = 5
declare @pPage int = 2
declare @porderid int = 245716

*/

DECLARE @PageNumber AS INT
DECLARE @RowsOfPage AS INT

SET @PageNumber=@ppage
SET @RowsOfPage=@ppagelength


CREATE TABLE #TmpEntreeVardar2  (
	code_base varchar(250) ,
	structure_id int,
	identite_id int, 
	event_id int,
	session_id int,
	place_id int

	,identifiant varchar(250)
	,commande_numero int 	, dossier_id int
	,manif_nom varchar(250)
	,lieu_nom varchar(250)
	,date_seance datetime
	,place_lib_categ varchar(250)
	,Place_lib_denom varchar(250)
	,place_montant_euros varchar(250)
	,place_montant_dg_eu varchar(250)
	--,Date_paiement varchar(250)
	--,Heure_paiement varchar(250)
	,date_paiement datetime null
	,client_nom varchar(250)
	,dossier_client_nom varchar(250)
	,commentaire varchar(250)
	,billet_etat varchar(250)
	--,Date_operation date
	--,Heure_operation time
	,entree_id int, iindex int
	,rang varchar(10), siege varchar(10), zone_name varchar(50), floor_name varchar(50), section_name varchar(50)	
	
	,date_operation datetime	
	,place_lib_tarif varchar(50)
	,mode_paiement varchar(50)
	,barcode varchar(50)
 )


DECLARE @sinceMinutes int = convert(int, @psinceminutes)

DECLARE @C VARCHAR(8000)

DECLARE @manif_id int 
DECLARE @seance_id int 

DECLARE @DateDebstr as varchar(50)
DECLARE @DateFinstr as varchar(50)
DECLARE @DateSortiestr as varchar(8)

DECLARE @DateDeb as datetime
DECLARE @DateFin as datetime 

IF (@sinceMinutes <>0)
BEGIN
	SET @DateDeb = (select (dateadd(n , - @sinceMinutes, getdate() )))
	SET @DateFin = getdate()
END
ELSE
BEGIN
	SET @DateDeb = @pdateFrom
	SET @DateFin = @pdateTo
END

SET @DateDebstr =convert(VARCHAR, @DateDeb, 103) + ' ' + convert(VARCHAR, @DateDeb, 108)
SET @DateFinstr = convert(VARCHAR, @datefin, 103) + ' ' + convert(VARCHAR, @datefin, 108)
SET @DateSortiestr =convert(varchar ,CONVERT(datetime,GETDATE()-1,103),112)

DECLARE @ManifID int 
DECLARE @SeanceID int 
DECLARE @SQL VARCHAR(8000)


SELECT DISTINCT manif_id, seance_id 
INTO #manifSeanceToCheck FROM histodossier 
WHERE date_operation >= @DateDeb and manif_id>0

IF (@porderId >0) 
BEGIN -- supp les séances où il n'y a pas cette commande
DELETE #manifSeanceToCheck WHERE seance_id NOT IN (
	SELECT seance_id from histodossier where commande_id = @porderId)
END


IF (@pplaceid > 0) -- supp les séances qui ne sont pas sur le lieux
BEGIN
	DELETE #manifSeanceToCheck WHERE seance_id NOT IN (SELECT seance_id FROM seance WHERE lieu_id= @pplaceId)	
end
IF (@peventid > 0) -- supp les séances qui ne sont pas de la manif
BEGIN
	DELETE #manifSeanceToCheck WHERE manif_id <> @peventid
END
DELETE #manifSeanceToCheck WHERE seance_id 
	NOT IN (select seance_id from seance where seance_date_deb >= @pStartDate and seance_date_deb <= @pEndDate)

--ouverture du curseur paginé
DECLARE Curseur  CURSOR FOR
SELECT DISTINCT manif_id, seance_id from #manifSeanceToCheck order by manif_id, seance_id
OFFSET (@PageNumber-1)*@RowsOfPage ROWS FETCH NEXT @RowsOfPage ROWS ONLY

OPEN curseur
FETCH curseur INTO @ManifID, @SeanceID
--génération de la requête boucle
WHILE @@fetch_status = 0 
	BEGIN 
		SET @SQL = 'SELECT DISTINCT
			st.structure_nom,
			st.structure_id, 
			d.identite_id,
			' + convert(varchar,@ManifID) + ', 
			' + convert(varchar,@SeanceID) + ', 
			lc.lieu_id

			,convert(varchar,d.identite_id) +''_''+ convert(varchar,d.dossier_id) + ''_'' + convert(varchar,e.entree_id) 
			,d.commande_id
			,d.dossier_id as dossier_id
			,m.manifestation_nom 
			,lc.lieu_config_nom
			
			,seance_date_deb
			,cat.categ_nom
			,dnm.denom_nom
			,e.montant1+e.montant2 
			,e.montant2 

			,(SELECT dateoperation from entreesvg_' + convert(varchar,@ManifID) + '
				 where entree_etat = ''P'' and dossier_id = e.dossier_id and entree_id = e.entree_id and dossier_v = e.dossier_v ) as ''Date Paiement''
			
			,rtrim(ltrim(i.identite_nomprenom))
			,rtrim(ltrim(d.dossier_client_nom))
			,rtrim(ltrim(ci.commentaire))		
			,case e.entree_etat when  ''R'' then ''Réservé'' when ''P'' then ''Payé'' when ''B'' then ''Edité'' when ''L'' then ''Libéré'' else '''' end as ''Etat de la place''
	
			,e.entree_id, e1.iindex
			,rlp.rang, rlp.siege, z.zone_nom, et.etage_nom, sc.section_nom			
			
			,e.dateoperation 
			,tt.type_tarif_nom			
			,(SELECT top 1 isnull(mp.mode_paie_nom,''ACOMPTE'') from compte_client cc LEFT OUTER JOIN mode_paiement mp on mp.mode_paie_id = cc.mode_paiement_id  
					WHERE cc.cc_numpaiement = d.num_paiement and cc.mode_paiement_id >0  and d.num_paiement > 0  and d.dossier_montant>0) as ''Mode de paiement'' 			
			,case e.entree_etat when ''B'' then
				case when motif <> '''' then rtrim(ltrim(r.motif)) else rtrim(ltrim(r.externe)) end
			else '''' end
			
		FROM ENTREEsvg_' + convert(varchar,@ManifID) + ' e 
		INNER JOIN entree_' + convert(varchar,@ManifID) + ' e1 on e1.entree_id = e.entree_id
		INNER JOIN dossier_' + convert(varchar,@ManifID) + ' d on d.dossier_id=e.dossier_id
		INNER JOIN categorie cat on cat.categ_id = e.categorie_id
		INNER JOIN type_tarif tt on tt.type_tarif_id = e.type_tarif_id

		INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id  = e1.reference_unique_physique_id 
		INNER JOIN lieu_physique lp on lp.lieu_physique_id = rlp.lieu_physique_id
		INNER JOIN lieu_configuration lc on lc.lieu_physique_id = lp.lieu_physique_id

		INNER JOIN section sc on sc.section_id = rlp.section_id

		INNER JOIN zone z on z.zone_id = rlp.zone_id 
		INNER JOIN etage et on et.etage_id = rlp.etage_id 
		INNER JOIN seance s on s.seance_id=d.seance_id
		INNER JOIN commande_ligne cl on cl.dossier_id = e.dossier_id and cl.seance_id = e.seance_id and cl.type_ligne = ''DOS''
		LEFT JOIN commande_infos ci ON ci.commande_id = cl.commande_id and ci.commande_id = cl.commande_id
		INNER JOIN Commande_Ligne_comp clc ON clc.commande_ligne_id = cl.commande_ligne_id 
		INNER JOIN filiere f on f.filiere_id = clc.filiere_id
		INNER JOIN manifestation m on m.manifestation_id = cl.manifestation_id
		INNER JOIN identite i on i.identite_id = d.identite_id
		LEFT OUTER JOIN global_appellation ga on ga.appellation_id = i.appellation_id
		INNER JOIN type_tarif_groupe ttg on ttg.type_tarif_groupe_id = tt.type_tarif_groupe
		LEFT OUTER JOIN formule_abonnement fa on fa.form_abon_id = cl.formule_id
		LEFT OUTER JOIN recette r on r.entree_id = e.entree_id and r.seance_id = e.seance_id and r.dossier_id = e.dossier_id
				
		INNER JOIN manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		LEFT OUTER JOIN super_groupe sg on sg.id_super_groupe = mg.super_groupe_id
		INNER JOIN [structure] st on st.structure_id =st.structure_id
		LEFT OUTER JOIN manifestation_genre gen on gen.id = m.ID_genre  --- Attention ceci est le sous-genre
		LEFT OUTER JOIN manifestation_groupe_genre mgg on mgg.id = gen.groupe_id --- Attention ceci est le genre
		LEFT OUTER JOIN Seance_Cible scbl on scbl.Seance_id = s.seance_Id
		LEFT OUTER JOIN cible cbl on cbl.id = scbl.Cible_id

		LEFT OUTER JOIN filiere f2 on f2.filiere_id = i.filiere_id
		LEFT OUTER JOIN lieu l on l.lieu_id = s.lieu_id 
		LEFT OUTER JOIN denomination dnm on dnm.denom_id = rlp.denomination_id

		WHERE e.seance_id = ' + convert(varchar,@SeanceID) + '
		AND e.entree_etat in (''B'',''P'',''R'',''L'') and e.dateoperation BETWEEN '''+ @DateDebstr +''' and '''+ @DateFinstr +'''
		ORDER BY e.dateoperation'

		print @sql
	
		INSERT INTO #TmpEntreeVardar2 EXEC (@SQL)

		FETCH curseur INTO @ManifID, @SeanceID
	END
CLOSE curseur
DEALLOCATE curseur  


if (@porderId >0)
begin
	delete #TmpEntreeVardar2 where commande_numero <> @porderId
end

declare @totalEventsCount int = 0

SELECT @totalEventsCount = count(*) /* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
FROM #manifSeanceToCheck
print '@totalEventsCount=' + convert(varchar,@totalEventsCount)
declare @totalPagesCount int = 1
set @totalPagesCount = (@totalEventsCount-1) / @RowsOfPage + 1

SELECT  @PageNumber AS pagenumber, @totalPagesCount AS pagescount, @totalEventsCount AS totalElementsCount, 
			code_base, 
			structure_id, i.identite_id, event_id, session_id, place_id,
			identifiant, commande_numero, dossier_id, manif_nom, lieu_nom, date_seance, place_lib_categ,place_lib_denom, iindex, rang, siege, 
			zone_name, floor_name, section_name,		
			place_lib_tarif,
			place_montant_euros, place_montant_dg_eu,
			date_paiement,
			client_nom, dossier_client_nom, commentaire, 
						i.postal_tel1,
			i.postal_tel2,
			i.postal_tel3,
			i.postal_tel4,
			i.postal_tel5,
			i.postal_tel6,
			i.postal_tel7,
			billet_etat, mode_paiement, barcode, date_operation
			FROM #TmpEntreeVardar2 
			inner join identite i on #TmpEntreeVardar2.identite_id = i.identite_id
			
			
			ORDER BY date_operation
			--print @bcp

DROP TABLE #TmpEntreeVardar2

DROP TABLE #manifSeanceToCheck

SELECT @DateDeb as dateFrom, @dateFin as dateTo