
/* CheckGpIdEnCours */
/* ******* verif que le consumer n'a pas déjà ce tarif, cette SEANCE dans son panier */
/*=========> on peut prendre plusieurs fois la même manif sur des seances differentes ! */


declare @n int =0
select @n = count(*) from panier_entree pe
inner join panier p on p.panier_id = pe.panier_id 
where p.etat ='C'
and pe.consumer_id =@pidentiteid
and pe.type_tarif_id = @ppriceid 
and pe.seance_id = @pSessionId

if @n = 0
begin
	select 'ok' as ret
end
else
begin
	select 'ko' as ret
end




