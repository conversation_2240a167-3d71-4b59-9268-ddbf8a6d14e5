﻿
/* export les identites updatées depuis @dateparametre ou @lastexport */

declare @ClientTableLastExportDate datetime
declare @iCounter int

DECLARE @NumStructure varchar (4)


--declare @ppartnerId int = 123
--declare @pStartDate datetime = '01/01/1753 00:00:00'


set @NumStructure = convert(varchar,(select top 1 structure_id from structure))

--Verify param table
if  not exists(select so.name FROM sysobjects so where so.name = 'DataxplorerParamsApi')
	Begin
	    CREATE TABLE [dbo].DataxplorerParamsApi(
	         [ID] [decimal](18, 0) IDENTITY(1,1) NOT NULL,
			 partnerId int,
	         [Cle] [varchar](200) NOT NULL DEFAULT (''),
	         [Valeur] [varchar](200) NOT NULL DEFAULT (''),
	         [ID_DateTime] [datetime] NOT NULL	DEFAULT (getdate())
        ) ON [PRIMARY]
     End

--Example Param --> ClientTableLastExportDate = '01/01/2010 15:26:36'

if  exists(select so.name FROM sysobjects so where so.name = 'Client')
	begin
	drop table Client
	end
	
CREATE TABLE [dbo].[Client](
	[ClientId] int NOT NULL DEFAULT(''),
	[ClientGroupe] varchar(50) ,
	[ClientAppellation] varchar(50) NULL DEFAULT(''),	
	[ClientNom]varchar(50)  NULL DEFAULT(''),
	[ClientPrenom]varchar(50)  NULL DEFAULT(''),
	[ClientAdresse]varchar(max)  NULL DEFAULT(''),
	[ClientCP]varchar(50)  NULL DEFAULT(''),	
	[ClientVille]varchar(50)  NULL DEFAULT(''),	
	[ClientRegion]varchar(50)  NULL DEFAULT(''),
	[ClientPays]varchar(50)  NULL DEFAULT(''),		
	[ClientTelephone]varchar(50)  NULL DEFAULT(''),
	[ClientEmail]varchar(50)  NULL DEFAULT(''),
	[ClientDateNaissance]datetime  NULL DEFAULT(''),
	[ClientSupprime]varchar(2) NOT NULL DEFAULT(''),
	[ClientCommentaire]varchar(50)  NULL DEFAULT(''),
	[ClientInterlocuteur]varchar(50)  NULL DEFAULT(''),
	[StructureId]int NOT NULL DEFAULT('')
		 
) ON [PRIMARY]


SELECT  @ClientTableLastExportDate =convert(datetime, valeur, 120) FROM DataxplorerParamsApi where cle LIKE 'ClientTableLastExportDate' AND partnerId = @ppartnerId
SET @ClientTableLastExportDate = Isnull(@ClientTableLastExportDate,convert(datetime, '01/01/1900 00:00:00', 120))

DECLARE @sincedate datetime
IF (@pStartDate = cast('1753-1-1' as datetime))
begin
	SET @sinceDate = @ClientTableLastExportDate	                
END
ELSE
BEGIN
	SET @sinceDate = @pStartDate	                
END

INSERT INTO Client
	   SELECT 
		i.identite_id, 
		ig.identite_groupe_nom,
		ga.appellation_nom,
		i.identite_nom,
		i.identite_prenom,
	   ltrim(rtrim(IsNull(replace(replace(i.postal_rue1,'chr(10)',' '),'chr(13)',' '),'') + ' ' +  IsNull(replace(replace(i.postal_rue2,'chr(10)',' '),'chr(13)',' '),'') + ' ' + IsNull(replace(replace(i.postal_rue3,'chr(10)',' '),'chr(13)',' '),'') + ' ' + IsNull(replace(replace(i.postal_rue4,'chr(10)',' '),'chr(13)',' '),''))),
	   ltrim(rtrim(i.postal_cp)),
	   ltrim(rtrim(i.postal_ville)),
	   ltrim(rtrim(i.postal_region)),
	   ltrim(rtrim(i.postal_pays)),
	   CASE WHEN i.postal_tel1 like '%[0-9]%' Then i.postal_tel1
	   ELSE CASE  When i.postal_tel2 like '%[0-9]%' Then i.postal_tel2
	   ELSE CASE  When i.postal_tel3 like '%[0-9]%' Then i.postal_tel3
	   ELSE CASE  When i.postal_tel4 like '%[0-9]%' Then i.postal_tel4
	   ELSE CASE  When i.postal_tel5 like '%[0-9]%' Then i.postal_tel5
	   ELSE CASE  When i.postal_tel6 like '%[0-9]%' Then i.postal_tel6
	   ELSE CASE  When i.postal_tel7 like '%[0-9]%' Then i.postal_tel7
	   ELSE ' ' end end end end end end end,
	   CASE  When i.postal_tel5 like '%[@]%' Then replace(replace(i.postal_tel5,'chr(10)',' '),'chr(13)',' ')
	   ELSE Case when i.postal_tel6  like '%[@]%' Then replace(replace(i.postal_tel6,'chr(10)',' '),'chr(13)',' ')
	   ELSE Case when i.postal_tel7 like '%[@]%' Then replace(replace(i.postal_tel7,'chr(10)',' '),'chr(13)',' ')
	   ELSE '' end end end,
	   i.identite_date_naissance,
	   i.FicheSupprimer,
	   '', --Isnull(ic.commentaire,''),
	   i.identite_complement,
	   @NumStructure                   
	   FROM identite i 
	   LEFT OUTER JOIN global_appellation ga on ga.appellation_id=i.appellation_id
	   LEFT OUTER JOIN Identite_complement ic on ic.identite_id = i.identite_id
	   LEFT OUTER JOIN identite_groupe ig on ig.identite_groupe_id = i.identite_groupe_id
	   --Un Comment if modification only required
	   WHERE i.identite_date_modification > @sinceDate 
	   and i.identite_date_modification < @pEndDate 


--Un Comment if modification only required

SELECT  @iCounter =count(*)  FROM  DataxplorerParamsApi WHERE cle = 'ClientTableLastExportDate'  and partnerId = @ppartnerId
IF @iCounter>0
UPDATE DataxplorerParamsApi SET Valeur =getdate() ,ID_DateTime =getdate() WHERE  cle = 'ClientTableLastExportDate' and partnerId = @ppartnerId
ELSE
INSERT INTO DataxplorerParamsApi (partnerId, Cle,Valeur,ID_DateTime) values (@ppartnerId, 'ClientTableLastExportDate', getdate(),getdate())

SELECT * FROM Client
---------------------------------------------------------------------------------------------

--Remove Temp Objects

IF EXISTS(select so.name FROM sysobjects so where so.name = 'Client')
begin
	drop table Client
end