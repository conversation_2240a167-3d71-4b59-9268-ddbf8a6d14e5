  --DECLARE @pFieldGlobalId INT = 3079

  SELECT fgt.id ,
		fgt.fieldCode,
		fgt.txt_de AS TxtDE,
		fgt.txt_en AS TxtEN,
		fgt.txt_es AS TxtES,
		fgt.txt_fr AS TxtFR,
		fgt.txt_it AS TxtIT,
		fgt.txt_nl AS TxtNL,
		fgt.txt_pt AS TxtPT,
		tv.Id, 
		tv.Name, 
		tv.Description, 
		tv.IsAutoCloseTag
  FROM translate_fieldsGlobalTranslation fgt
  LEFT OUTER JOIN translate_fieldsVariables tfv ON tfv.fieldId = fgt.id 
  LEFT OUTER JOIN translate_variables tv ON tv.Id = tfv.variableId
  WHERE fgt.id = @pFieldGlobalId