
/************* Retourne le nombre restant (nb_max par tarif - le nombre dans l'histo pour ce tarif) *******************
declare @pSponsorReference varchar(max)  ='31D53A8D8822491BC5A70B653C1B7AAE654668D73AC0D0F79CF2C4E481F85FB9'
declare @pSessionId int = 22
declare @pGestionPlaceId int = 55134
*/

declare @existTable  int = (SELECT  count(*) FROM sys.tables WHERE name ='sponsor_entrees')
if(@existTable > 0)
BEGIN
   declare @sponsorMode varchar(max) 
   select @sponsorMode = preference_valeur from structure_prefs where preference_cle = 'SPONSOR_MODE' 
   
   --Détermine la manifestation et le tarif grâce à la gestion_place_id
   declare @tarifId int
   declare @manifId int
   select @tarifId = type_tarif_id, @manifId = manif_id from gestion_place where gestion_place_id = @pGestionPlaceId
   
   --nb max par manif sur ce tarif
   declare @nbMaxOfThisPriceId int = 0
   select @nbMaxOfThisPriceId = preference_valeur from structure_prefs where preference_cle = 'SPONSOR_MAX_'+ convert(varchar(max), @tarifId)

   declare @dossierId int = 0
   declare @entreeId int = 0
   declare @eventId int = 0
   declare @restant int = 0 -- Nombre restant pour la carte (nb max - nb histo)

   /*** Mode du sponsor à la manifestation ****/
   if LOWER(@sponsorMode) = 'event'
   BEGIN
      --retourne une liste de ce qui est dans l'historique pour ce sponsor
      select @eventId= manifestation_id, @dossierId = se.dossier_id, @entreeId= se.entree_id 
      from sponsor_entrees se
      inner join seance s on s.seance_id = se.seance_id
      inner join gestion_place gp on gp.gestion_place_id = @pGestionPlaceId
      where s.seance_id in (select seance_id from seance where manifestation_id = gp.manif_id) and
      se.reference_sponsor = @pSponsorReference
   END

   /*** Mode du sponsor à la séance ****/
   if LOWER(@sponsorMode) = 'session'
   BEGIN
      --retourne une liste de ce qui est dans l'historique pour ce sponsor
      select @eventId = manifestation_id, @dossierId=se.dossier_id, @entreeId= se.entree_id from sponsor_entrees se
      inner join seance s on s.seance_id = se.seance_id
      where s.seance_id in (@pSessionId) and se.reference_sponsor = @pSponsorReference
   END

   if @dossierId > 0  and @entreeId > 0 
   BEGIN      
        declare @SQL nvarchar(max) = ''
        SET @SQL = 'select @outputFromExec = count(*) from entree_' + convert(varchar,@eventId) + ' where dossier_id =  '+ convert(varchar,@dossierId)+  ' and entree_etat <> ''L'' '
        declare @nbEntree int
        exec sp_executesql @SQL, N'@outputFromExec int out', @nbEntree out
        

        set @restant = (@nbMaxOfThisPriceId - @nbEntree)

        select @restant as restant
   END

   IF @dossierId = 0  and @entreeId =  0 
   BEGIN
      select @nbMaxOfThisPriceId
   END

END


