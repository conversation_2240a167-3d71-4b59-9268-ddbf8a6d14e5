﻿


	select * from (

SELECT (SELECT max(formule_id) from Commande_Ligne clc where clc.Commande_id = c.commande_id
) as formule_id, cmdl.abo_id,c.commande_id, c.identite_id,i.identite_nom, i.identite_prenom,
	i.postal_tel1,i.postal_tel2,i.postal_tel3,i.postal_tel4,i.postal_tel5,i.postal_tel6,i.postal_tel7,
	(SELECT max(filiere_id) from Commande_Ligne_comp clc where clc.Commande_id = c.commande_id) as filiere_Id
FROM commande c
INNER JOIN identite i ON c.identite_id = i.identite_id
inner join commande_ligne cmdl ON cmdl.commande_id=c.commande_id 
WHERE c.commande_id IN ([ListOrders])
) s group by formule_id, abo_id, commande_id, identite_id, identite_nom, identite_prenom, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7, filiere_Id

