﻿declare @langCode varchar(5)
set @langCode=@plangCode
declare @langId int
set @langId =0;
select @langId = langue_id FROM langue WHERE langue_code=@langCode

select min(totalAmount) as minAmount, c.categ_nom, c.categ_id, c.categ_code
from (

SELECT 
	TotalAmount= 100*(vts.vts_grille1+vts.vts_grille2+case when modecol4='REMISE' then - vts.vts_grille4  
	when modecol4='TAXE' or modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  
	when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  
	when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  
	when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  
	when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  
	when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  
	when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then vts.vts_grille10 else 0 END)
	, cat.categ_id, cat.categ_nom
	FROM valeur_tarif_stock[eventID] vts
	INNER JOIN structure st ON st.structure_id >0

	INNER JOIN categorie cat ON cat.categ_id = vts.categ_id
	LEFT JOIN traduction_categorie tcat ON tcat.categ_id = vts.categ_id and tcat.langue_id = @langId

	INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
	LEFT JOIN traduction_type_tarif ttt ON  ttt.type_tarif_id = vts.type_tarif_id and ttt.langue_id = @langId

	inner join filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id 
	INNER JOIN operateur_droit od ON od.droit_table_id = tt.type_tarif_id 

	INNER JOIN seance s ON s.seance_Id = vts.seance_id

	WHERE fd.filiere_id in ({listfilieresId}) and valeur='O'
	AND od.operateur_id = ({listoperateursId}) AND od.droit_valeur =1 and droit_table ='TYPE_TARIF'

	AND vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2
						WHERE vts2.tarif_logique_id=vts.tarif_logique_id
						and vts2.seance_id=vts.seance_id
						and vts2.categ_id= vts.categ_id
						and vts2.type_tarif_id= vts.type_tarif_id
			) and vts.vts_grille1>=0
			and tt.type_tarif_id in ({listtarifsref})

		) s 
		INNER JOIN categorie c on s.categ_id = c.categ_id
		where s.TotalAmount>0
		group by c.categ_nom, pref_affichage, c.categ_id, c.categ_code
		order by pref_affichage 
