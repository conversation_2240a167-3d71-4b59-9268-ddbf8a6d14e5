﻿declare @seanceId int = @psessionId


if (@seanceId <> 0)
begin
	select 
	Sean<PERSON>_ID As sessionId, 
	codebarre AS barcode,
	numbillet as ticketnumber,
	datecontrole as controlDate,
	statutRetour as returnCode,
	machine as ipScan,
	case  
	when statutRetour = 1 then 'OK'
	when statutRetour = -12 then 'ALREADYSCANNED'
	when statutRetour = -14 then 'NOTFOUND'
	when statutRetour in (-1, -2, -4) then 'SESSIONINVALID'
	when statutRetour = -6 then 'CANCELED'
	when statutRetour = -7 then 'DUPLIC'
	when statutRetour = -8 then 'SESSIONINCORRECT'
	end as returnText

	--,* 
	from Controle_Acces 
	where Seance_ID = @seanceId
	and DATECONTROLE > @pdateFrom and DATECONTROLE < @pdateTo
	order by ID desc

end
else
begin


		if datediff(HOUR, @pdateFrom, @pdateTo)> 24
			select 'error', 'dates interval > 24h'	
		else
		begin
			select 
			Sean<PERSON>_ID As sessionId, 
			codebarre AS barcode,
			numbillet as ticketnumber,
			datecontrole as controlDate,
			statutRetour as returnCode,
			machine as ipScan,
			case  
			when statutRetour = 1 then 'OK'
			when statutRetour = -12 then 'ALREADYSCANNED'
			when statutRetour = -14 then 'NOTFOUND'
			when statutRetour in (-1, -2, -4) then 'SESSIONINVALID'
			when statutRetour = -6 then 'CANCELED'
			when statutRetour = -7 then 'DUPLIC'
				when statutRetour = -8 then 'SESSIONINCORRECT'
			end as returnText

			--,* 
			from Controle_Acces 
			--where Seance_ID = @seanceId
			WHERE DATECONTROLE > @pdateFrom and DATECONTROLE < @pdateTo
			order by ID desc
		end
	

end