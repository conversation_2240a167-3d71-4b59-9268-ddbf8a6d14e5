﻿
/* dispo par séance / categ pour les reserves auquelles à droit l'operateur */

/* Attention, reservesOfOperateur.sql doit être appellé avant, pour remplir la table @myreserves = la table des reserves auquelles à droit l'operateur */

DECLARE @mycategs[eventID] table (seance_id int, categ_id int)

INSERT into @mycategs[eventID] 
SELECT DISTINCT vts.seance_id, vts.categ_id from valeur_tarif_stock[eventID] vts 
INNER JOIN type_tarif tt on tt.type_tarif_id = vts.type_tarif_id
INNER JOIN filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id 
INNER JOIN operateur_droit od ON od.droit_table_id = tt.type_tarif_id 
where vts_v =(SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					and vts2.seance_id=vts.seance_id
					and vts2.categ_id= vts.categ_id
					and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0
and filiere_id in ({listfilieresId}) and od.operateur_id in ({listoperateursId}) AND od.droit_valeur =1 and droit_table ='TYPE_TARIF'  and valeur='O'

SELECT e.seance_id as sessionId, [eventID] as eventId, categorie_id as categoryId, rlp.zone_id as zoneId, rlp.etage_id as floorId, rlp.section_id as sectionId, count(*) as dispo,
	CASE s.NEPASAFFICHERDATE 
	WHEN 'O' THEN 0
	WHEN 'N' THEN 1
	WHEN '' THEN 1
	WHEN NULL THEN 1
	END  as afficherLaDate
FROM entree_[eventID] e 
	INNER JOIN seance s on s.seance_id = e.seance_id
	INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id
	INNER JOIN @myreserves r on e.reserve_id = r.reserve_id
	INNER JOIN categorie cat ON e.categorie_id = cat.categ_id
	INNER JOIN @mycategs[eventID] mycats on mycats.categ_id = cat.categ_id and mycats.seance_id = s.seance_id
WHERE entree_etat='L' and (flag_selection ='' or flag_selection is null) and alotissement_id =0 and contingent_id=0	
	GROUP BY e.seance_id, categorie_id, rlp.zone_id, rlp.etage_id, rlp.section_id, s.NEPASAFFICHERDATE
	ORDER BY e.seance_id, categorie_id, rlp.zone_id, rlp.etage_id, rlp.section_id

DELETE @mycategs[eventID]