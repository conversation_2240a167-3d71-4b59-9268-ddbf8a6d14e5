
--declare @plangCode varchar(2) = 'fr'

/************* Get Produit Assurance */

DECLARE @groupAssurance int = 7

DECLARE @LgId int


SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT p.produit_id as ProductID, 
CONVERT(bit,CASE isnull(p_i.produit_id,0) WHEN 0 THEN 0 ELSE 1 END) as bLierProduit,
CASE WHEN tp.produit_nom IS null THEN p.produit_nom else tp.produit_nom END AS productName  , 

CONVERT(INTEGER,(montant1 + montant2)*100) as TotalAmount ,CONVERT(INTEGER,montant2 * 100)  as Charge,
isnull(tva.tva_libelle,'') as tva_libelle , isnull(tva.tva_taux,0) as tva_taux,
convert(int,ps.seance_id) as SessionID, 
convert(int,ps.manifestation_id) as EventID, 
p.produit_type
,p.type_montant
,p.pref_affichage, 1 as nbr
,ps.restant as restant
,ps.jauge as jauge,
'ASS' 
as Groupe_Code,
isnull( p_i.acces_autonome, 0) as acces_autonome, isnull(p_i.site_vente, 0) as site_vente,
 date_deb_validite, date_fin_validite, p.Groupe_id,
 p.produit_descrip, pdl.descriptif_long_text
FROM produit p
left join produit_internet p_i on p_i.produit_id  =  p.produit_id
LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LgId
LEFT OUTER JOIN tva ON  tva.tva_id = p.tva1
INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
LEFT OUTER JOIN produit_descriptif_long  pdl ON pdl.produit_ID = p.produit_id
where p.Groupe_id= @groupAssurance and restant>0