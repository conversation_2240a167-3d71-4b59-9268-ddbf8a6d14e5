

/*

declare @plangCode varchar(2) = 'fR'


*/


DECLARE @langue_Id int = 0
SELECT @langue_Id = langue_id FROM langue WHERE  upper(langue_code) = upper(@plangcode);
IF @langue_Id is null
	set @langue_Id = 0


DECLARE @tmpZapper TABLE (zapperetage INT NOT NULL,zapperZES INT NOT NULL, eventId INT NOT NULL); 

INSERT into @tmpZapper (zapperetage, zapperZES, eventId)   
select  sum(zapperetage) as zapperetage, sum(zapperzes) as zapper<PERSON><PERSON>, manifestation_id  from ( 
	select count(*) as zapperetage, '' as zapper<PERSON><PERSON>, pm.manifestation_id from proprietes_of_manifs pm, proprietes_references_of_manifs pref 
	where pref.propriete_ref_id = pm.propriete_ref_id and valeur = '1'
	and pm.manifestation_id in ({eventids})
	and pref.code = 'ZapperEtag'
	group by manifestation_id
 union  
	select  '' as zapperetage, count(*) as zapper<PERSON><PERSON>,  pm.manifestation_id from proprietes_of_manifs pm, proprietes_references_of_manifs pref 
	where pref.propriete_ref_id = pm.propriete_ref_id and valeur = '1'
	and pm.manifestation_id in ({eventids}) 
	and pref.code = 'ZapperZES' 
	group by manifestation_id
) as zapperresult 
group by manifestation_id 

SELECT manif.manifestation_id as EventID, 
	manif.manifestation_id as manifestation_id, 	
	isnull(trad_manif.manifestation_nom, manif.manifestation_nom) as EventName,
	isnull(trad_manif.manifestation_nom, manif.manifestation_nom) as Manif_nom,
	isnull(trad_manif.manifestation_descrip, manif.manifestation_descrip) as EventDescription1,
	isnull(trad_manif.manifestation_descrip, manif.manifestation_descrip) as Manif_info_1,
	
	isnull(trad_manif.manifestation_descrip2, manif.manifestation_descrip2) as EventDescription2,
	isnull(trad_manif.manifestation_descrip2, manif.manifestation_descrip2) as Manif_info_2,
	isnull(trad_manif.manifestation_descrip3, manif.manifestation_descrip3) as EventDescription3,
	isnull(trad_manif.manifestation_descrip3, manif.manifestation_descrip3) as Manif_info_3,
	isnull(trad_manif.manifestation_descrip4, manif.manifestation_descrip4) as EventDescription4,
	isnull(trad_manif.manifestation_descrip4, manif.manifestation_descrip4) as Manif_info_4,
	isnull(trad_manif.manifestation_descrip5, manif.manifestation_descrip5) as EventDescription5,
	isnull(trad_manif.manifestation_descrip5, manif.manifestation_descrip5) as Manif_info_5,
	
	ISNULL(zapperetage, 0) as zapperetage,ISNULL(zapperZES ,0) as zapperZES,
	ISNULL(manif_groupe_code,'') as GRPMAN_CODE, ISNULL(manif_groupe_nom,'') as GRPMAN_NOM,
	ISNULL([producteur_nom],'') as NOM_PROD,ISNULL([producteur_code],'')  as CODE_PROD,
	LIC_PROD = CASE manif.Licence_id
		WHEN 1 THEN [num_licence1] WHEN 2 THEN [num_licence2] WHEN 3 THEN [num_licence3]
		WHEN 4 THEN [num_licence4] WHEN 5 THEN [num_licence5]  ELSE [num_licence1] 
	END
	,manif.manifestation_descrip3 as TITRE_FILM_CNC
	,disci.nom as CLASSEMENT_CNC
	
FROM manifestation manif
LEFT JOIN traduction_manifestation trad_manif on manif.manifestation_id = trad_manif.manifestation_id and trad_manif.langue_id = @langue_Id
LEFT OUTER JOIN @tmpZapper tmpzes on tmpzes.eventId = manif.manifestation_id
LEFT OUTER JOIN producteur p on p.producteur_id= manif.producteur_id
LEFT OUTER JOIN manifestation_groupe mg on mg.manif_groupe_id= manif.manifestation_groupe_id
LEFT OUTER JOIN Manif_Competences mcomp on mcomp.manifestation_id= manif.manifestation_id
LEFT OUTER JOIN discipline disci on mcomp.discipline_id= disci.id
  
 WHERE manif.manifestation_id in ({eventids})