/*
declare @pLangCode varchar(50) = 'fr'
declare @pManifestationGenreId int = 2 --sous Genre 
*/
IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NULL 
BEGIN
    
   
    select *
    from manifestation_groupe_genre mgg
    left outer join manifestation_genre mg on mgg.id = mg.groupe_id
    ORDER by mg.code
END


IF OBJECT_ID('dbo.traduction_manifestation_genre') IS  NOT NULL 
BEGIN

    DECLARE @langue_id int = (select langue_id from langue where langue_code = @pLangCode)

    
    -- select *    
    --from manifestation_groupe_genre mgg
    --left outer join manifestation_genre mg on mgg.id = mg.groupe_id
    --LEFT JOIN traduction_manifestation_genre trad_manif_genre on mg.id = trad_manif_genre.id and trad_manif_genre.langue_id = @langue_id
    --where mg.id = @pManifestationGenreId

    
  IF @pManifestationGenreId  = 0
	BEGIN
		select *
		from manifestation_genre mg
		LEFT JOIN traduction_manifestation_genre trad_manif_genre on mg.id  = trad_manif_genre.id and trad_manif_genre.langue_id = @langue_id
		where mg.groupe_id = @pManifestationGenreId
	END 
	ELSE
	BEGIN
		select *
		from manifestation_genre mg
		LEFT JOIN traduction_manifestation_genre trad_manif_genre on mg.id  = trad_manif_genre.id and trad_manif_genre.langue_id = @langue_id
		where mg.id = @pManifestationGenreId
	END

END

