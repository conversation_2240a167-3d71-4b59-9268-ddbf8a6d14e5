
/*


declare @pIdentiteId int
set @pIdentiteId = 878


*/
/*
select * from Adhesion_Adherent  aa
inner join Adhesion_Adherent_Catalog aac on aac.Adhesion_Adherent_ID = aa.Adhesion_Adherent_ID
where Adhesion_DateFin > GETDATE()
and Actif=1
and aa.Identite_id = @pIdentiteId
*/


SELECT aac.Adhesion_Adherent_Catalog_ID
   , aa.Adhesion_Adherent_id
   , aa.identite_id 
   , i.identite_nomprenom
   , aac.Adhesion_Catalog_ID
   , ac.Catalog_Libelle
   , isnull(p.produit_id,0) as produit_id
   , isnull(p.produit_nom,'') as produit_nom
  , convert(int, isnull(dp.dos_prod_montant,0)*100) as montant
   , aac.Adhesion_Date_Creation
   , aac.adhesion_datedeb
   , aac.adhesion_datefin
   , aa.ADHERENT_ID
   , case when adhesion_datefin < getdate() then 0 else aac.actif end as Actif --- si la date est passée donc actif = 0
   , dp.dos_prod_Etat
   , dp.produit_id
   , dp.commande_id
   , aac.internet
FROM Adhesion_Adherent AS aa
INNER JOIN Adhesion_Adherent_Catalog AS aac 
   ON aac.adhesion_adherent_id = aa.adhesion_adherent_id
INNER JOIN  Adhesion_Catalog AS ac
   ON ac.Adhesion_Catalog_ID = aac.Adhesion_Catalog_ID
INNER JOIN Identite AS i
   ON i.identite_id = aa.identite_id 
LEFT OUTER JOIN Adhesion_Dossier_Produit AS adp
   ON adp.Adhesion_Adherent_Catalog_ID = aac.Adhesion_Adherent_Catalog_ID
LEFT OUTER JOIN Dossier_produit AS dp
   ON dp.dos_prod_id = adp.dos_prod_id
LEFT OUTER JOIN Produit AS p
   ON p.produit_id = dp.produit_id 
WHERE aa.Identite_id = @pIdentiteId
ORDER BY aac.adhesion_datefin, aac.Adhesion_Date_Creation 