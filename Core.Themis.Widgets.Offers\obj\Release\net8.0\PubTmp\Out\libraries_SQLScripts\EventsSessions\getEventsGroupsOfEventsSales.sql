/************** Liste des groupes de manifestations sur les manifestations acutellement en vente (règle de vente valide) ***************/
/*
declare @pLangCode varchar(max)
set @pLangCode='fr'
*/

declare @langCode varchar(max)
set @langCode=@plangCode


DECLARE @langue_id int = (select langue_id from langue where langue_code = @langCode)


select distinct 
isnull(trad_manif_group.manif_groupe_id, mg.manif_groupe_id) as manif_groupe_id,
isnull(trad_manif_group.manif_groupe_nom, mg.manif_groupe_nom) as manif_groupe_nom,
isnull(trad_manif_group.manif_groupe_code, mg.manif_groupe_code) as manif_groupe_code
from manifestation_groupe mg
LEFT JOIN traduction_manifestation_groupe trad_manif_group on mg.manif_groupe_id = trad_manif_group.manif_groupe_id 
	and trad_manif_group.langue_id = @langue_id
inner join manifestation m on m.manifestation_groupe_id = mg.manif_groupe_id
inner join gestion_place gp on gp.manif_id = m.manifestation_id 
where m.supprimer <> 'O' and mg.supprimer  <> 'O' and gp.isvalide = 1
order by manif_groupe_nom

