/*
declare @pLangCode varchar(max) = 'FR'
declare @pEventId int = 1

*/


declare @langueId int = (select langue_id from langue where langue_code = @pLangCode)



select * from manifestation m
inner join seance s on s.manifestation_id = m.manifestation_id 
left outer join gestion_place gp on gp.manif_id = m.manifestation_id and s.seance_id = gp.seance_id 
left outer join traduction_manifestation tm on tm.manifestation_id = m.manifestation_id and tm.langue_id = @langueId
where m.manifestation_id = @pEventId and m.supprimer = 'N' 


