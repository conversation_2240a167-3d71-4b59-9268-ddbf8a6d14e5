﻿--DECLARE @pIdentiteId INT = 898
--DECLARE @pSkipElement INT = 4
--DECLARE @pTakeElement INT = 2

DECLARE @SqlForDossier VARCHAR(4000), @SqlForDossierSvg VARCHAR(4000), @SqlForEntree VARCHAR(4000), @SqlForEntreeSvg VARCHAR(4000)
DECLARE @dossierId INT, @manifId INT

DECLARE @TotalCommandeCount INT = 
(SELECT COUNT(DISTINCT c.commande_id) 
FROM commande c 
INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
WHERE  cl.type_ligne ='DOS' 
AND (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId));

/*Définission des infos de commandes pour le fetch*/
DECLARE CommandeLigneInfos CURSOR SCROLL FOR 

	SELECT cl.dossier_id, cl.manifestation_id
	FROM 
	(
		SELECT DISTINCT c.*
		FROM commande_ligne cl 
		INNER JOIN commande c ON cl.commande_id = c.commande_id
		WHERE  cl.type_ligne ='DOS' 
		AND (cl.identite_id = @pIdentiteId OR c.identite_id = @pIdentiteId)
		ORDER BY c.commande_id OFFSET @pSkipElement ROWS FETCH NEXT @pTakeElement ROWS ONLY
	) c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	WHERE cl.manifestation_id <> 0;

OPEN CommandeLigneInfos; 
FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId;

/*Création et remplissage de la table #Dossier*/
CREATE TABLE #Dossier (
	manifestation_id_dossier INT,
	/*type_montant représente le modeCol de la table structure (ex: TAXE, COMMISSION...)*/
	type_montant4 CHAR(15),
	type_montant5 CHAR(15),
	type_montant6 CHAR(15),
	type_montant7 CHAR(15),
	type_montant8 CHAR(15),
	type_montant9 CHAR(15),
	type_montant10 CHAR(15),
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat CHAR(5),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer DECIMAL(18,10),
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	operation_id INT,
	filiere_id INT
)

/*Création et remplissage de la table #DossierSvg*/
CREATE TABLE #DossierSvg (
	manifestation_id_dossier_svg INT,
	dossier_id INT,
	identite_id INT, 
	commande_id INT,
	abon_manif_id INT,
	seance_id INT, 
	dossier_v INT,
	operateur_id INT,
	dossier_montant DECIMAL(18,10),
	dossier_montant1 DECIMAL(18,10),
	dossier_montant2 DECIMAL(18,10),
	dossier_montant3 DECIMAL(18,10),
	dossier_montant4 DECIMAL(18,10),
	dossier_montant5 DECIMAL(18,10),
	dossier_montant6 DECIMAL(18,10),
	dossier_montant7 DECIMAL(18,10),
	dossier_montant8 DECIMAL(18,10),
	dossier_montant9 DECIMAL(18,10),
	dossier_montant10 DECIMAL(18,10),
	dossier_c CHAR(50),
	dossier_etat CHAR(5),
	dossier_icone INT,
	dossier_numero INT,
	dossier_nbplace INT,
	dossier_montantpayer INT,
	dossier_facture INT,
	dossier_client_nom CHAR(40),
	num_paiement INT,
	date_operation DATETIME,
	type_operation CHAR(10),
	filiere_id INT
)

/*Création et remplissage de la table #Entree*/
CREATE TABLE #Entree (
	manifestation_id_entree INT,
	/*type_montant représente le modeCol de la table structure (ex: TAXE, COMMISSION...)*/
	type_montant4 CHAR(15),
	type_montant5 CHAR(15),
	type_montant6 CHAR(15),
	type_montant7 CHAR(15),
	type_montant8 CHAR(15),
	type_montant9 CHAR(15),
	type_montant10 CHAR(15),
	entree_id INT,
	seance_id INT, 
	dossier_id INT,
	entree_etat CHAR(1),
	categorie_id INT, 
	type_tarif_id INT,
	valeur_tarif_stock_id INT,
	ValeurTarifStockVersion INT,
	numero_billet DECIMAL(19,0),
	reference_unique_physique_id INT,
	reference_unique_logique_id INT,
	lieu_configuration_id INT,
	alotissement_id INT,
	reserve_id INT,
	contingent_id INT,
	derniere_modif DATETIME,
	code_anu CHAR(20) NULL,
	iindex INT,
	flag_section CHAR(10) NULL,
	montant1 DECIMAL(18,10) NULL,
	montant2 DECIMAL(18,10) NULL,
	montant3 DECIMAL(18,10) NULL,
	montant4 DECIMAL(18,10) NULL,
	montant5 DECIMAL(18,10) NULL,
	montant6 DECIMAL(18,10) NULL,
	montant7 DECIMAL(18,10) NULL,
	montant8 DECIMAL(18,10) NULL,
	montant9 DECIMAL(18,10) NULL,
	montant10 DECIMAL(18,10) NULL,
	dateoperation DATETIME,
	controleacces VARCHAR(50) NULL,
)

/*Création et remplissage de la table #EntreeSvg*/
CREATE TABLE #EntreeSvg (
	manifestation_id_entree_svg INT,
	entree_id INT,
	seance_id INT, 
	dossier_id INT,
	entree_etat CHAR(1),
	categorie_id INT, 
	type_tarif_id INT,
	valeur_tarif_stock_id INT,
	ValeurTarifStockVersion INT,
	numero_billet DECIMAL(19,0),
	alotissement_id INT,
	reserve_id INT,
	contingent_id INT,
	montant1 DECIMAL(18,10) NULL,
	montant2 DECIMAL(18,10) NULL,
	montant3 DECIMAL(18,10) NULL,
	montant4 DECIMAL(18,10) NULL,
	montant5 DECIMAL(18,10) NULL,
	montant6 DECIMAL(18,10) NULL,
	montant7 DECIMAL(18,10) NULL,
	montant8 DECIMAL(18,10) NULL,
	montant9 DECIMAL(18,10) NULL,
	montant10 DECIMAL(18,10) NULL,
	dateoperation DATETIME,
	dossier_v INT,
)

/*remplissage des tables temporaire*/
WHILE @@FETCH_STATUS=0 
BEGIN 
	SET @SqlForDossier = 
	'INSERT INTO #Dossier 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier, 
		s.ModeCol4 as type_montant4,
		s.ModeCol5 as type_montant5,
		s.ModeCol6 as type_montant6,
		s.ModeCol7 as type_montant7,
		s.ModeCol8 as type_montant8,
		s.ModeCol9 as type_montant9,
		s.ModeCol10 as type_montant10,
		d.*
		FROM dossier_' + LTRIM(STR(@manifId)) + ' d, structure s
		WHERE d.dossier_id = ' + LTRIM(STR(@dossierId))

	SET @SqlForDossierSvg = 
	'INSERT INTO #DossierSvg 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_dossier_svg, * 
		FROM dossiersvg_' + LTRIM(STR(@manifId)) + ' dsvg
		WHERE dsvg.dossier_id = ' + LTRIM(STR(@dossierId))

	SET @SqlForEntree = 
	'INSERT INTO #Entree 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_entree, 
		s.ModeCol4 as type_montant4,
		s.ModeCol5 as type_montant5,
		s.ModeCol6 as type_montant6,
		s.ModeCol7 as type_montant7,
		s.ModeCol8 as type_montant8,
		s.ModeCol9 as type_montant9,
		s.ModeCol10 as type_montant10,
		e.*
		FROM entree_' + LTRIM(STR(@manifId)) + ' e, structure s
		WHERE e.dossier_id = ' + LTRIM(STR(@dossierId))

	SET @SqlForEntreeSvg = 
	'INSERT INTO #EntreeSvg 
		SELECT ' + LTRIM(STR(@manifId)) + ' as manifestation_id_entree_svg, *
		FROM entreesvg_' + LTRIM(STR(@manifId)) + ' esvg
		WHERE esvg.dossier_id = ' + LTRIM(STR(@dossierId))

	PRINT(@SqlForDossier)
	PRINT(@SqlForDossierSvg)
	PRINT(@SqlForEntree)
	PRINT(@SqlForEntreeSvg)

	EXEC(@SqlForDossier)
	EXEC(@SqlForDossierSvg)
	EXEC(@SqlForEntree)
	EXEC(@SqlForEntreeSvg)


FETCH NEXT FROM CommandeLigneInfos INTO @dossierId, @manifId; 

END

CLOSE CommandeLigneInfos; 
DEALLOCATE CommandeLigneInfos; 

/*Affigage des tables temporaires*/
--SELECT * FROM #Dossier
--SELECT * FROM #DossierSvg
--SELECT * FROM #Entree
--SELECT * FROM #EntreeSvg


SELECT 1 as use_generic_entity, @TotalCommandeCount as total_items, *
FROM commande c 
INNER JOIN identite i ON c.identite_id = i.identite_id
LEFT JOIN commande_infos ci ON c.commande_id = ci.commande_id
INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
INNER JOIN identite icl ON cl.identite_id = icl.identite_id
LEFT JOIN Commande_Ligne_comp clc ON cl.commande_ligne_id = clc.commande_ligne_id
INNER JOIN #Dossier d 
	ON cl.dossier_id = d.dossier_id
	AND cl.manifestation_id = d.manifestation_id_dossier
INNER JOIN #DossierSvg dsvg 
	ON d.dossier_id = dsvg.dossier_id
	AND d.manifestation_id_dossier = dsvg.manifestation_id_dossier_svg
LEFT JOIN #Entree e 
	ON e.dossier_id = d.dossier_id
	AND e.seance_id = d.seance_id
	AND e.manifestation_id_entree = d.manifestation_id_dossier
LEFT JOIN #EntreeSvg esvg 
	ON e.entree_id = esvg.entree_id
	AND e.seance_id = esvg.seance_id
	AND e.manifestation_id_entree = esvg.manifestation_id_entree_svg
LEFT JOIN seance s ON e.seance_id = s.seance_Id
LEFT JOIN manifestation m ON s.manifestation_id = m.manifestation_id
LEFT JOIN dossier_produit dp ON c.commande_id = dp.commande_id
LEFT JOIN produit p ON dp.produit_id = p.produit_id
LEFT JOIN dossier_produit_reservation dpr ON dp.dos_prod_id = dpr.dos_prod_id

/*Suppression des tables temporaires*/
DROP TABLE #Dossier
DROP TABLE #DossierSvg
DROP TABLE #Entree
DROP TABLE #EntreeSvg